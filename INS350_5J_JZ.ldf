<?xml version="1.0" encoding="UTF-8"?>
<BaliProject version="3.2" title="INS350_5J_JZ" device="LFE5U-25F-7BG256C" default_implementation="impl1">
    <Options/>
    <Implementation title="impl1" dir="impl1" description="impl1" synthesis="synplify" default_strategy="Strategy1">
        <Options def_top="INS350_5J_JZ" top="INS350_5J_JZ"/>
        <Source name="Src_al/INS350_5J_JZ.v" type="Verilog" type_short="Verilog">
            <Options top_module="INS350_5J_JZ"/>
        </Source>
        <Source name="Src_al/Src_al/SignalProcessing.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/speed_select_Tx.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/SquareWaveGenerator.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/UART_Control.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/uart_tx.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/SignalGenerator.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/Ctrl_Data.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/DS18B20.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/Integration.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/Modulation.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Src_al/Rs422Output.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="Src_al/Demodulation.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="IP_al/global_clock1/global_clock/global_clock.v" type="Verilog" type_short="Verilog">
            <Options/>
        </Source>
        <Source name="impl1/impl1.xcf" type="Programming Project File" type_short="Programming">
            <Options/>
        </Source>
        <Source name="INS350_5J_JZ.lpf" type="Logic Preference" type_short="LPF">
            <Options/>
        </Source>
    </Implementation>
    <Strategy name="Strategy1" file="INS350_5J_JZ1.sty"/>
</BaliProject>
