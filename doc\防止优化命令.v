防止优化：
模块/* synthesis syn_noprune=1 *//* synthesis NOCLIP=1 */
wire /* synthesis syn_keep=1 */
reg /* synthesis syn_preserve=1 */  
wire /*synthesis syn keep=1 nomerge=""*/


Wire buf_delay /* synthesis syn_keep=1 NOMERGE=”ON” */;

module myreg ( datain, rst, clk, en, dout, cout ) /* synthesis syn_hier = “fixed” */;  不让软件优化多次例化的同一个模块，而且radiant后续不会优化。

module myreg ( datain, rst, clk, en, dout, cout )/* synthesis syn_noprune=1 *//* synthesis NOCLIP=1 */;不让软件优化因为没有输出信号到管脚而优化整个模块

约束算数逻辑是用DSP还是LUT，如果想全局就放在module下面
reg [8:0]   add_data   /*synthesis syn_multstyle = "block_mult | logic"*/;

设置最大扇出
reg d/* synthesis syn_maxfan=3 */;

数组综合成EBR实现
reg [DATA_WIDTH-1:0]     mem[(1<<ADDR_WIDTH)-1:0] /* synthesis syn_ramstyle="block_ram" */;