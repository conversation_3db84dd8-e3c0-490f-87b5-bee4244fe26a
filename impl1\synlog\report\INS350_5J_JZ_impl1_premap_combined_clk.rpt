

@S |Clock Optimization Summary



#### START OF PREMAP CLOCK OPTIMIZATION REPORT #####[

0 non-gated/non-generated clock tree(s) driving 0 clock pin(s) of sequential element(s)
3 gated/generated clock tree(s) driving 1097 clock pin(s) of sequential element(s)
0 instances converted, 1097 sequential instances remain driven by gated/generated clocks

============================================================================ Gated/Generated Clocks ============================================================================
Clock Tree ID     Driving Element            Drive Element Type     Unconverted Fanout     Sample Instance                            Explanation                               
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
@KP:ckid0_1       CLK120.PLLInst_0.CLKOP     EHXPLLL                855                    wendu.cnt[7:0]                             Clock source is invalid for GCC           
@KP:ckid0_4       CLK120.PLLInst_0.CLKOS     EHXPLLL                178                    signal_process.demodu.AD_validcnt[7:0]     Clock source is invalid for GCC           
@KP:ckid0_6       wendu.clk_us.Q[0]          dffre                  64                     wendu.cur_state[5]                         Derived clock on input (not legal for GCC)
================================================================================================================================================================================


##### END OF CLOCK OPTIMIZATION REPORT ######

