Starting process: 

Configuration data saved


SCUBA, Version Diamond (64-bit) 3.12.1.454
Thu Mar 13 16:52:22 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

BEGIN SCUBA Module Synthesis

    Issued command   : D:\Software\lscc\diamond\3.12\ispfpga\bin\nt64\scuba.exe -w -n global_clock -lang verilog -synth lse -bus_exp 7 -bb -arch sa5p00 -type pll -fin 20.00 -fclkop 120.00 -fclkop_tol 0.0 -fclkos 60.00 -fclkos_tol 0.0 -phases 0 -fclkos2 60.00 -fclkos2_tol 0.0 -phases2 0 -phase_cntl STATIC -rst -lock -sticky -fb_mode 1 
    Circuit name     : global_clock
    Module type      : pll
    Module Version   : 5.7
    Ports            : 
	Inputs       : CLKI, RST
	Outputs      : CLKOP, CLKOS, CLKOS2, LOCK
    I/O buffer       : not inserted
    EDIF output      : global_clock.edn
    Verilog output   : global_clock.v
    Verilog template : global_clock_tmpl.v
    Verilog purpose  : for synthesis and simulation
    Bus notation     : big endian
    Report output    : global_clock.srp
    Estimated Resource Usage:

END   SCUBA Module Synthesis

File: global_clock.lpc created.


End process: completed successfully.


Total Warnings:  0

Total Errors:  0


