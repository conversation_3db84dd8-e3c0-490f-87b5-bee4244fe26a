<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML><HEAD>
<META http-equiv=Content-Type content="text/html; charset=iso-8859-1">
<STYLE type=text/css>
<!--
.blink {text-decoration:blink}
.ms  {font-size: 9pt; font-family: monospace; font-weight: normal}
.msb {font-size: 9pt; font-family: monospace; font-weight: bold  }
-->
</STYLE>
<META content="MSHTML 6.00.2900.2180" name=GENERATOR></HEAD>
<BODY><B>
</B>
<BR><PRE><A name="Report Header"></A>
--------------------------------------------------------------------------------
<PERSON><PERSON><PERSON> TRACE Report - Hold, Version Diamond (64-bit) 3.12.1.454
Tue Mar 25 11:21:54 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Design file:     INS350_5J_JZ
Device,speed:    LFE5U-25F,M
Report level:    verbose report, limited to 10 items per preference
--------------------------------------------------------------------------------



</A><A name="FREQUENCY NET 'clk120mhz' 120.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "clk120mhz" 120.000000 MHz ;
            10 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------
<font color=#000000> 

Passed: The following path meets requirements by 0.174ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/rs422/SLICE_464">signal_process/rs422/p_sum_dy[29]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/rs422/SLICE_408">signal_process/rs422/RS_dout[5]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.292ns  (55.8% logic, 44.2% route), 1 logic levels.

 Constraint Details:

      0.292ns physical path delay signal_process/rs422/SLICE_464 to signal_process/rs422/SLICE_408 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.174ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.163,R24C39A.CLK,R24C39A.Q1,signal_process/rs422/SLICE_464:ROUTE, 0.129,R24C39A.Q1,R24C39C.M0,signal_process/rs422/p_sum_dy[29]">Data path</A> signal_process/rs422/SLICE_464 to signal_process/rs422/SLICE_408:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R24C39A.CLK to     R24C39A.Q1 <A href="#@comp:signal_process/rs422/SLICE_464">signal_process/rs422/SLICE_464</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         1     0.129<A href="#@net:signal_process/rs422/p_sum_dy[29]:R24C39A.Q1:R24C39C.M0:0.129">     R24C39A.Q1 to R24C39C.M0    </A> <A href="#@net:signal_process/rs422/p_sum_dy[29]">signal_process/rs422/p_sum_dy[29]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.292   (55.8% logic, 44.2% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R24C39A.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/rs422/SLICE_464:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C39A.CLK:0.653">  PLL_BR0.CLKOP to R24C39A.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R24C39C.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/rs422/SLICE_408:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C39C.CLK:0.653">  PLL_BR0.CLKOP to R24C39C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.174ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:u_uart/U2/SLICE_521">u_uart/U2/tx_data[3]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:u_uart/U1/SLICE_506">u_uart/U1/tx_data[3]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.292ns  (55.8% logic, 44.2% route), 1 logic levels.

 Constraint Details:

      0.292ns physical path delay u_uart/U2/SLICE_521 to u_uart/U1/SLICE_506 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.174ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.163,R34C36C.CLK,R34C36C.Q1,u_uart/U2/SLICE_521:ROUTE, 0.129,R34C36C.Q1,R34C36A.M1,u_uart/tx_data[3]">Data path</A> u_uart/U2/SLICE_521 to u_uart/U1/SLICE_506:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R34C36C.CLK to     R34C36C.Q1 <A href="#@comp:u_uart/U2/SLICE_521">u_uart/U2/SLICE_521</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         1     0.129<A href="#@net:u_uart/tx_data[3]:R34C36C.Q1:R34C36A.M1:0.129">     R34C36C.Q1 to R34C36A.M1    </A> <A href="#@net:u_uart/tx_data[3]">u_uart/tx_data[3]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.292   (55.8% logic, 44.2% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R34C36C.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to u_uart/U2/SLICE_521:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R34C36C.CLK:0.653">  PLL_BR0.CLKOP to R34C36C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R34C36A.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to u_uart/U1/SLICE_506:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R34C36A.CLK:0.653">  PLL_BR0.CLKOP to R34C36A.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.175ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/modu/SLICE_389">signal_process/modu/mudu_dy[2]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/modu/SLICE_389">signal_process/modu/mudu_dy[3]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.293ns  (56.0% logic, 44.0% route), 1 logic levels.

 Constraint Details:

      0.293ns physical path delay signal_process/modu/SLICE_389 to signal_process/modu/SLICE_389 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.175ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.164,R16C34A.CLK,R16C34A.Q0,signal_process/modu/SLICE_389:ROUTE, 0.129,R16C34A.Q0,R16C34A.M1,signal_process/modu/mudu_dy[2]">Data path</A> signal_process/modu/SLICE_389 to signal_process/modu/SLICE_389:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R16C34A.CLK to     R16C34A.Q0 <A href="#@comp:signal_process/modu/SLICE_389">signal_process/modu/SLICE_389</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         1     0.129<A href="#@net:signal_process/modu/mudu_dy[2]:R16C34A.Q0:R16C34A.M1:0.129">     R16C34A.Q0 to R16C34A.M1    </A> <A href="#@net:signal_process/modu/mudu_dy[2]">signal_process/modu/mudu_dy[2]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.293   (56.0% logic, 44.0% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R16C34A.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/modu/SLICE_389:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R16C34A.CLK:0.653">  PLL_BR0.CLKOP to R16C34A.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R16C34A.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/modu/SLICE_389:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R16C34A.CLK:0.653">  PLL_BR0.CLKOP to R16C34A.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.175ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:u_uart/U2/SLICE_521">u_uart/U2/tx_data[2]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:u_uart/U1/SLICE_506">u_uart/U1/tx_data[2]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.293ns  (56.0% logic, 44.0% route), 1 logic levels.

 Constraint Details:

      0.293ns physical path delay u_uart/U2/SLICE_521 to u_uart/U1/SLICE_506 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.175ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.164,R34C36C.CLK,R34C36C.Q0,u_uart/U2/SLICE_521:ROUTE, 0.129,R34C36C.Q0,R34C36A.M0,u_uart/tx_data[2]">Data path</A> u_uart/U2/SLICE_521 to u_uart/U1/SLICE_506:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R34C36C.CLK to     R34C36C.Q0 <A href="#@comp:u_uart/U2/SLICE_521">u_uart/U2/SLICE_521</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         1     0.129<A href="#@net:u_uart/tx_data[2]:R34C36C.Q0:R34C36A.M0:0.129">     R34C36C.Q0 to R34C36A.M0    </A> <A href="#@net:u_uart/tx_data[2]">u_uart/tx_data[2]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.293   (56.0% logic, 44.0% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R34C36C.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to u_uart/U2/SLICE_521:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R34C36C.CLK:0.653">  PLL_BR0.CLKOP to R34C36C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R34C36A.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to u_uart/U1/SLICE_506:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R34C36A.CLK:0.653">  PLL_BR0.CLKOP to R34C36A.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.176ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/modu/SLICE_390">signal_process/modu/mudu_dy[5]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/modu/SLICE_613">signal_process/modu/mudu_dy[6]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay signal_process/modu/SLICE_390 to signal_process/modu/SLICE_613 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.163,R15C34B.CLK,R15C34B.Q1,signal_process/modu/SLICE_390:ROUTE, 0.131,R15C34B.Q1,R15C34D.M0,signal_process/modu/mudu_dy[5]">Data path</A> signal_process/modu/SLICE_390 to signal_process/modu/SLICE_613:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R15C34B.CLK to     R15C34B.Q1 <A href="#@comp:signal_process/modu/SLICE_390">signal_process/modu/SLICE_390</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         2     0.131<A href="#@net:signal_process/modu/mudu_dy[5]:R15C34B.Q1:R15C34D.M0:0.131">     R15C34B.Q1 to R15C34D.M0    </A> <A href="#@net:signal_process/modu/mudu_dy[5]">signal_process/modu/mudu_dy[5]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R15C34B.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/modu/SLICE_390:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R15C34B.CLK:0.653">  PLL_BR0.CLKOP to R15C34B.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R15C34D.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/modu/SLICE_613:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R15C34D.CLK:0.653">  PLL_BR0.CLKOP to R15C34D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.177ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/rs422/SLICE_422">signal_process/rs422/RxTr_dy[0]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/rs422/SLICE_422">signal_process/rs422/RxTr_dy[1]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay signal_process/rs422/SLICE_422 to signal_process/rs422/SLICE_422 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.164,R19C45B.CLK,R19C45B.Q0,signal_process/rs422/SLICE_422:ROUTE, 0.131,R19C45B.Q0,R19C45B.M1,signal_process/rs422/RxTr_dy[0]">Data path</A> signal_process/rs422/SLICE_422 to signal_process/rs422/SLICE_422:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R19C45B.CLK to     R19C45B.Q0 <A href="#@comp:signal_process/rs422/SLICE_422">signal_process/rs422/SLICE_422</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         3     0.131<A href="#@net:signal_process/rs422/RxTr_dy[0]:R19C45B.Q0:R19C45B.M1:0.131">     R19C45B.Q0 to R19C45B.M1    </A> <A href="#@net:signal_process/rs422/RxTr_dy[0]">signal_process/rs422/RxTr_dy[0]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R19C45B.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/rs422/SLICE_422:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R19C45B.CLK:0.653">  PLL_BR0.CLKOP to R19C45B.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R19C45B.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/rs422/SLICE_422:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R19C45B.CLK:0.653">  PLL_BR0.CLKOP to R19C45B.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.177ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/integ/SLICE_385">signal_process/integ/inte_dy[0]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/integ/SLICE_385">signal_process/integ/inte_dy[1]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay signal_process/integ/SLICE_385 to signal_process/integ/SLICE_385 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.164,R21C54D.CLK,R21C54D.Q0,signal_process/integ/SLICE_385:ROUTE, 0.131,R21C54D.Q0,R21C54D.M1,signal_process/integ/inte_dy[0]">Data path</A> signal_process/integ/SLICE_385 to signal_process/integ/SLICE_385:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R21C54D.CLK to     R21C54D.Q0 <A href="#@comp:signal_process/integ/SLICE_385">signal_process/integ/SLICE_385</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         2     0.131<A href="#@net:signal_process/integ/inte_dy[0]:R21C54D.Q0:R21C54D.M1:0.131">     R21C54D.Q0 to R21C54D.M1    </A> <A href="#@net:signal_process/integ/inte_dy[0]">signal_process/integ/inte_dy[0]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R21C54D.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/integ/SLICE_385:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R21C54D.CLK:0.653">  PLL_BR0.CLKOP to R21C54D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R21C54D.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/integ/SLICE_385:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R21C54D.CLK:0.653">  PLL_BR0.CLKOP to R21C54D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.178ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/modu/SLICE_403">signal_process/modu/mudu_dy[0]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/modu/SLICE_403">signal_process/modu/mudu_dy[1]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.296ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.296ns physical path delay signal_process/modu/SLICE_403 to signal_process/modu/SLICE_403 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.178ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.164,R19C43C.CLK,R19C43C.Q0,signal_process/modu/SLICE_403:ROUTE, 0.132,R19C43C.Q0,R19C43C.M1,signal_process/mudu_dy[0]">Data path</A> signal_process/modu/SLICE_403 to signal_process/modu/SLICE_403:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R19C43C.CLK to     R19C43C.Q0 <A href="#@comp:signal_process/modu/SLICE_403">signal_process/modu/SLICE_403</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.132<A href="#@net:signal_process/mudu_dy[0]:R19C43C.Q0:R19C43C.M1:0.132">     R19C43C.Q0 to R19C43C.M1    </A> <A href="#@net:signal_process/mudu_dy[0]">signal_process/mudu_dy[0]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.296   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R19C43C.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/modu/SLICE_403:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R19C43C.CLK:0.653">  PLL_BR0.CLKOP to R19C43C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R19C43C.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/modu/SLICE_403:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R19C43C.CLK:0.653">  PLL_BR0.CLKOP to R19C43C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.178ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/rs422/SLICE_405">signal_process/rs422/dalay_cout[0]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/rs422/SLICE_405">signal_process/rs422/dalay_cout[0]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.297ns  (80.8% logic, 19.2% route), 2 logic levels.

 Constraint Details:

      0.297ns physical path delay signal_process/rs422/SLICE_405 to signal_process/rs422/SLICE_405 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.178ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.164,R22C43A.CLK,R22C43A.Q0,signal_process/rs422/SLICE_405:ROUTE, 0.057,R22C43A.Q0,R22C43A.D0,signal_process/rs422/CO0:CTOF_DEL, 0.076,R22C43A.D0,R22C43A.F0,signal_process/rs422/SLICE_405:ROUTE, 0.000,R22C43A.F0,R22C43A.DI0,signal_process/rs422/dalay_cout_3[0]">Data path</A> signal_process/rs422/SLICE_405 to signal_process/rs422/SLICE_405:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R22C43A.CLK to     R22C43A.Q0 <A href="#@comp:signal_process/rs422/SLICE_405">signal_process/rs422/SLICE_405</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         6     0.057<A href="#@net:signal_process/rs422/CO0:R22C43A.Q0:R22C43A.D0:0.057">     R22C43A.Q0 to R22C43A.D0    </A> <A href="#@net:signal_process/rs422/CO0">signal_process/rs422/CO0</A>
CTOF_DEL    ---     0.076     R22C43A.D0 to     R22C43A.F0 <A href="#@comp:signal_process/rs422/SLICE_405">signal_process/rs422/SLICE_405</A>
ROUTE         1     0.000<A href="#@net:signal_process/rs422/dalay_cout_3[0]:R22C43A.F0:R22C43A.DI0:0.000">     R22C43A.F0 to R22C43A.DI0   </A> <A href="#@net:signal_process/rs422/dalay_cout_3[0]">signal_process/rs422/dalay_cout_3[0]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.297   (80.8% logic, 19.2% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R22C43A.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/rs422/SLICE_405:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R22C43A.CLK:0.653">  PLL_BR0.CLKOP to R22C43A.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R22C43A.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/rs422/SLICE_405:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R22C43A.CLK:0.653">  PLL_BR0.CLKOP to R22C43A.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.179ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/trans/SLICE_483">signal_process/trans/count[0]</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/trans/SLICE_483">signal_process/trans/count[0]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               0.298ns  (80.5% logic, 19.5% route), 2 logic levels.

 Constraint Details:

      0.298ns physical path delay signal_process/trans/SLICE_483 to signal_process/trans/SLICE_483 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.179ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:REG_DEL, 0.164,R31C58A.CLK,R31C58A.Q0,signal_process/trans/SLICE_483:ROUTE, 0.058,R31C58A.Q0,R31C58A.D0,signal_process/trans/count[0]:CTOF_DEL, 0.076,R31C58A.D0,R31C58A.F0,signal_process/trans/SLICE_483:ROUTE, 0.000,R31C58A.F0,R31C58A.DI0,signal_process/trans/count_i[0]">Data path</A> signal_process/trans/SLICE_483 to signal_process/trans/SLICE_483:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R31C58A.CLK to     R31C58A.Q0 <A href="#@comp:signal_process/trans/SLICE_483">signal_process/trans/SLICE_483</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         3     0.058<A href="#@net:signal_process/trans/count[0]:R31C58A.Q0:R31C58A.D0:0.058">     R31C58A.Q0 to R31C58A.D0    </A> <A href="#@net:signal_process/trans/count[0]">signal_process/trans/count[0]</A>
CTOF_DEL    ---     0.076     R31C58A.D0 to     R31C58A.F0 <A href="#@comp:signal_process/trans/SLICE_483">signal_process/trans/SLICE_483</A>
ROUTE         1     0.000<A href="#@net:signal_process/trans/count_i[0]:R31C58A.F0:R31C58A.DI0:0.000">     R31C58A.F0 to R31C58A.DI0   </A> <A href="#@net:signal_process/trans/count_i[0]">signal_process/trans/count_i[0]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    0.298   (80.5% logic, 19.5% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R31C58A.CLK,clk120mhz">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/trans/SLICE_483:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R31C58A.CLK:0.653">  PLL_BR0.CLKOP to R31C58A.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOP,R31C58A.CLK,clk120mhz">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/trans/SLICE_483:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       413     0.653<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R31C58A.CLK:0.653">  PLL_BR0.CLKOP to R31C58A.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


</A><A name="FREQUENCY NET 'wendu.clk_us' 1.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
            10 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------
<font color=#000000> 

Passed: The following path meets requirements by 0.176ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_546">wendu/data_temp[7]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_491">wendu/data[7]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_546 to wendu/SLICE_491 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.163,R35C38D.CLK,R35C38D.Q1,wendu/SLICE_546:ROUTE, 0.131,R35C38D.Q1,R35C38A.M1,wendu/data_temp[7]">Data path</A> wendu/SLICE_546 to wendu/SLICE_491:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R35C38D.CLK to     R35C38D.Q1 <A href="#@comp:wendu/SLICE_546">wendu/SLICE_546</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         2     0.131<A href="#@net:wendu/data_temp[7]:R35C38D.Q1:R35C38A.M1:0.131">     R35C38D.Q1 to R35C38A.M1    </A> <A href="#@net:wendu/data_temp[7]">wendu/data_temp[7]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R35C38D.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_546:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R35C38D.CLK:0.894">     R38C31A.Q0 to R35C38D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R35C38A.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_491:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R35C38A.CLK:0.894">     R38C31A.Q0 to R35C38A.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.176ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_543">wendu/data_temp[1]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_543">wendu/data_temp[0]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_543 to wendu/SLICE_543 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.163,R36C40B.CLK,R36C40B.Q1,wendu/SLICE_543:ROUTE, 0.131,R36C40B.Q1,R36C40B.M0,wendu/data_temp[1]">Data path</A> wendu/SLICE_543 to wendu/SLICE_543:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R36C40B.CLK to     R36C40B.Q1 <A href="#@comp:wendu/SLICE_543">wendu/SLICE_543</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         2     0.131<A href="#@net:wendu/data_temp[1]:R36C40B.Q1:R36C40B.M0:0.131">     R36C40B.Q1 to R36C40B.M0    </A> <A href="#@net:wendu/data_temp[1]">wendu/data_temp[1]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C40B.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_543:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C40B.CLK:0.894">     R38C31A.Q0 to R36C40B.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C40B.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_543:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C40B.CLK:0.894">     R38C31A.Q0 to R36C40B.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.176ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_549">wendu/data_temp[13]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_549">wendu/data_temp[12]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_549 to wendu/SLICE_549 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.163,R36C38B.CLK,R36C38B.Q1,wendu/SLICE_549:ROUTE, 0.131,R36C38B.Q1,R36C38B.M0,wendu/data_temp[13]">Data path</A> wendu/SLICE_549 to wendu/SLICE_549:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R36C38B.CLK to     R36C38B.Q1 <A href="#@comp:wendu/SLICE_549">wendu/SLICE_549</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         2     0.131<A href="#@net:wendu/data_temp[13]:R36C38B.Q1:R36C38B.M0:0.131">     R36C38B.Q1 to R36C38B.M0    </A> <A href="#@net:wendu/data_temp[13]">wendu/data_temp[13]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C38B.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_549:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C38B.CLK:0.894">     R38C31A.Q0 to R36C38B.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C38B.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_549:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C38B.CLK:0.894">     R38C31A.Q0 to R36C38B.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.177ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_549">wendu/data_temp[12]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_548">wendu/data_temp[11]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay wendu/SLICE_549 to wendu/SLICE_548 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.164,R36C38B.CLK,R36C38B.Q0,wendu/SLICE_549:ROUTE, 0.131,R36C38B.Q0,R36C38C.M1,wendu/data_temp[12]">Data path</A> wendu/SLICE_549 to wendu/SLICE_548:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R36C38B.CLK to     R36C38B.Q0 <A href="#@comp:wendu/SLICE_549">wendu/SLICE_549</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         2     0.131<A href="#@net:wendu/data_temp[12]:R36C38B.Q0:R36C38C.M1:0.131">     R36C38B.Q0 to R36C38C.M1    </A> <A href="#@net:wendu/data_temp[12]">wendu/data_temp[12]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C38B.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_549:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C38B.CLK:0.894">     R38C31A.Q0 to R36C38B.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C38C.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_548:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C38C.CLK:0.894">     R38C31A.Q0 to R36C38C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.177ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_548">wendu/data_temp[10]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_493">wendu/data[10]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay wendu/SLICE_548 to wendu/SLICE_493 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.164,R36C38C.CLK,R36C38C.Q0,wendu/SLICE_548:ROUTE, 0.131,R36C38C.Q0,R36C38D.M0,wendu/data_temp[10]">Data path</A> wendu/SLICE_548 to wendu/SLICE_493:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R36C38C.CLK to     R36C38C.Q0 <A href="#@comp:wendu/SLICE_548">wendu/SLICE_548</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         2     0.131<A href="#@net:wendu/data_temp[10]:R36C38C.Q0:R36C38D.M0:0.131">     R36C38C.Q0 to R36C38D.M0    </A> <A href="#@net:wendu/data_temp[10]">wendu/data_temp[10]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C38C.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_548:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C38C.CLK:0.894">     R38C31A.Q0 to R36C38C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C38D.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_493:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C38D.CLK:0.894">     R38C31A.Q0 to R36C38D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.177ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_550">wendu/data_temp[14]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_495">wendu/data[14]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay wendu/SLICE_550 to wendu/SLICE_495 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.164,R36C37C.CLK,R36C37C.Q0,wendu/SLICE_550:ROUTE, 0.131,R36C37C.Q0,R36C37A.M0,wendu/data_temp[14]">Data path</A> wendu/SLICE_550 to wendu/SLICE_495:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R36C37C.CLK to     R36C37C.Q0 <A href="#@comp:wendu/SLICE_550">wendu/SLICE_550</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         2     0.131<A href="#@net:wendu/data_temp[14]:R36C37C.Q0:R36C37A.M0:0.131">     R36C37C.Q0 to R36C37A.M0    </A> <A href="#@net:wendu/data_temp[14]">wendu/data_temp[14]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C37C.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_550:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C37C.CLK:0.894">     R38C31A.Q0 to R36C37C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C37A.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_495:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C37A.CLK:0.894">     R38C31A.Q0 to R36C37A.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.177ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_546">wendu/data_temp[6]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_491">wendu/data[6]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay wendu/SLICE_546 to wendu/SLICE_491 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.164,R35C38D.CLK,R35C38D.Q0,wendu/SLICE_546:ROUTE, 0.131,R35C38D.Q0,R35C38A.M0,wendu/data_temp[6]">Data path</A> wendu/SLICE_546 to wendu/SLICE_491:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R35C38D.CLK to     R35C38D.Q0 <A href="#@comp:wendu/SLICE_546">wendu/SLICE_546</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         2     0.131<A href="#@net:wendu/data_temp[6]:R35C38D.Q0:R35C38A.M0:0.131">     R35C38D.Q0 to R35C38A.M0    </A> <A href="#@net:wendu/data_temp[6]">wendu/data_temp[6]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R35C38D.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_546:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R35C38D.CLK:0.894">     R38C31A.Q0 to R35C38D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R35C38A.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_491:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R35C38A.CLK:0.894">     R38C31A.Q0 to R35C38A.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.192ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_526">wendu/bit_cnt[0]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_526">wendu/bit_cnt[0]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.311ns  (77.2% logic, 22.8% route), 2 logic levels.

 Constraint Details:

      0.311ns physical path delay wendu/SLICE_526 to wendu/SLICE_526 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.192ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.164,R36C19D.CLK,R36C19D.Q0,wendu/SLICE_526:ROUTE, 0.071,R36C19D.Q0,R36C19D.C0,wendu/bit_cnt[0]:CTOF_DEL, 0.076,R36C19D.C0,R36C19D.F0,wendu/SLICE_526:ROUTE, 0.000,R36C19D.F0,R36C19D.DI0,wendu/N_128_i">Data path</A> wendu/SLICE_526 to wendu/SLICE_526:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R36C19D.CLK to     R36C19D.Q0 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         7     0.071<A href="#@net:wendu/bit_cnt[0]:R36C19D.Q0:R36C19D.C0:0.071">     R36C19D.Q0 to R36C19D.C0    </A> <A href="#@net:wendu/bit_cnt[0]">wendu/bit_cnt[0]</A>
CTOF_DEL    ---     0.076     R36C19D.C0 to     R36C19D.F0 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         1     0.000<A href="#@net:wendu/N_128_i:R36C19D.F0:R36C19D.DI0:0.000">     R36C19D.F0 to R36C19D.DI0   </A> <A href="#@net:wendu/N_128_i">wendu/N_128_i</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.311   (77.2% logic, 22.8% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C19D.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_526:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C19D.CLK:0.894">     R38C31A.Q0 to R36C19D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C19D.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_526:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C19D.CLK:0.894">     R38C31A.Q0 to R36C19D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.192ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_547">wendu/data_temp[9]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_547">wendu/data_temp[8]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.310ns  (52.6% logic, 47.4% route), 1 logic levels.

 Constraint Details:

      0.310ns physical path delay wendu/SLICE_547 to wendu/SLICE_547 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.192ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.163,R35C38C.CLK,R35C38C.Q1,wendu/SLICE_547:ROUTE, 0.147,R35C38C.Q1,R35C38C.M0,wendu/data_temp[9]">Data path</A> wendu/SLICE_547 to wendu/SLICE_547:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R35C38C.CLK to     R35C38C.Q1 <A href="#@comp:wendu/SLICE_547">wendu/SLICE_547</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         2     0.147<A href="#@net:wendu/data_temp[9]:R35C38C.Q1:R35C38C.M0:0.147">     R35C38C.Q1 to R35C38C.M0    </A> <A href="#@net:wendu/data_temp[9]">wendu/data_temp[9]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.310   (52.6% logic, 47.4% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R35C38C.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_547:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R35C38C.CLK:0.894">     R38C31A.Q0 to R35C38C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R35C38C.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_547:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R35C38C.CLK:0.894">     R38C31A.Q0 to R35C38C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.193ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_549">wendu/data_temp[12]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_494">wendu/data[12]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               0.311ns  (52.7% logic, 47.3% route), 1 logic levels.

 Constraint Details:

      0.311ns physical path delay wendu/SLICE_549 to wendu/SLICE_494 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.193ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.164,R36C38B.CLK,R36C38B.Q0,wendu/SLICE_549:ROUTE, 0.147,R36C38B.Q0,R36C39A.M0,wendu/data_temp[12]">Data path</A> wendu/SLICE_549 to wendu/SLICE_494:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R36C38B.CLK to     R36C38B.Q0 <A href="#@comp:wendu/SLICE_549">wendu/SLICE_549</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         2     0.147<A href="#@net:wendu/data_temp[12]:R36C38B.Q0:R36C39A.M0:0.147">     R36C38B.Q0 to R36C39A.M0    </A> <A href="#@net:wendu/data_temp[12]">wendu/data_temp[12]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    0.311   (52.7% logic, 47.3% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C38B.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_549:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C38B.CLK:0.894">     R38C31A.Q0 to R36C38B.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 0.894,R38C31A.Q0,R36C39A.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_494:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.894<A href="#@net:wendu.clk_us:R38C31A.Q0:R36C39A.CLK:0.894">     R38C31A.Q0 to R36C39A.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    0.894   (0.0% logic, 100.0% route), 0 logic levels.


</A><A name="FREQUENCY NET 'clk_in_c' 20.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "clk_in_c" 20.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


</A><A name="FREQUENCY NET 'clk_AD' 60.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "clk_AD" 60.000000 MHz ;
            10 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------
<font color=#000000> 

Passed: The following path meets requirements by 0.177ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_283">signal_process/demodu/AD_valid_dy[0]</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_283">signal_process/demodu/AD_valid_dy[1]</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay signal_process/demodu/SLICE_283 to signal_process/demodu/SLICE_283 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.164,R41C41D.CLK,R41C41D.Q0,signal_process/demodu/SLICE_283:ROUTE, 0.131,R41C41D.Q0,R41C41D.M1,signal_process/demodu/AD_valid_dy[0]">Data path</A> signal_process/demodu/SLICE_283 to signal_process/demodu/SLICE_283:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R41C41D.CLK to     R41C41D.Q0 <A href="#@comp:signal_process/demodu/SLICE_283">signal_process/demodu/SLICE_283</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         2     0.131<A href="#@net:signal_process/demodu/AD_valid_dy[0]:R41C41D.Q0:R41C41D.M1:0.131">     R41C41D.Q0 to R41C41D.M1    </A> <A href="#@net:signal_process/demodu/AD_valid_dy[0]">signal_process/demodu/AD_valid_dy[0]</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R41C41D.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_283:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R41C41D.CLK:0.653">  PLL_BR0.CLKOS to R41C41D.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R41C41D.CLK,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_283:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R41C41D.CLK:0.653">  PLL_BR0.CLKOS to R41C41D.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.179ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/fifo/SLICE_297">signal_process/demodu/fifo/FF_19</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/fifo/SLICE_297">signal_process/demodu/fifo/FF_19</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.298ns  (80.5% logic, 19.5% route), 2 logic levels.

 Constraint Details:

      0.298ns physical path delay signal_process/demodu/fifo/SLICE_297 to signal_process/demodu/fifo/SLICE_297 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.179ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.164,R42C49A.CLK,R42C49A.Q0,signal_process/demodu/fifo/SLICE_297:ROUTE, 0.058,R42C49A.Q0,R42C49A.D0,signal_process/demodu/fifo/empty_flag:CTOF_DEL, 0.076,R42C49A.D0,R42C49A.F0,signal_process/demodu/fifo/SLICE_297:ROUTE, 0.000,R42C49A.F0,R42C49A.DI0,signal_process/demodu/fifo/empty_d">Data path</A> signal_process/demodu/fifo/SLICE_297 to signal_process/demodu/fifo/SLICE_297:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R42C49A.CLK to     R42C49A.Q0 <A href="#@comp:signal_process/demodu/fifo/SLICE_297">signal_process/demodu/fifo/SLICE_297</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         6     0.058<A href="#@net:signal_process/demodu/fifo/empty_flag:R42C49A.Q0:R42C49A.D0:0.058">     R42C49A.Q0 to R42C49A.D0    </A> <A href="#@net:signal_process/demodu/fifo/empty_flag">signal_process/demodu/fifo/empty_flag</A>
CTOF_DEL    ---     0.076     R42C49A.D0 to     R42C49A.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_297">signal_process/demodu/fifo/SLICE_297</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/empty_d:R42C49A.F0:R42C49A.DI0:0.000">     R42C49A.F0 to R42C49A.DI0   </A> <A href="#@net:signal_process/demodu/fifo/empty_d">signal_process/demodu/fifo/empty_d</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.298   (80.5% logic, 19.5% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R42C49A.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/fifo/SLICE_297:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R42C49A.CLK:0.653">  PLL_BR0.CLKOS to R42C49A.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R42C49A.CLK,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/fifo/SLICE_297:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R42C49A.CLK:0.653">  PLL_BR0.CLKOS to R42C49A.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.193ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_354">signal_process/demodu/sample_sum[0]</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_354">signal_process/demodu/sample_sum[0]</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.312ns  (76.9% logic, 23.1% route), 2 logic levels.

 Constraint Details:

      0.312ns physical path delay signal_process/demodu/SLICE_354 to signal_process/demodu/SLICE_354 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.193ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.164,R27C46C.CLK,R27C46C.Q0,signal_process/demodu/SLICE_354:ROUTE, 0.072,R27C46C.Q0,R27C46C.C0,signal_process/demodu/sample_sum[0]:CTOF_DEL, 0.076,R27C46C.C0,R27C46C.F0,signal_process/demodu/SLICE_354:ROUTE, 0.000,R27C46C.F0,R27C46C.DI0,signal_process/demodu/un3_sample_sum_axb_0">Data path</A> signal_process/demodu/SLICE_354 to signal_process/demodu/SLICE_354:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R27C46C.CLK to     R27C46C.Q0 <A href="#@comp:signal_process/demodu/SLICE_354">signal_process/demodu/SLICE_354</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         4     0.072<A href="#@net:signal_process/demodu/sample_sum[0]:R27C46C.Q0:R27C46C.C0:0.072">     R27C46C.Q0 to R27C46C.C0    </A> <A href="#@net:signal_process/demodu/sample_sum[0]">signal_process/demodu/sample_sum[0]</A>
CTOF_DEL    ---     0.076     R27C46C.C0 to     R27C46C.F0 <A href="#@comp:signal_process/demodu/SLICE_354">signal_process/demodu/SLICE_354</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/un3_sample_sum_axb_0:R27C46C.F0:R27C46C.DI0:0.000">     R27C46C.F0 to R27C46C.DI0   </A> <A href="#@net:signal_process/demodu/un3_sample_sum_axb_0">signal_process/demodu/un3_sample_sum_axb_0</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.312   (76.9% logic, 23.1% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R27C46C.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_354:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R27C46C.CLK:0.653">  PLL_BR0.CLKOS to R27C46C.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R27C46C.CLK,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_354:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R27C46C.CLK:0.653">  PLL_BR0.CLKOS to R27C46C.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.251ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_303">signal_process/demodu/latch_sample_sum[11]</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.345ns  (47.2% logic, 52.8% route), 1 logic levels.

 Constraint Details:

      0.345ns physical path delay signal_process/demodu/SLICE_303 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.051ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.094ns) by 0.251ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.163,R27C47D.CLK,R27C47D.Q1,signal_process/demodu/SLICE_303:ROUTE, 0.182,R27C47D.Q1,EBR_R25C46.DI11,signal_process/demodu/latch_sample_sum[11]">Data path</A> signal_process/demodu/SLICE_303 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R27C47D.CLK to     R27C47D.Q1 <A href="#@comp:signal_process/demodu/SLICE_303">signal_process/demodu/SLICE_303</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.182<A href="#@net:signal_process/demodu/latch_sample_sum[11]:R27C47D.Q1:EBR_R25C46.DI11:0.182">     R27C47D.Q1 to EBR_R25C46.DI11</A> <A href="#@net:signal_process/demodu/latch_sample_sum[11]">signal_process/demodu/latch_sample_sum[11]</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.345   (47.2% logic, 52.8% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R27C47D.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_303:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R27C47D.CLK:0.653">  PLL_BR0.CLKOS to R27C47D.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.696,PLL_BR0.CLKOS,EBR_R25C46.CLKW,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKW:0.696">  PLL_BR0.CLKOS to EBR_R25C46.CLKW</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.251ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_298">signal_process/demodu/latch_sample_sum[1]</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.345ns  (47.2% logic, 52.8% route), 1 logic levels.

 Constraint Details:

      0.345ns physical path delay signal_process/demodu/SLICE_298 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.051ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.094ns) by 0.251ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.163,R26C46D.CLK,R26C46D.Q1,signal_process/demodu/SLICE_298:ROUTE, 0.182,R26C46D.Q1,EBR_R25C46.DI1,signal_process/demodu/latch_sample_sum[1]">Data path</A> signal_process/demodu/SLICE_298 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R26C46D.CLK to     R26C46D.Q1 <A href="#@comp:signal_process/demodu/SLICE_298">signal_process/demodu/SLICE_298</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.182<A href="#@net:signal_process/demodu/latch_sample_sum[1]:R26C46D.Q1:EBR_R25C46.DI1:0.182">     R26C46D.Q1 to EBR_R25C46.DI1</A> <A href="#@net:signal_process/demodu/latch_sample_sum[1]">signal_process/demodu/latch_sample_sum[1]</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.345   (47.2% logic, 52.8% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R26C46D.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_298:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R26C46D.CLK:0.653">  PLL_BR0.CLKOS to R26C46D.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.696,PLL_BR0.CLKOS,EBR_R25C46.CLKW,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKW:0.696">  PLL_BR0.CLKOS to EBR_R25C46.CLKW</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.251ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_299">signal_process/demodu/latch_sample_sum[3]</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.345ns  (47.2% logic, 52.8% route), 1 logic levels.

 Constraint Details:

      0.345ns physical path delay signal_process/demodu/SLICE_299 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.051ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.094ns) by 0.251ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.163,R27C46D.CLK,R27C46D.Q1,signal_process/demodu/SLICE_299:ROUTE, 0.182,R27C46D.Q1,EBR_R25C46.DI3,signal_process/demodu/latch_sample_sum[3]">Data path</A> signal_process/demodu/SLICE_299 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R27C46D.CLK to     R27C46D.Q1 <A href="#@comp:signal_process/demodu/SLICE_299">signal_process/demodu/SLICE_299</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.182<A href="#@net:signal_process/demodu/latch_sample_sum[3]:R27C46D.Q1:EBR_R25C46.DI3:0.182">     R27C46D.Q1 to EBR_R25C46.DI3</A> <A href="#@net:signal_process/demodu/latch_sample_sum[3]">signal_process/demodu/latch_sample_sum[3]</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.345   (47.2% logic, 52.8% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R27C46D.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_299:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R27C46D.CLK:0.653">  PLL_BR0.CLKOS to R27C46D.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.696,PLL_BR0.CLKOS,EBR_R25C46.CLKW,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKW:0.696">  PLL_BR0.CLKOS to EBR_R25C46.CLKW</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.252ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_303">signal_process/demodu/latch_sample_sum[10]</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.346ns  (47.4% logic, 52.6% route), 1 logic levels.

 Constraint Details:

      0.346ns physical path delay signal_process/demodu/SLICE_303 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.051ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.094ns) by 0.252ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.164,R27C47D.CLK,R27C47D.Q0,signal_process/demodu/SLICE_303:ROUTE, 0.182,R27C47D.Q0,EBR_R25C46.DI10,signal_process/demodu/latch_sample_sum[10]">Data path</A> signal_process/demodu/SLICE_303 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R27C47D.CLK to     R27C47D.Q0 <A href="#@comp:signal_process/demodu/SLICE_303">signal_process/demodu/SLICE_303</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.182<A href="#@net:signal_process/demodu/latch_sample_sum[10]:R27C47D.Q0:EBR_R25C46.DI10:0.182">     R27C47D.Q0 to EBR_R25C46.DI10</A> <A href="#@net:signal_process/demodu/latch_sample_sum[10]">signal_process/demodu/latch_sample_sum[10]</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.346   (47.4% logic, 52.6% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R27C47D.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_303:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R27C47D.CLK:0.653">  PLL_BR0.CLKOS to R27C47D.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.696,PLL_BR0.CLKOS,EBR_R25C46.CLKW,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKW:0.696">  PLL_BR0.CLKOS to EBR_R25C46.CLKW</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.252ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_306">signal_process/demodu/latch_sample_sum[16]</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.346ns  (47.4% logic, 52.6% route), 1 logic levels.

 Constraint Details:

      0.346ns physical path delay signal_process/demodu/SLICE_306 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.051ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.094ns) by 0.252ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.164,R26C48D.CLK,R26C48D.Q0,signal_process/demodu/SLICE_306:ROUTE, 0.182,R26C48D.Q0,EBR_R25C46.DI16,signal_process/demodu/latch_sample_sum[16]">Data path</A> signal_process/demodu/SLICE_306 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R26C48D.CLK to     R26C48D.Q0 <A href="#@comp:signal_process/demodu/SLICE_306">signal_process/demodu/SLICE_306</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.182<A href="#@net:signal_process/demodu/latch_sample_sum[16]:R26C48D.Q0:EBR_R25C46.DI16:0.182">     R26C48D.Q0 to EBR_R25C46.DI16</A> <A href="#@net:signal_process/demodu/latch_sample_sum[16]">signal_process/demodu/latch_sample_sum[16]</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.346   (47.4% logic, 52.6% route), 1 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R26C48D.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_306:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R26C48D.CLK:0.653">  PLL_BR0.CLKOS to R26C48D.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.696,PLL_BR0.CLKOS,EBR_R25C46.CLKW,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKW:0.696">  PLL_BR0.CLKOS to EBR_R25C46.CLKW</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.257ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_284">signal_process/demodu/AD_validcnt[0]</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_286">signal_process/demodu/Latch_sum</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.376ns  (63.8% logic, 36.2% route), 2 logic levels.

 Constraint Details:

      0.376ns physical path delay signal_process/demodu/SLICE_284 to signal_process/demodu/SLICE_286 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.257ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.164,R42C40B.CLK,R42C40B.Q0,signal_process/demodu/SLICE_284:ROUTE, 0.136,R42C40B.Q0,R42C38C.D0,signal_process/demodu/AD_validcnt[0]:CTOF_DEL, 0.076,R42C38C.D0,R42C38C.F0,signal_process/demodu/SLICE_286:ROUTE, 0.000,R42C38C.F0,R42C38C.DI0,signal_process/demodu/Latch_sumc">Data path</A> signal_process/demodu/SLICE_284 to signal_process/demodu/SLICE_286:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R42C40B.CLK to     R42C40B.Q0 <A href="#@comp:signal_process/demodu/SLICE_284">signal_process/demodu/SLICE_284</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         3     0.136<A href="#@net:signal_process/demodu/AD_validcnt[0]:R42C40B.Q0:R42C38C.D0:0.136">     R42C40B.Q0 to R42C38C.D0    </A> <A href="#@net:signal_process/demodu/AD_validcnt[0]">signal_process/demodu/AD_validcnt[0]</A>
CTOF_DEL    ---     0.076     R42C38C.D0 to     R42C38C.F0 <A href="#@comp:signal_process/demodu/SLICE_286">signal_process/demodu/SLICE_286</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/Latch_sumc:R42C38C.F0:R42C38C.DI0:0.000">     R42C38C.F0 to R42C38C.DI0   </A> <A href="#@net:signal_process/demodu/Latch_sumc">signal_process/demodu/Latch_sumc</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.376   (63.8% logic, 36.2% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R42C40B.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_284:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R42C40B.CLK:0.653">  PLL_BR0.CLKOS to R42C40B.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R42C38C.CLK,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_286:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R42C38C.CLK:0.653">  PLL_BR0.CLKOS to R42C38C.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.257ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_284">signal_process/demodu/AD_validcnt[0]</A>  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_288">signal_process/demodu/Write_en</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               0.376ns  (63.8% logic, 36.2% route), 2 logic levels.

 Constraint Details:

      0.376ns physical path delay signal_process/demodu/SLICE_284 to signal_process/demodu/SLICE_288 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.257ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.164,R42C40B.CLK,R42C40B.Q0,signal_process/demodu/SLICE_284:ROUTE, 0.136,R42C40B.Q0,R42C38A.D0,signal_process/demodu/AD_validcnt[0]:CTOF_DEL, 0.076,R42C38A.D0,R42C38A.F0,signal_process/demodu/SLICE_288:ROUTE, 0.000,R42C38A.F0,R42C38A.DI0,signal_process/demodu/Write_enc">Data path</A> signal_process/demodu/SLICE_284 to signal_process/demodu/SLICE_288:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R42C40B.CLK to     R42C40B.Q0 <A href="#@comp:signal_process/demodu/SLICE_284">signal_process/demodu/SLICE_284</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         3     0.136<A href="#@net:signal_process/demodu/AD_validcnt[0]:R42C40B.Q0:R42C38A.D0:0.136">     R42C40B.Q0 to R42C38A.D0    </A> <A href="#@net:signal_process/demodu/AD_validcnt[0]">signal_process/demodu/AD_validcnt[0]</A>
CTOF_DEL    ---     0.076     R42C38A.D0 to     R42C38A.F0 <A href="#@comp:signal_process/demodu/SLICE_288">signal_process/demodu/SLICE_288</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/Write_enc:R42C38A.F0:R42C38A.DI0:0.000">     R42C38A.F0 to R42C38A.DI0   </A> <A href="#@net:signal_process/demodu/Write_enc">signal_process/demodu/Write_enc</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    0.376   (63.8% logic, 36.2% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R42C40B.CLK,clk_AD">Source Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_284:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R42C40B.CLK:0.653">  PLL_BR0.CLKOS to R42C40B.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:ROUTE, 0.653,PLL_BR0.CLKOS,R42C38A.CLK,clk_AD">Destination Clock Path</A> CLK120/PLLInst_0 to signal_process/demodu/SLICE_288:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653<A href="#@net:clk_AD:PLL_BR0.CLKOS:R42C38A.CLK:0.653">  PLL_BR0.CLKOS to R42C38A.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


</A><A name="FREQUENCY NET 'clk_AD_t_c' 60.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


</A><A name="BLOCK PATH FROM CLKNET 'clk_AD_t_c' TO CLKNET 'clk120mhz"></A>================================================================================
Preference: BLOCK PATH FROM CLKNET "clk_AD_t_c" TO CLKNET "clk120mhz" ;
            0 items scored.
--------------------------------------------------------------------------------

<A name="Report Summary"></A><B><U><big>Report Summary</big></U></B>
--------------
----------------------------------------------------------------------------
Preference(MIN Delays)                  |   Constraint|       Actual|Levels
----------------------------------------------------------------------------
                                        |             |             |
FREQUENCY NET "clk120mhz" 120.000000    |             |             |
MHz ;                                   |     0.000 ns|     0.174 ns|   1  
                                        |             |             |
FREQUENCY NET "wendu.clk_us" 1.000000   |             |             |
MHz ;                                   |     0.000 ns|     0.176 ns|   1  
                                        |             |             |
FREQUENCY NET "clk_in_c" 20.000000 MHz  |             |             |
;                                       |            -|            -|   0  
                                        |             |             |
FREQUENCY NET "clk_AD" 60.000000 MHz ;  |     0.000 ns|     0.177 ns|   1  
                                        |             |             |
FREQUENCY NET "clk_AD_t_c" 60.000000    |             |             |
MHz ;                                   |            -|            -|   0  
                                        |             |             |
----------------------------------------------------------------------------


All preferences were met.


<A name="Clock Domains Analysis"></A><B><U><big>Clock Domains Analysis</big></U></B>
------------------------

Found 4 clocks:

Clock Domain: <A href="#@net:wendu.clk_us">wendu.clk_us</A>   Source: wendu/SLICE_241.Q0   Loads: 37
   Covered under: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;

Clock Domain: <A href="#@net:clk_in_c">clk_in_c</A>   Source: clk_in.PAD   Loads: 1
   No transfer within this clock domain is found

Clock Domain: <A href="#@net:clk_AD">clk_AD</A>   Source: CLK120/PLLInst_0.CLKOS   Loads: 101
   Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;

   Data transfers from:
   Clock Domain: <A href="#@net:clk120mhz">clk120mhz</A>   Source: CLK120/PLLInst_0.CLKOP
      Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;   Transfers: 2

Clock Domain: <A href="#@net:clk120mhz">clk120mhz</A>   Source: CLK120/PLLInst_0.CLKOP   Loads: 413
   Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;

   Data transfers from:
   Clock Domain: <A href="#@net:wendu.clk_us">wendu.clk_us</A>   Source: wendu/SLICE_241.Q0
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 16

   Clock Domain: <A href="#@net:clk_AD_t_c">clk_AD_t_c</A>   Source: CLK120/PLLInst_0.CLKOS2
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD_t_c" TO CLKNET "clk120mhz" ;

   Clock Domain: <A href="#@net:clk_AD">clk_AD</A>   Source: CLK120/PLLInst_0.CLKOS
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 111


Timing summary (Hold):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38518 paths, 6 nets, and 3974 connections (99.15% coverage)



Timing summary (Setup and Hold):
---------------

Timing errors: 0 (setup), 0 (hold)
Score: 0 (setup), 0 (hold)
Cumulative negative slack: 0 (0+0)
