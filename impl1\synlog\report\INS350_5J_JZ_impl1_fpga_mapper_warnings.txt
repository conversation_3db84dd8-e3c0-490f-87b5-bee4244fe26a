@W: FA239 :"d:\project\ins350_5j_jz _copy\src_al\src_al\ds18b20.v":269:11:269:33|ROM RD_CMD_DATA_pmux (in view: work.DS18B20(verilog)) mapped in logic. To map to a technology ROM, apply attribute syn_romstyle on this instance.
@W: FA239 :"d:\project\ins350_5j_jz _copy\src_al\src_al\ds18b20.v":211:11:211:33|ROM WR_CMD_DATA_pmux (in view: work.DS18B20(verilog)) mapped in logic. To map to a technology ROM, apply attribute syn_romstyle on this instance.
@W: BN132 :"d:\project\ins350_5j_jz _copy\src_al\src_al\rs422output.v":88:0:88:5|Removing instance signal_process.rs422.output_dy[0] because it is equivalent to instance signal_process.modu.mudu_dy[0]. To keep the instance, apply constraint syn_preserve=1 on the instance.
@W: BN132 :"d:\project\ins350_5j_jz _copy\src_al\src_al\rs422output.v":88:0:88:5|Removing instance signal_process.rs422.output_dy[1] because it is equivalent to instance signal_process.modu.mudu_dy[1]. To keep the instance, apply constraint syn_preserve=1 on the instance.
@W: MT246 :"d:\project\ins350_5j_jz _copy\ip_al\global_clock1\global_clock\global_clock.v":59:12:59:20|Blackbox EHXPLLL is missing a user supplied timing model. This may have a negative effect on timing analysis and optimizations (Quality of Results)
@W: MT420 |Found inferred clock global_clock|CLKOP_inferred_clock with period 5.00ns. Please declare a user-defined clock on net CLK120.clk120mhz.
@W: MT420 |Found inferred clock global_clock|CLKOS_inferred_clock with period 5.00ns. Please declare a user-defined clock on net CLK120.clk_AD.
