// Verilog netlist produced by program LSE :  version Diamond (64-bit) 3.12.1.454
// Netlist written on Thu Mar 13 16:52:25 2025
//
// Verilog Description of module global_clock
//

module global_clock (CLKI, RST, CLKOP, CLKOS, CLKOS2, LOCK) /* synthesis NGD_DRC_MASK=1, syn_module_defined=1 */ ;   // d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(8[8:20])
    input CLKI;   // d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(9[16:20])
    input RST;   // d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(10[16:19])
    output CLKOP;   // d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(11[17:22])
    output CLKOS;   // d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(12[17:22])
    output CLKOS2;   // d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(13[17:23])
    output LOCK;   // d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(14[17:21])
    
    wire CLKI /* synthesis is_clock=1 */ ;   // d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(9[16:20])
    wire CLKOP /* synthesis is_clock=1 */ ;   // d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(11[17:22])
    
    wire scuba_vlo, VCC_net;
    
    VLO scuba_vlo_inst (.Z(scuba_vlo));
    EHXPLLL PLLInst_0 (.CLKI(CLKI), .CLKFB(CLKOP), .PHASESEL0(scuba_vlo), 
            .PHASESEL1(scuba_vlo), .PHASEDIR(scuba_vlo), .PHASESTEP(scuba_vlo), 
            .PHASELOADREG(scuba_vlo), .STDBY(scuba_vlo), .PLLWAKESYNC(scuba_vlo), 
            .RST(RST), .ENCLKOP(scuba_vlo), .ENCLKOS(scuba_vlo), .ENCLKOS2(scuba_vlo), 
            .ENCLKOS3(scuba_vlo), .CLKOP(CLKOP), .CLKOS(CLKOS), .CLKOS2(CLKOS2), 
            .LOCK(LOCK)) /* synthesis FREQUENCY_PIN_CLKOS2="60.000000", FREQUENCY_PIN_CLKOS="60.000000", FREQUENCY_PIN_CLKOP="120.000000", FREQUENCY_PIN_CLKI="20.000000", ICP_CURRENT="5", LPF_RESISTOR="16", syn_instantiated=1 */ ;
    defparam PLLInst_0.CLKI_DIV = 1;
    defparam PLLInst_0.CLKFB_DIV = 6;
    defparam PLLInst_0.CLKOP_DIV = 5;
    defparam PLLInst_0.CLKOS_DIV = 10;
    defparam PLLInst_0.CLKOS2_DIV = 10;
    defparam PLLInst_0.CLKOS3_DIV = 1;
    defparam PLLInst_0.CLKOP_ENABLE = "ENABLED";
    defparam PLLInst_0.CLKOS_ENABLE = "ENABLED";
    defparam PLLInst_0.CLKOS2_ENABLE = "ENABLED";
    defparam PLLInst_0.CLKOS3_ENABLE = "DISABLED";
    defparam PLLInst_0.CLKOP_CPHASE = 4;
    defparam PLLInst_0.CLKOS_CPHASE = 9;
    defparam PLLInst_0.CLKOS2_CPHASE = 9;
    defparam PLLInst_0.CLKOS3_CPHASE = 0;
    defparam PLLInst_0.CLKOP_FPHASE = 0;
    defparam PLLInst_0.CLKOS_FPHASE = 0;
    defparam PLLInst_0.CLKOS2_FPHASE = 0;
    defparam PLLInst_0.CLKOS3_FPHASE = 0;
    defparam PLLInst_0.FEEDBK_PATH = "CLKOP";
    defparam PLLInst_0.CLKOP_TRIM_POL = "FALLING";
    defparam PLLInst_0.CLKOP_TRIM_DELAY = 0;
    defparam PLLInst_0.CLKOS_TRIM_POL = "FALLING";
    defparam PLLInst_0.CLKOS_TRIM_DELAY = 0;
    defparam PLLInst_0.OUTDIVIDER_MUXA = "DIVA";
    defparam PLLInst_0.OUTDIVIDER_MUXB = "DIVB";
    defparam PLLInst_0.OUTDIVIDER_MUXC = "DIVC";
    defparam PLLInst_0.OUTDIVIDER_MUXD = "DIVD";
    defparam PLLInst_0.PLL_LOCK_MODE = 2;
    defparam PLLInst_0.PLL_LOCK_DELAY = 200;
    defparam PLLInst_0.STDBY_ENABLE = "DISABLED";
    defparam PLLInst_0.REFIN_RESET = "DISABLED";
    defparam PLLInst_0.SYNC_ENABLE = "DISABLED";
    defparam PLLInst_0.INT_LOCK_STICKY = "ENABLED";
    defparam PLLInst_0.DPHASE_SOURCE = "DISABLED";
    defparam PLLInst_0.PLLRST_ENA = "ENABLED";
    defparam PLLInst_0.INTFB_WAKE = "DISABLED";
    GSR GSR_INST (.GSR(VCC_net));
    PUR PUR_INST (.PUR(VCC_net));
    defparam PUR_INST.RST_PULSE = 1;
    VHI i87 (.Z(VCC_net));
    
endmodule
//
// Verilog Description of module PUR
// module not written out since it is a black-box. 
//

