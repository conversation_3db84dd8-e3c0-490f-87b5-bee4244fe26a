<?xml version='1.0' encoding='utf-8' ?>
<!DOCTYPE		ispXCF	SYSTEM	"IspXCF.dtd" >
<ispXCF version="3.12">
	<Comment></Comment>
	<Chain>
		<Comm>JTAG</Comm>
		<Device>
			<SelectedProg value="TRUE"/>
			<Pos>1</Pos>
			<Vendor>WinBond</Vendor>
			<Family>ECP5U</Family>
			<Name>LFE5U-25F</Name>
			<Package>All</Package>
			<Bypass>
				<InstrLen>8</InstrLen>
				<InstrVal>11111111</InstrVal>
				<BScanLen>1</BScanLen>
				<BScanVal>0</BScanVal>
			</Bypass>
			<File>D:/Project/INS350_5J_JZ _copy/impl1/INS350_5J_JZ_impl1.bit</File>
			<FileTime>06/23/25 15:41:45</FileTime>
			<Jedec<PERSON>hecksum>0x282E</Jedec<PERSON>hecksum>
			<Operation>SPI Flash Erase,Program,Verify</Operation>
			<Option>
				<SVFVendor>JTAG STANDARD</SVFVendor>
				<Usercode>0x00000000</Usercode>
				<AccessMode>SPI Flash Background Programming</AccessMode>
			</Option>
			<FPGALoader>
			<CPLDDevice>
				<Device>
					<Pos>1</Pos>
					<Vendor>Lattice</Vendor>
					<Family>ECP5U</Family>
					<Name>LFE5U-25F</Name>
					<IDCode>0x41111043</IDCode>
					<Package>All</Package>
					<PON>LFE5U-25F</PON>
					<Bypass>
						<InstrLen>8</InstrLen>
						<InstrVal>11111111</InstrVal>
						<BScanLen>1</BScanLen>
						<BScanVal>0</BScanVal>
					</Bypass>
					<Operation>Bypass</Operation>
					<Option>
						<SVFVendor>JTAG STANDARD</SVFVendor>
						<IOState>HighZ</IOState>
						<PreloadLength>409</PreloadLength>
						<IOVectorData>0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF</IOVectorData>
						<AccessMode>JTAG</AccessMode>
					</Option>
				</Device>
			</CPLDDevice>
			<FlashDevice>
				<Device>
					<Pos>1</Pos>
					<Vendor>WinBond</Vendor>
					<Family>SPI Serial Flash</Family>
					<Name>W25Q16JV</Name>
					<IDCode>0x14</IDCode>
					<Package>150mil 8-pin SOIC</Package>
					<Operation>SPI Flash Erase,Program,Verify</Operation>
					<File>D:/Project/INS350_5J_JZ _copy/impl1/INS350_5J_JZ_impl1.bit</File>
					<AddressBase>0x00000000</AddressBase>
					<EndAddress>0x00080000</EndAddress>
					<DeviceSize>16</DeviceSize>
					<DataSize>582677</DataSize>
					<NumberOfDevices>1</NumberOfDevices>
					<ReInitialize value="FALSE"/>
				</Device>
			</FlashDevice>
			<FPGADevice>
				<Device>
					<Pos>1</Pos>
					<Name></Name>
					<File>D:/Project/INS350_5J_JZ _copy/impl1/INS350_5J_JZ_impl1.bit</File>
					<LocalChainList>
						<LocalDevice index="-99"
							name="Unknown"
							file="D:/Project/INS350_5J_JZ _copy/impl1/INS350_5J_JZ_impl1.bit"/>
					</LocalChainList>
					<Option>
						<SVFVendor>JTAG STANDARD</SVFVendor>
					</Option>
				</Device>
			</FPGADevice>
			</FPGALoader>
		</Device>
	</Chain>
	<ProjectOptions>
		<Program>SEQUENTIAL</Program>
		<Process>ENTIRED CHAIN</Process>
		<OperationOverride>No Override</OperationOverride>
		<StartTAP>TLR</StartTAP>
		<EndTAP>TLR</EndTAP>
		<VerifyUsercode value="FALSE"/>
		<TCKDelay>1</TCKDelay>
	</ProjectOptions>
	<CableOptions>
		<CableName>USB</CableName>
		<PortAdd>EzUSB-0</PortAdd>
		<USBID>\\?\usb#vid_1134&amp;amp;pid_8001#6&amp;amp;347e1681&amp;amp;2&amp;amp;1#</USBID>
	</CableOptions>
</ispXCF>
