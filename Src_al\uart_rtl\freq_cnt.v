module freq_cnt #(
parameter SYS_FREQ=25_000_000
)(
input clk,
input rstn,
input clkdet,
output [31:0] sig_clkcnt,
output data_vld
);


//gen 1s pulse
reg frame_1s;
reg [31:0] cnt;
always @(posedge clk or negedge  rstn) begin
    if (rstn==1'b0) begin
        frame_1s<=1'b0;
        cnt<='d0;
    end 
    else begin
        cnt<=(cnt==SYS_FREQ-1)? 'd0:cnt+1'b1;
        frame_1s<=(cnt==SYS_FREQ-1)? ~frame_1s:frame_1s;
    end 
end //end always 


//sync frame_1s  to sig zone 

reg [2:0] frame_dly;
always @(posedge clkdet or negedge rstn)  begin
    if(rstn==1'b0)
        frame_dly<=3'b000;
    else 
        frame_dly<={frame_dly[1:0],frame_1s};
end 
assign frame_fall=~frame_dly[1] && frame_dly[2];

reg [31:0] freq;
always @(posedge clkdet or negedge rstn) begin
    if(rstn==1'b0)
       freq<='d0;
    else begin
        if(frame_dly[1]==1'b1) 
            freq<=freq+1'b1;
        else 
            freq<='d0;
    end 
end 

reg [31:0]freq_ff0;
always @(posedge clkdet) 
	if(frame_fall==1'b1)
    	freq_ff0<=freq;

assign data_vld=frame_fall;
assign sig_clkcnt=freq_ff0;
endmodule 
