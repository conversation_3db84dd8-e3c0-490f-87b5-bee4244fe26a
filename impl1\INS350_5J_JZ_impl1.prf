SCHEMATIC START ;
# map:  version Diamond (64-bit) 3.12.1.454 -- WARNING: Map write only section -- Tu<PERSON> Jun 24 17:15:08 2025

SYSCONFIG SLAVE_SPI_PORT=DISABLE MASTER_SPI_PORT=DISABLE SLAVE_PARALLEL_PORT=DISABLE BACKGROUND_RECONFIG=OFF DONE_EX=OFF DONE_OD=ON DONE_PULL=ON MCCLK_FREQ=2.4 TRANSFR=OFF CONFIG_IOVOLTAGE=3.3 CONFIG_SECURE=OFF WAKE_UP=21 COMPRESS_CONFIG=OFF CONFIG_MODE=JTAG INBUF=OFF ;
LOCATE COMP "dq" SITE "A3" ;
LOCATE COMP "TXD" SITE "A14" ;
LOCATE COMP "clk_in" SITE "L16" ;
LOCATE COMP "clk_DA" SITE "M2" ;
LOCATE COMP "DA_DATA[13]" SITE "M1" ;
LOCATE COMP "DA_DATA[12]" SITE "L1" ;
LOCATE COMP "DA_DATA[11]" SITE "J2" ;
LOCATE COMP "DA_DATA[10]" SITE "J1" ;
LOCATE COMP "DA_DATA[9]" SITE "G2" ;
LOCATE COMP "DA_DATA[8]" SITE "G1" ;
LOCATE COMP "DA_DATA[7]" SITE "F2" ;
LOCATE COMP "DA_DATA[6]" SITE "F1" ;
LOCATE COMP "DA_DATA[5]" SITE "E1" ;
LOCATE COMP "DA_DATA[4]" SITE "D1" ;
LOCATE COMP "DA_DATA[3]" SITE "C1" ;
LOCATE COMP "DA_DATA[2]" SITE "C2" ;
LOCATE COMP "DA_DATA[1]" SITE "B1" ;
LOCATE COMP "DA_DATA[0]" SITE "B2" ;
LOCATE COMP "AD_DATA[11]" SITE "P1" ;
LOCATE COMP "AD_DATA[10]" SITE "P2" ;
LOCATE COMP "AD_DATA[9]" SITE "R1" ;
LOCATE COMP "AD_DATA[8]" SITE "R2" ;
LOCATE COMP "AD_DATA[7]" SITE "T2" ;
LOCATE COMP "AD_DATA[6]" SITE "R3" ;
LOCATE COMP "AD_DATA[5]" SITE "T3" ;
LOCATE COMP "AD_DATA[4]" SITE "R4" ;
LOCATE COMP "AD_DATA[3]" SITE "T4" ;
LOCATE COMP "AD_DATA[2]" SITE "R5" ;
LOCATE COMP "AD_DATA[1]" SITE "R6" ;
LOCATE COMP "AD_DATA[0]" SITE "T6" ;
LOCATE COMP "TxTransmit" SITE "A2" ;
FREQUENCY NET "clk120mhz" 120.000000 MHz ;
FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
USE PRIMARY NET "clk120mhz" ;
FREQUENCY NET "clk_in_c" 20.000000 MHz ;
FREQUENCY NET "clk_AD" 60.000000 MHz ;
FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
SCHEMATIC END ;
RVL_ALIAS "reveal_ist_1" "CLK120/CLKOP_t"; 
RVL_ALIAS "reveal_ist_1" "CLK120/CLKOP_t"; 
RVL_ALIAS "reveal_ist_1" "CLK120/CLKOP_t"; 
RVL_ALIAS "reveal_ist_1" "CLK120/CLKOP_t"; 
RVL_ALIAS "reveal_ist_1" "CLK120/CLKOP_t"; 
RVL_ALIAS "reveal_ist_1" "CLK120/CLKOP_t"; 
BLOCK RESETPATHS ;
BLOCK ASYNCPATHS ;
BANK 0 VCCIO 3.3 V;
BANK 1 VCCIO 3.3 V;
BANK 2 VCCIO 3.3 V;
BANK 3 VCCIO 3.3 V;
BANK 6 VCCIO 3.3 V;
BANK 7 VCCIO 3.3 V;
BANK 8 VCCIO 3.3 V;
BLOCK JTAGPATHS ;
BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
COMMERCIAL ;
