synthesis:  version Diamond (64-bit) 3.12.1.454

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
Tue Mar 18 15:52:11 2025


Command Line:  synthesis -f INS350_5J_JZ_impl1_lattice.synproj -gui -msgset D:/Project/INS350_5J_JZ/promote.xml 

Synthesis options:
The -a option is ECP5U.
The -s option is 7.
The -t option is CABGA256.
The -d option is LFE5U-25F.
Using package CABGA256.
Using performance grade 7.
                                                          

##########################################################

### Lattice Family : ECP5U

### Device  : LFE5U-25F

### Package : CABGA256

### Speed   : 7

##########################################################

                                                          

INFO - synthesis: User-Selected Strategy Settings
Optimization goal = Timing
Top-level module name = INS350_5J_JZ.
Target frequency = 200.000000 MHz.
Maximum fanout = 1000.
Timing path count = 3 (default)
BRAM utilization = 100.000000 %
DSP usage = true
DSP utilization = 100.000000 %
fsm_encoding_style = auto
resolve_mixed_drivers = 0
fix_gated_clocks = 1

Mux style = Auto
Use Carry Chain = true
carry_chain_length = 0
Loop Limit = 1950.
Use IO Insertion = TRUE
Use IO Reg = AUTO

Resource Sharing = TRUE
Propagate Constants = TRUE
Remove Duplicate Registers = TRUE
force_gsr = no
ROM style = auto
RAM style = auto
The -comp option is FALSE.
The -syn option is FALSE.
-p D:/Project/INS350_5J_JZ (searchpath added)
-p D:/Software/lscc/diamond/3.12/ispfpga/sa5p00/data (searchpath added)
-p D:/Project/INS350_5J_JZ/impl1 (searchpath added)
-p D:/Project/INS350_5J_JZ (searchpath added)
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/INS350_5J_JZ.v
Verilog design file = D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/Ctrl_Data.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/Demodulation.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/DS18B20.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/Integration.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/Modulation.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/Rs422Output.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/SignalGenerator.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/SignalProcessing.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/speed_select_Tx.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/SquareWaveGenerator.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/UART_Control.v
Verilog design file = D:/Project/INS350_5J_JZ/Src_al/uart_tx.v
Verilog design file = D:/Project/INS350_5J_JZ/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v
NGD file = INS350_5J_JZ_impl1.ngd
-sdc option: SDC file input not used.
-lpf option: Output file option is ON.
Hardtimer checking is enabled (default). The -dt option is not used.
The -r option is OFF. [ Remove LOC Properties is OFF. ]
Technology check ok...

Analyzing Verilog file D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v. VERI-1482
Compile design.
Compile Design Begin
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/ins350_5j_jz.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/ctrl_data.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/demodulation.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/ds18b20.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/integration.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/modulation.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/rs422output.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/signalgenerator.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/signalprocessing.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/speed_select_tx.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/squarewavegenerator.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/uart_control.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/src_al/uart_tx.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v. VERI-1482
Analyzing Verilog file D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v. VERI-1482
Top module name (Verilog): INS350_5J_JZ
INFO - synthesis: d:/project/ins350_5j_jz/src_al/ins350_5j_jz.v(17): compiling module INS350_5J_JZ. VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(8): compiling module global_clock. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(757): compiling module VHI. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(761): compiling module VLO. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(1696): compiling module EHXPLLL_renamed_due_excessive_length_1. VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/ds18b20.v(17): compiling module DS18B20. VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/uart_control.v(16): compiling module UART_Control. VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/speed_select_tx.v(17): compiling module speed_select_Tx. VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/uart_tx.v(16): compiling module uart_tx. VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/ctrl_data.v(17): compiling module Ctrl_Data. VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/signalprocessing.v(41): compiling module SignalProcessing(acum_cnt=40,iWID_TRANS=13,wCLOSED=1'b0,iTRANSIT_TIME=296,iAD_VALID_START=150,iFEEDBACK_SCALE=10,iOUTPUT_SCALE=1350,iDELAYED=120,DA_CONSTANT=16200,iTRANSMIT_COFF=600000). VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/signalgenerator.v(42): compiling module SignalGenerator(iTRANSIT_TIME=296,iAD_VALID_START=150). VERI-1018
WARNING - synthesis: d:/project/ins350_5j_jz/src_al/signalgenerator.v(76): expression size 32 truncated to fit in target size 13. VERI-1209
WARNING - synthesis: d:/project/ins350_5j_jz/src_al/signalgenerator.v(79): expression size 32 truncated to fit in target size 16. VERI-1209
INFO - synthesis: d:/project/ins350_5j_jz/src_al/demodulation.v(42): compiling module Demodulation(acum_cnt=40). VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(8): compiling module Asys_fifo56X16. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(25): compiling module AND2. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(367): compiling module INV. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(810): compiling module XOR2. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(710): compiling module ROM16X1A(initval=16'b011001000110010). VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(710): compiling module ROM16X1A(initval=16'b0100010001010000). VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(959): compiling module PDPW16KD(RESETMODE="ASYNC",CSDECODE_W="0b001"). VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(119): compiling module FD1P3DX. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(160): compiling module FD1S3BX. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(168): compiling module FD1S3DX. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76): compiling module CCU2C(INIT0=16'b0110011010101010,INIT1=16'b0110011010101010,INJECT1_0="NO",INJECT1_1="NO"). VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(76): compiling module CCU2C(INIT0=16'b1001100110101010,INIT1=16'b1001100110101010,INJECT1_0="NO",INJECT1_1="NO"). VERI-1018
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(362): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(402): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(450): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(498): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(538): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(578): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(626): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(674): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(722): input port CIN is not connected on this instance. VDB-1013
INFO - synthesis: d:/project/ins350_5j_jz/src_al/integration.v(42): compiling module Integration. VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/modulation.v(42): compiling module Modulation(iWID_TRANS=13,DA_CONSTANT=16200,wCLOSED=1'b0). VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/rs422output.v(43): compiling module Rs422Output(iDELAYED=120). VERI-1018
INFO - synthesis: d:/project/ins350_5j_jz/src_al/squarewavegenerator.v(16): compiling module SquareWaveGenerator(iTRANSMIT_COFF=600000). VERI-1018
WARNING - synthesis: d:/project/ins350_5j_jz/src_al/squarewavegenerator.v(34): expression size 32 truncated to fit in target size 20. VERI-1209
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/sa5p00/data/sa5plib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/xo2c00/data/xo2clib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/mg5g00/data/mg5glib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/or5g00/data/orc5glib.ngl'...
Loading device for application map from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Top-level module name = INS350_5J_JZ.
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(362): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(402): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(450): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(498): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(538): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(578): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(626): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(674): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/asys_fifo56x16/asys_fifo56x16.v(722): input port CIN is not connected on this instance. VDB-1013
WARNING - synthesis: I/O Port RXD 's net has no driver and is unused.
WARNING - synthesis: I/O Port RxTransmit 's net has no driver and is unused.
######## Missing driver on net n91. Patching with GND.
######## Missing driver on net n90. Patching with GND.
######## Missing driver on net n89. Patching with GND.
######## Missing driver on net n87. Patching with GND.
######## Missing driver on net n85. Patching with GND.
######## Missing driver on net n86. Patching with GND.
######## Missing driver on net n88. Patching with GND.
######## Missing driver on net n84. Patching with GND.
######## Missing driver on net n83. Patching with GND.
WARNING - synthesis: Bit 13 of Register \signal_process/modu/square is stuck at Zero
WARNING - synthesis: Bit 12 of Register \signal_process/modu/square is stuck at Zero
WARNING - synthesis: Bit 13 of Register \signal_process/modu/square_dy is stuck at Zero
WARNING - synthesis: Bit 12 of Register \signal_process/modu/square_dy is stuck at Zero
WARNING - synthesis: Bit 1 of Register \signal_process/modu/dout_mult is stuck at Zero
WARNING - synthesis: Bit 0 of Register \signal_process/modu/dout_mult is stuck at Zero
WARNING - synthesis: Bit 2 of Register \signal_process/modu/dout_mult is stuck at Zero
INFO - synthesis: Extracted state machine for register '\u_uart/U2/tx_state' with one-hot encoding
original encoding -> new encoding (one-hot encoding)

 0000 -> 00000000001

 0001 -> 00000000010

 0010 -> 00000000100

 0011 -> 00000001000

 0100 -> 00000010000

 0101 -> 00000100000

 0110 -> 00001000000

 0111 -> 00010000000

 1000 -> 00100000000

 1001 -> 01000000000

 1010 -> 10000000000

INFO - synthesis: Extracted state machine for register '\wendu/cur_state' with one-hot encoding
original encoding -> new encoding (one-hot encoding)

 000001 -> 000001

 000010 -> 000010

 000100 -> 000100

 001000 -> 001000

 010000 -> 010000

 100000 -> 100000

INFO - synthesis: Extracted state machine for register '\signal_process/rs422/trans_state' with one-hot encoding
original encoding -> new encoding (one-hot encoding)

 0000 -> 000001

 0001 -> 000010

 0010 -> 000100

 0011 -> 001000

 0100 -> 010000

 0101 -> 100000






######### Begin FIR Info ########


Number of FIR filters recognized: 1



######### End FIR Info ########


WARNING - synthesis: Bit 10 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 9 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 8 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 7 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 6 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 5 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 4 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 3 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 2 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 1 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: Bit 0 of Register \signal_process/modu/dout_mult__0_res3_e2 is stuck at Zero
WARNING - synthesis: d:/project/ins350_5j_jz/src_al/modulation.v(86): Register \signal_process/modu/dout_reg_i3 clock is stuck at Zero. VDB-5035
WARNING - synthesis: Bit 19 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 18 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 17 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 16 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 15 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 14 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 13 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 12 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 11 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 10 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 9 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 8 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 7 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 6 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 5 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 4 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 3 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 2 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 1 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 0 of Register mult_641_e1 is stuck at Zero
WARNING - synthesis: Bit 13 of Register \signal_process/modu/dout_reg_e2 is stuck at Zero
WARNING - synthesis: Bit 12 of Register \signal_process/modu/dout_reg_e2 is stuck at Zero
WARNING - synthesis: Bit 29 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 28 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 27 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 26 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 25 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 24 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 23 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 22 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 21 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 20 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 19 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 18 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 17 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 16 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 15 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 14 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 13 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 12 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 11 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 10 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 9 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 8 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 7 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 6 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 5 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 4 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 3 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 2 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 1 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 0 of Register mult_641_e3 is stuck at Zero
WARNING - synthesis: Bit 9 of Register mult_641_e2 is stuck at Zero
WARNING - synthesis: Bit 8 of Register mult_641_e2 is stuck at Zero
WARNING - synthesis: Bit 7 of Register mult_641_e2 is stuck at Zero
WARNING - synthesis: Bit 6 of Register mult_641_e2 is stuck at Zero
WARNING - synthesis: Bit 5 of Register mult_641_e2 is stuck at Zero
WARNING - synthesis: Bit 4 of Register mult_641_e2 is stuck at Zero
WARNING - synthesis: Bit 3 of Register mult_641_e2 is stuck at Zero
WARNING - synthesis: Bit 2 of Register mult_641_e2 is stuck at Zero
WARNING - synthesis: Bit 1 of Register mult_641_e2 is stuck at Zero
WARNING - synthesis: Bit 0 of Register mult_641_e2 is stuck at Zero
######## Missing driver on net n2115. Patching with GND.
######## Missing driver on net n2114. Patching with GND.
######## Missing driver on net n2113. Patching with GND.
WARNING - synthesis: I/O Port RXD 's net has no driver and is unused.
WARNING - synthesis: I/O Port RxTransmit 's net has no driver and is unused.
WARNING - synthesis: d:/project/ins350_5j_jz/src_al/modulation.v(132): Register \signal_process/modu/c_stair_i0 is stuck at Zero. VDB-5013
WARNING - synthesis: d:/project/ins350_5j_jz/src_al/modulation.v(86): Register \signal_process/modu/dout_reg_e3_e3_i0_i13 is stuck at Zero. VDB-5013
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i0 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i11.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i10 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i0.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i9 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i10.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i8 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i9.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i7 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i8.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i6 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i7.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i5 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i6.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i4 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i5.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i3 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i4.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i2 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i3.
Duplicate register/latch removal. \signal_process/modu/dout_reg_e3_e3_i0_i1 is a one-to-one match with \signal_process/modu/dout_reg_e3_e3_i0_i2.
Applying 200.000000 MHz constraint to all clocks

WARNING - synthesis: No user .sdc file.
Results of NGD DRC are available in INS350_5J_JZ_drc.log.
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/sa5p00/data/sa5plib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/xo2c00/data/xo2clib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/mg5g00/data/mg5glib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/or5g00/data/orc5glib.ngl'...
WARNING - synthesis: logical net 'RXD' has no load.
WARNING - synthesis: input pad net 'RXD' has no legal load.
WARNING - synthesis: logical net 'RxTransmit' has no load.
WARNING - synthesis: input pad net 'RxTransmit' has no legal load.
WARNING - synthesis: DRC complete with 4 warnings.
All blocks are expanded and NGD expansion is successful.
Writing NGD file INS350_5J_JZ_impl1.ngd.

################### Begin Area Report (INS350_5J_JZ)######################
Number of register bits => 971 of 24879 (3 % )
ALU54B => 2
AND2 => 3
BB => 1
CCU2C => 224
EHXPLLL => 1
FD1P3AX => 309
FD1P3BX => 2
FD1P3DX => 182
FD1P3IX => 112
FD1S3AX => 267
FD1S3BX => 3
FD1S3DX => 88
FD1S3IX => 7
FD1S3JX => 1
GSR => 1
IB => 13
INV => 4
LUT4 => 435
MULT18X18D => 5
MUX21 => 1
OB => 18
PDPW16KD => 2
PFUMX => 3
ROM16X1A => 2
XOR2 => 1
################### End Area Report ##################

################### Begin Clock Report ######################
Clock Nets
Number of Clocks: 5
  Net : CLK120/clk120mhz, loads : 796
  Net : CLK120/clk_AD, loads : 179
  Net : wendu/clk_us, loads : 7
  Net : clk_in_c, loads : 1
  Net : CLK120/clk_AD_t_c, loads : 1
Clock Enable Nets
Number of Clock Enables: 29
Top 10 highest fanout Clock Enables:
  Net : signal_process/demodu/clk120mhz_enable_82, loads : 56
  Net : signal_process/demodu/clk120mhz_enable_345, loads : 56
  Net : signal_process/demodu/clk_AD_enable_106, loads : 50
  Net : signal_process/rs422/next_state_3__N_1479, loads : 36
  Net : signal_process/rs422/clk120mhz_enable_258, loads : 32
  Net : wendu/clk120mhz_enable_277, loads : 29
  Net : signal_process/demodu/fifo/wren_i, loads : 21
  Net : signal_process/ctrl_signal/clk120mhz_enable_357, loads : 19
  Net : wendu/clk120mhz_enable_176, loads : 16
  Net : wendu/clk120mhz_enable_161, loads : 16
Highest fanout non-clock nets
Top 10 highest fanout non-clock nets:
  Net : signal_process/ctrl_signal/n7490, loads : 275
  Net : signal_process/demodu/n7488, loads : 60
  Net : clk120mhz_enable_146, loads : 57
  Net : signal_process/demodu/clk120mhz_enable_82, loads : 56
  Net : signal_process/demodu/clk120mhz_enable_345, loads : 56
  Net : signal_process/integ/DA_dout_55__N_1064, loads : 54
  Net : clk_AD_enable_51, loads : 51
  Net : signal_process/rs422/n7960, loads : 50
  Net : signal_process/demodu/clk_AD_enable_106, loads : 50
  Net : signal_process/ctrl_signal/clk120mhz_enable_224, loads : 49
################### End Clock Report ##################

Timing Report Summary
--------------
--------------------------------------------------------------------------------
Constraint                              |   Constraint|       Actual|Levels
--------------------------------------------------------------------------------
                                        |             |             |
create_clock -period 5.000000 -name     |             |             |
clk2 [get_nets clk_us]                  |  200.000 MHz|  248.262 MHz|     3  
                                        |             |             |
create_clock -period 5.000000 -name     |             |             |
clk1 [get_nets clk120mhz]               |  200.000 MHz|   72.939 MHz|    21 *
                                        |             |             |
create_clock -period 5.000000 -name     |             |             |
clk0 [get_nets clk_AD]                  |  200.000 MHz|  101.523 MHz|    10 *
                                        |             |             |
--------------------------------------------------------------------------------


2 <USER> <GROUP> met.


Peak Memory Usage: 131.281  MB

--------------------------------------------------------------
Elapsed CPU time for LSE flow : 5.219  secs
--------------------------------------------------------------
