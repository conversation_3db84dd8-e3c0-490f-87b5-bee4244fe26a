SCUBA, Version Diamond (64-bit) 3.12.1.454
Mon May 12 13:33:51 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

    Issued command   : D:/Software/lscc/diamond/3.12/ispfpga/bin/nt64/scuba -w -bus_exp 7 -bb -arch sa5p00 -type bram -wp 10 -rp 0011 -data_width 1 -num_rows 2 -rdata_width 1 -read_reg1 outreg -gsr DISABLED -reset_rel async -memformat bin -cascade -1 -n pmi_ram_dpEbnonesadr112112p13063b8e -pmi 
    Circuit name     : pmi_ram_dpEbnonesadr112112p13063b8e
    Module type      : RAM_DP
    Module Version   : 6.5
    Ports            : 
	Inputs       : Wr<PERSON><PERSON><PERSON>[0:0], <PERSON><PERSON><PERSON><PERSON>[0:0], Data[0:0], <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, WrClockEn
	Outputs      : Q[0:0]
    I/O buffer       : not inserted
    EDIF output      : pmi_ram_dpEbnonesadr112112p13063b8e.edn
    Bus notation     : big endian
    Report output    : pmi_ram_dpEbnonesadr112112p13063b8e.srp
    Element Usage    :
         DP16KD : 1
    Estimated Resource Usage:
            EBR : 1
