<?xml version="1.0" encoding="UTF-8"?>
<BuildStatus>
    <Strategy name="Strategy1">
        <Milestone name="Export" build_result="0" build_time="0">
            <Task name="IBIS" build_result="0" update_result="2" update_time="1750814189"/>
            <Task name="TimingSimFileVlg" build_result="0" update_result="2" update_time="1750814189"/>
            <Task name="TimingSimFileVHD" build_result="0" update_result="2" update_time="1750814189"/>
            <Task name="Bitgen" build_result="0" update_result="2" update_time="1750814189"/>
            <Task name="Promgen" build_result="0" update_result="2" update_time="1750814189"/>
        </Milestone>
        <Milestone name="Map" build_result="1" build_time="1750814190">
            <Task name="Map" build_result="1" update_result="2" update_time="1750814190"/>
            <Task name="MapTrace" build_result="0" update_result="2" update_time="1750814189"/>
            <Task name="MapVerilogSimFile" build_result="0" update_result="2" update_time="1750814189"/>
            <Task name="MapVHDLSimFile" build_result="0" update_result="2" update_time="1750814189"/>
        </Milestone>
        <Milestone name="PAR" build_result="0" build_time="1750814189">
            <Task name="PAR" build_result="0" update_result="2" update_time="1750814189"/>
            <Task name="PARTrace" build_result="0" update_result="2" update_time="1750814189"/>
            <Task name="IOTiming" build_result="0" update_result="2" update_time="1750814189"/>
        </Milestone>
        <Milestone name="Synthesis" build_result="2" build_time="1750664468">
            <Task name="Synplify_Synthesis" build_result="2" update_result="0" update_time="1750664468"/>
        </Milestone>
        <Milestone name="TOOL_Report" build_result="0" build_time="0">
            <Task name="HDLE" build_result="2" update_result="0" update_time="1750812727"/>
            <Task name="BKM" build_result="0" update_result="2" update_time="1750664447"/>
            <Task name="SSO" build_result="0" update_result="2" update_time="1750664469"/>
            <Task name="PIODRC" build_result="0" update_result="2" update_time="1750664469"/>
            <Task name="DEC" build_result="0" update_result="2" update_time="1750664469"/>
        </Milestone>
        <Milestone name="Translate" build_result="2" build_time="1750664469">
            <Task name="Translate" build_result="2" update_result="0" update_time="1750664469"/>
        </Milestone>
        <Report name=".vdbs/INS350_5J_JZ_impl1_map.vdb" last_build_time="1742287551" last_build_size="359381"/>
        <Report name="INS350_5J_JZ_impl1.bgn" last_build_time="1750734978" last_build_size="4472"/>
        <Report name="INS350_5J_JZ_impl1.bit" last_build_time="1750734978" last_build_size="582677"/>
        <Report name="INS350_5J_JZ_impl1.edi" last_build_time="1750664465" last_build_size="1187475"/>
        <Report name="INS350_5J_JZ_impl1.lsedata" last_build_time="1742284331" last_build_size="1417397"/>
        <Report name="INS350_5J_JZ_impl1.mcs" last_build_time="1742868162" last_build_size="1638935"/>
        <Report name="INS350_5J_JZ_impl1.ncd" last_build_time="1750734968" last_build_size="1359497"/>
        <Report name="INS350_5J_JZ_impl1.ngd" last_build_time="1750664469" last_build_size="1031125"/>
        <Report name="INS350_5J_JZ_impl1.tw1" last_build_time="1750756510" last_build_size="33162"/>
        <Report name="INS350_5J_JZ_impl1.twr" last_build_time="1750734972" last_build_size="261974"/>
        <Report name="INS350_5J_JZ_impl1_map.ncd" last_build_time="1750756508" last_build_size="1024702"/>
    </Strategy>
</BuildStatus>
                                                                                                                                                                                                                                                                                                                       