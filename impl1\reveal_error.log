-- all messages logged in file D:/Project/INS350_5J_J<PERSON> _copy/impl1/reveal_error.log
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/impl1/reveal_workspace/tmpreveal/INS350_5J_JZ_reveal_coretop.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/INS350_5J_JZ.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SignalProcessing.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/speed_select_Tx.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SquareWaveGenerator.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/UART_Control.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/uart_tx.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SignalGenerator.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Ctrl_Data.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/DS18B20.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Integration.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Modulation.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Rs422Output.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/Src_al/Demodulation.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v' (VERI-1482)
-- Analyzing Verilog file 'D:/Project/INS350_5J_JZ _copy/IP_al/global_clock1/global_clock/global_clock.v' (VERI-1482)
D:/Project/INS350_5J_JZ _copy/Src_al/INS350_5J_JZ.v(17): INFO: compiling module 'INS350_5J_JZ' (VERI-1018)
D:/Project/INS350_5J_JZ _copy/Src_al/INS350_5J_JZ.v(147): INFO: elaborating module 'INS350_5J_JZ' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/IP_al/global_clock1/global_clock/global_clock.v(86): INFO: elaborating module 'global_clock_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/DS18B20.v(318): INFO: elaborating module 'DS18B20_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/UART_Control.v(101): INFO: elaborating module 'UART_Control_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SignalProcessing.v(190): INFO: elaborating module 'SignalProcessing_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/speed_select_Tx.v(60): INFO: elaborating module 'speed_select_Tx_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/uart_tx.v(109): INFO: elaborating module 'uart_tx_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Ctrl_Data.v(225): INFO: elaborating module 'Ctrl_Data_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SignalGenerator.v(129): INFO: elaborating module 'SignalGenerator_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Demodulation.v(185): INFO: elaborating module 'Demodulation_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Integration.v(80): INFO: elaborating module 'Integration_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Modulation.v(158): INFO: elaborating module 'Modulation_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Rs422Output.v(233): INFO: elaborating module 'Rs422Output_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SquareWaveGenerator.v(49): INFO: elaborating module 'SquareWaveGenerator_uniq_1' (VERI-9000)
D:/Project/INS350_5J_JZ _copy/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v(809): INFO: elaborating module 'Asys_fifo56X16_uniq_1' (VERI-9000)
-- Pretty printing all design elements in library 'work' to file 'D:/Project/INS350_5J_JZ _copy/impl1/reveal_workspace/tmpreveal/INS350_5J_JZ_rvl.v' (VERI-1491)
