<Project ModBy="Analyzer" Name="D:/Project/INS350_5J_JZ _copy/my_rvl/my_rvl.rvs" Date="2025-04-10">
 <Core Name="INS350_5J_JZ_LA0">
  <Setting>
   <Capture SamplesPerTrig="8192" NumTrigsCap="1"/>
   <Event EventCnt="0" CntEnableRun="0"/>
   <TrigSetting PreTrgSamples="" AND_ALL="0" PostTrgSamples="" TURadix="0"/>
  </Setting>
  <Dataset Name="Base">
   <Trace>
    <Bus Name="DA_DATA" Radix="0">
     <Sig Name="DA_DATA:0"/>
     <Sig Name="DA_DATA:1"/>
     <Sig Name="DA_DATA:2"/>
     <Sig Name="DA_DATA:3"/>
     <Sig Name="DA_DATA:4"/>
     <Sig Name="DA_DATA:5"/>
     <Sig Name="DA_DATA:6"/>
     <Sig Name="DA_DATA:7"/>
     <Sig Name="DA_DATA:8"/>
     <Sig Name="DA_DATA:9"/>
     <Sig Name="DA_DATA:10"/>
     <Sig Name="DA_DATA:11"/>
     <Sig Name="DA_DATA:12"/>
     <Sig Name="DA_DATA:13"/>
    </Bus>
   </Trace>
   <Trigger>
    <TU Operator="0" Name="TU1" ID="1" Value="1" Radix="0"/>
    <TE Enable="1" Expression="TU1" Name="TE1" ID="1"/>
   </Trigger>
  </Dataset>
 </Core>
</Project>
