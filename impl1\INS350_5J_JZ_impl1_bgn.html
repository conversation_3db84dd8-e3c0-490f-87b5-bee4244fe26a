<HTML>
<HEAD><TITLE>Bitgen Report</TITLE>
<STYLE TYPE="text/css">
<!--
 body,pre{
    font-family:'Courier New', monospace;
    color: #000000;
    font-size:88%;
    background-color: #ffffff;
}
h1 {
    font-weight: bold;
    margin-top: 24px;
    margin-bottom: 10px;
    border-bottom: 3px solid #000;    font-size: 1em;
}
h2 {
    font-weight: bold;
    margin-top: 18px;
    margin-bottom: 5px;
    font-size: 0.90em;
}
h3 {
    font-weight: bold;
    margin-top: 12px;
    margin-bottom: 5px;
    font-size: 0.80em;
}
p {
    font-size:78%;
}
P.Table {
    margin-top: 4px;
    margin-bottom: 4px;
    margin-right: 4px;
    margin-left: 4px;
}
table
{
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    border-collapse: collapse;
}
th {
    font-weight:bold;
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    text-align:left;
    font-size:78%;
}
td {
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    font-size:78%;
}
a {
    color:#013C9A;
    text-decoration:none;
}

a:visited {
    color:#013C9A;
}

a:hover, a:active {
    text-decoration:underline;
    color:#5BAFD4;
}
.pass
{
background-color: #00ff00;
}
.fail
{
background-color: #ff0000;
}
.comment
{
    font-size: 90%;
    font-style: italic;
}

-->
</STYLE>
</HEAD>
<PRE><A name="Bgn"></A>BITGEN: Bitstream Generator Diamond (64-bit) 3.12.1.454
Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
Tue Jun 24 11:16:13 2025


Command: bitgen -w -g CfgMode:Disable -g RamCfg:Reset -g DisableUES:FALSE -g ES:No -e -s D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.sec -k D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.bek -gui -msgset D:/Project/INS350_5J_JZ _V2/promote.xml INS350_5J_JZ_impl1.ncd INS350_5J_JZ_impl1.prf 

Loading design for application Bitgen from file INS350_5J_JZ_impl1.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application Bitgen from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.

Running DRC.
DRC detected 0 errors and 0 warnings.
Reading Preference File from INS350_5J_JZ_impl1.prf.

<A name="bgn_ps"></A>
<B><U><big>Preference Summary:</big></U></B>

+---------------------------------+---------------------------------+
|  Preference                     |  Current Setting                |
+---------------------------------+---------------------------------+
|                         RamCfg  |                        Reset**  |
+---------------------------------+---------------------------------+
|                        CfgMode  |                      Disable**  |
+---------------------------------+---------------------------------+
|                        DONE_EX  |                          OFF**  |
+---------------------------------+---------------------------------+
|                        DONE_OD  |                           ON**  |
+---------------------------------+---------------------------------+
|                     MCCLK_FREQ  |                          2.4**  |
+---------------------------------+---------------------------------+
|                  CONFIG_SECURE  |                          OFF**  |
+---------------------------------+---------------------------------+
|                    CONFIG_MODE  |                         JTAG**  |
+---------------------------------+---------------------------------+
|                        WAKE_UP  |                           21**  |
+---------------------------------+---------------------------------+
|                          INBUF  |                          OFF**  |
+---------------------------------+---------------------------------+
|                             ES  |                           No**  |
+---------------------------------+---------------------------------+
|                 SLAVE_SPI_PORT  |                      DISABLE**  |
+---------------------------------+---------------------------------+
|                MASTER_SPI_PORT  |                      DISABLE**  |
+---------------------------------+---------------------------------+
|                COMPRESS_CONFIG  |                          OFF**  |
+---------------------------------+---------------------------------+
|            BACKGROUND_RECONFIG  |                          OFF**  |
+---------------------------------+---------------------------------+
|                     DisableUES  |                        FALSE**  |
+---------------------------------+---------------------------------+
|            SLAVE_PARALLEL_PORT  |                      DISABLE**  |
+---------------------------------+---------------------------------+
|                      DONE_PULL  |                           ON**  |
+---------------------------------+---------------------------------+
|               CONFIG_IOVOLTAGE  |                            3.3  |
+---------------------------------+---------------------------------+
|                        TRANSFR  |                          OFF**  |
+---------------------------------+---------------------------------+
 *  Default setting.
 ** The specified setting matches the default setting.


Creating bit map...
 
Bitstream Status: Final           Version 10.27.
 
Saving bit stream in "INS350_5J_JZ_impl1.bit".
Total CPU Time: 5 secs 
Total REAL Time: 5 secs 
Peak Memory Usage: 290 MB



<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
</PRE></FONT>
</BODY>
</HTML>
