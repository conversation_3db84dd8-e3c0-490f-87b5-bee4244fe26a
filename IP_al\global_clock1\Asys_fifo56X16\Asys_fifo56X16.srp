SCUBA, Version Diamond (64-bit) 3.12.1.454
Thu Mar 13 14:50:25 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

    Issued command   : D:\Software\lscc\diamond\3.12\ispfpga\bin\nt64\scuba.exe -w -n Asys_fifo56X16 -lang verilog -synth synplify -bus_exp 7 -bb -arch sa5p00 -type ebfifo -sync_mode -depth 128 -width 56 -no_enable -pe 10 -pe2 12 -pf 128 -pf2 126 -reset_rel SYNC -fdc D:/Project/INS350_5J_JZ/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.fdc 
    Circuit name     : Asys_fifo56X16
    Module type      : fifoblk
    Module Version   : 5.1
    Ports            : 
	Inputs       : Data[55:0], Clock, WrEn, RdEn, Reset
	Outputs      : Q[55:0], Empty, Full, AlmostEmpty, AlmostFull
    I/O buffer       : not inserted
    EDIF output      : Asys_fifo56X16.edn
    Verilog output   : Asys_fifo56X16.v
    Verilog template : Asys_fifo56X16_tmpl.v
    Verilog testbench: tb_Asys_fifo56X16_tmpl.v
    Verilog purpose  : for synthesis and simulation
    Bus notation     : big endian
    Report output    : Asys_fifo56X16.srp
    Element Usage    :
          CCU2C : 51
           AND2 : 4
        FD1P3DX : 24
        FD1S3BX : 2
        FD1S3DX : 2
            INV : 9
       ROM16X1A : 4
           XOR2 : 1
       PDPW16KD : 2
    Estimated Resource Usage:
            LUT : 111
            EBR : 2
            Reg : 28
