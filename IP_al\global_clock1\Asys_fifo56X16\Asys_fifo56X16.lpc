[Device]
Family=ecp5u
PartType=LFE5U-25F
PartName=LFE5U-25F-7BG256C
SpeedGrade=7
Package=CABGA256
OperatingCondition=COM
Status=S

[IP]
VendorName=Lattice Semiconductor Corporation
CoreType=LPM
CoreStatus=Demo
CoreName=FIFO
CoreRevision=5.1
ModuleName=Asys_fifo56X16
SourceFormat=verilog
ParameterFileVersion=1.0
Date=03/13/2025
Time=14:50:20

[Parameters]
Verilog=1
VHDL=0
EDIF=1
Destination=Synplicity
Expression=BusA(0 to 7)
Order=Big Endian [MSB:LSB]
IO=0
FIFOImp=EBR Based
Depth=128
Width=56
regout=0
CtrlByRdEn=0
EmpFlg=1
PeMode=Static - Dual Threshold
PeAssert=10
PeDeassert=12
FullFlg=1
PfMode=Static - Dual Threshold
PfAssert=128
PfDeassert=126
Reset=Async
Reset1=Sync
RDataCount=0
EnECC=0
EnFWFT=0

[Command]
cmd_line= -w -n Asys_fifo56X16 -lang verilog -synth synplify -bus_exp 7 -bb -arch sa5p00 -type ebfifo -sync_mode -depth 128 -width 56 -no_enable -pe 10 -pe2 12 -pf 128 -pf2 126 -reset_rel SYNC
