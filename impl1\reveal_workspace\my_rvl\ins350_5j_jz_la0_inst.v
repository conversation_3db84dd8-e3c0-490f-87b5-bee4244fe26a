//=============================================================================
// Verilog module generated by IPExpress    05/12/2025    13:33:15         
// Filename: ins350_5j_jz_la0_inst.v                                          
// Copyright(c) 2006 Lattice Semiconductor Corporation. All rights reserved.   
//=============================================================================

/* WARNING - Changes to this file should be performed by re-running IPexpress
or modifying the .LPC file and regenerating the core.  Other changes may lead
to inconsistent simulation and/or implemenation results */

ins350_5j_jz_la0 (
    .clk		(clk),
    .reset_n		(reset_n),
    .jtck		(jtck),
    .jrstn		(jrstn),
    .jce2		(jce2),
    .jtdi		(jtdi),
    .er2_tdo		(er2_tdo),
    .jshift		(jshift),
    .jupdate		(jupdate),
    .trigger_din_0	(trigger_din_0),
    .trace_din		(trace_din),
    .ip_enable    	(ip_enable)
)

object /* synthesis GSR=DISABLED */
object /* synthesis UGROUP=?ins350_5j_jz_la0_core? */
pragma // attribute UGROUP ?ins350_5j_jz_la0_core? 
;

