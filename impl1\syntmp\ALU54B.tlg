Selecting top level module ALU54B
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15356:7:15356:12|Synthesizing module ALU54B in library work.
@W: CG532 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15523:0:15523:6|Within an initial block, only Verilog force statements and memory initialization statements and initialization of entire variable are recognized, and all other content is ignored. Simulation mismatch may occur
@W: CG204 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15523:0:15523:6|Within an initial block, memory initialization statements of entire variable and nested loops are not recognized. Simulation mismatch may occur
@W: CG532 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15570:0:15570:6|Within an initial block, only Verilog force statements and memory initialization statements and initialization of entire variable are recognized, and all other content is ignored. Simulation mismatch may occur
@W: CG204 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15570:0:15570:6|Within an initial block, memory initialization statements of entire variable and nested loops are not recognized. Simulation mismatch may occur
@W: CG296 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17111:14:17111:91|Incomplete sensitivity list; assuming completeness. Make sure all referenced variables in message CG290 are included in the sensitivity list.
@W: CG290 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17120:27:17120:37|Referenced variable rnd_pattern is not in sensitivity list.
@W: CG290 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17121:27:17121:40|Referenced variable rnd_pattern_m1 is not in sensitivity list.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15481:8:15481:18|Object cfb_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15484:8:15484:23|Object input_c_clk_sig0 is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15485:8:15485:23|Object input_c_clk_sig1 is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15486:8:15486:22|Object output0_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15487:8:15487:22|Object output1_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15488:8:15488:19|Object flag_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15489:8:15489:25|Object opcodein_0_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15490:8:15490:25|Object opcodein_1_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15491:8:15491:26|Object opcodeop0_0_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15492:8:15492:26|Object opcodeop0_1_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15493:8:15493:26|Object opcodeop1_0_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15493:29:15493:46|Object opcodeop1_0_ce_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15493:49:15493:67|Object opcodeop1_0_rst_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15494:8:15494:26|Object opcodeop1_1_clk_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15494:29:15494:46|Object opcodeop1_1_ce_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15494:49:15494:67|Object opcodeop1_1_rst_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15513:15:15513:24|Object mask01_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15514:15:15514:23|Object mcpat_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15515:15:15515:25|Object maskpat_sig is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15516:15:15516:25|Object rnd_pattern is declared but not assigned. Either assign a value or remove the declaration.
@W: CG360 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16055:9:16055:15|Removing wire PUR_sig, as there is no assignment to it.
Running optimization stage 1 on ALU54B .......
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17321:4:17321:9|Pruning unused register eqz_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17321:4:17321:9|Pruning unused register eqzm_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17321:4:17321:9|Pruning unused register eqom_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17321:4:17321:9|Pruning unused register eqpatb_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17321:4:17321:9|Pruning unused register eqpat_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17321:4:17321:9|Pruning unused register over_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17321:4:17321:9|Pruning unused register under_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17321:4:17321:9|Pruning unused register overunder_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17295:4:17295:9|Pruning unused register eqz_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17295:4:17295:9|Pruning unused register eqzm_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17295:4:17295:9|Pruning unused register eqom_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17295:4:17295:9|Pruning unused register eqpatb_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17295:4:17295:9|Pruning unused register eqpat_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17295:4:17295:9|Pruning unused register over_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17295:4:17295:9|Pruning unused register under_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17295:4:17295:9|Pruning unused register overunder_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17201:4:17201:9|Pruning unused register r_out_msb_sync[53:18]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17189:4:17189:9|Pruning unused register r_out_msb_async[53:18]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17165:4:17165:9|Pruning unused register r_out_lsb_sync[17:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17153:4:17153:9|Pruning unused register r_out_lsb_async[17:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16840:4:16840:9|Pruning unused register op10_sig_1_sync[2:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16828:4:16828:9|Pruning unused register op10_sig_1_async[2:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16816:4:16816:9|Pruning unused register op7_sig_1_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16804:4:16804:9|Pruning unused register op7_sig_1_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16792:4:16792:9|Pruning unused register opin_sig_1_sync[6:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16780:4:16780:9|Pruning unused register opin_sig_1_async[6:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16718:4:16718:9|Pruning unused register cfb_sync[53:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16706:4:16706:9|Pruning unused register cfb_async[53:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16662:4:16662:9|Pruning unused register op10_sig_0_sync[2:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16650:4:16650:9|Pruning unused register op10_sig_0_async[2:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16638:4:16638:9|Pruning unused register op7_sig_0_sync. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16626:4:16626:9|Pruning unused register op7_sig_0_async. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16614:4:16614:9|Pruning unused register opin_sig_0_sync[6:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16602:4:16602:9|Pruning unused register opin_sig_0_async[6:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16590:4:16590:9|Pruning unused register c_sig_sync1[26:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16578:4:16578:9|Pruning unused register c_sig_async1[26:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16566:4:16566:9|Pruning unused register c_sig_sync0[26:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":16554:4:16554:9|Pruning unused register c_sig_async0[26:0]. Make sure that there are no unused intermediate registers.
@W: CL118 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":17127:7:17127:10|Latch generated from always block for signal r_out[53:0]; possible missing assignment in an if or case statement.
Finished optimization stage 1 on ALU54B (CPU Time 0h:00m:00s, Memory Used current: 121MB peak: 122MB)
Running optimization stage 2 on ALU54B .......
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15388:6:15388:10|Input CFB53 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:6:15391:8|Input CE0 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:10:15391:12|Input CE1 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:14:15391:16|Input CE2 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:18:15391:20|Input CE3 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:22:15391:25|Input CLK0 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:27:15391:30|Input CLK1 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:32:15391:35|Input CLK2 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:37:15391:40|Input CLK3 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:42:15391:45|Input RST0 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:47:15391:50|Input RST1 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:52:15391:55|Input RST2 is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\impl1\syntmp\ALU54B.v":15391:57:15391:60|Input RST3 is unused.
Finished optimization stage 2 on ALU54B (CPU Time 0h:00m:00s, Memory Used current: 133MB peak: 134MB)
