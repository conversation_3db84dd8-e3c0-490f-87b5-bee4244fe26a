(edif pmi_ram_dpEbnonesadr1410102414101024p13ba1268
  (edifVersion 2 0 0)
  (edifLevel 0)
  (keywordMap (keywordLevel 0))
  (status
    (written
      (timestamp 2025 4 2 16 21 4)
      (program "SCUBA" (version "Diamond (64-bit) 3.12.1.454"))))
      (comment "D:/Software/lscc/diamond/3.12/ispfpga/bin/nt64/scuba -w -bus_exp 7 -bb -arch sa5p00 -type bram -wp 10 -rp 0011 -data_width 14 -num_rows 1024 -rdata_width 14 -read_reg1 outreg -gsr DISABLED -reset_rel async -memformat bin -cascade -1 -n pmi_ram_dpEbnonesadr1410102414101024p13ba1268 -pmi ")
  (library ORCLIB
    (edifLevel 0)
    (technology
      (numberDefinition))
    (cell VHI
      (cellType GENERIC)
      (view view1
        (viewType NETLIST)
        (interface
          (port Z
            (direction OUTPUT)))))
    (cell VLO
      (cellType GENERIC)
      (view view1
        (viewType NETLIST)
        (interface
          (port Z
            (direction OUTPUT)))))
    (cell DP16KD
      (cellType GENERIC)
      (view view1
        (viewType NETLIST)
        (interface
          (port DIA17
            (direction INPUT))
          (port DIA16
            (direction INPUT))
          (port DIA15
            (direction INPUT))
          (port DIA14
            (direction INPUT))
          (port DIA13
            (direction INPUT))
          (port DIA12
            (direction INPUT))
          (port DIA11
            (direction INPUT))
          (port DIA10
            (direction INPUT))
          (port DIA9
            (direction INPUT))
          (port DIA8
            (direction INPUT))
          (port DIA7
            (direction INPUT))
          (port DIA6
            (direction INPUT))
          (port DIA5
            (direction INPUT))
          (port DIA4
            (direction INPUT))
          (port DIA3
            (direction INPUT))
          (port DIA2
            (direction INPUT))
          (port DIA1
            (direction INPUT))
          (port DIA0
            (direction INPUT))
          (port ADA13
            (direction INPUT))
          (port ADA12
            (direction INPUT))
          (port ADA11
            (direction INPUT))
          (port ADA10
            (direction INPUT))
          (port ADA9
            (direction INPUT))
          (port ADA8
            (direction INPUT))
          (port ADA7
            (direction INPUT))
          (port ADA6
            (direction INPUT))
          (port ADA5
            (direction INPUT))
          (port ADA4
            (direction INPUT))
          (port ADA3
            (direction INPUT))
          (port ADA2
            (direction INPUT))
          (port ADA1
            (direction INPUT))
          (port ADA0
            (direction INPUT))
          (port CEA
            (direction INPUT))
          (port OCEA
            (direction INPUT))
          (port CLKA
            (direction INPUT))
          (port WEA
            (direction INPUT))
          (port CSA2
            (direction INPUT))
          (port CSA1
            (direction INPUT))
          (port CSA0
            (direction INPUT))
          (port RSTA
            (direction INPUT))
          (port DIB17
            (direction INPUT))
          (port DIB16
            (direction INPUT))
          (port DIB15
            (direction INPUT))
          (port DIB14
            (direction INPUT))
          (port DIB13
            (direction INPUT))
          (port DIB12
            (direction INPUT))
          (port DIB11
            (direction INPUT))
          (port DIB10
            (direction INPUT))
          (port DIB9
            (direction INPUT))
          (port DIB8
            (direction INPUT))
          (port DIB7
            (direction INPUT))
          (port DIB6
            (direction INPUT))
          (port DIB5
            (direction INPUT))
          (port DIB4
            (direction INPUT))
          (port DIB3
            (direction INPUT))
          (port DIB2
            (direction INPUT))
          (port DIB1
            (direction INPUT))
          (port DIB0
            (direction INPUT))
          (port ADB13
            (direction INPUT))
          (port ADB12
            (direction INPUT))
          (port ADB11
            (direction INPUT))
          (port ADB10
            (direction INPUT))
          (port ADB9
            (direction INPUT))
          (port ADB8
            (direction INPUT))
          (port ADB7
            (direction INPUT))
          (port ADB6
            (direction INPUT))
          (port ADB5
            (direction INPUT))
          (port ADB4
            (direction INPUT))
          (port ADB3
            (direction INPUT))
          (port ADB2
            (direction INPUT))
          (port ADB1
            (direction INPUT))
          (port ADB0
            (direction INPUT))
          (port CEB
            (direction INPUT))
          (port OCEB
            (direction INPUT))
          (port CLKB
            (direction INPUT))
          (port WEB
            (direction INPUT))
          (port CSB2
            (direction INPUT))
          (port CSB1
            (direction INPUT))
          (port CSB0
            (direction INPUT))
          (port RSTB
            (direction INPUT))
          (port DOA17
            (direction OUTPUT))
          (port DOA16
            (direction OUTPUT))
          (port DOA15
            (direction OUTPUT))
          (port DOA14
            (direction OUTPUT))
          (port DOA13
            (direction OUTPUT))
          (port DOA12
            (direction OUTPUT))
          (port DOA11
            (direction OUTPUT))
          (port DOA10
            (direction OUTPUT))
          (port DOA9
            (direction OUTPUT))
          (port DOA8
            (direction OUTPUT))
          (port DOA7
            (direction OUTPUT))
          (port DOA6
            (direction OUTPUT))
          (port DOA5
            (direction OUTPUT))
          (port DOA4
            (direction OUTPUT))
          (port DOA3
            (direction OUTPUT))
          (port DOA2
            (direction OUTPUT))
          (port DOA1
            (direction OUTPUT))
          (port DOA0
            (direction OUTPUT))
          (port DOB17
            (direction OUTPUT))
          (port DOB16
            (direction OUTPUT))
          (port DOB15
            (direction OUTPUT))
          (port DOB14
            (direction OUTPUT))
          (port DOB13
            (direction OUTPUT))
          (port DOB12
            (direction OUTPUT))
          (port DOB11
            (direction OUTPUT))
          (port DOB10
            (direction OUTPUT))
          (port DOB9
            (direction OUTPUT))
          (port DOB8
            (direction OUTPUT))
          (port DOB7
            (direction OUTPUT))
          (port DOB6
            (direction OUTPUT))
          (port DOB5
            (direction OUTPUT))
          (port DOB4
            (direction OUTPUT))
          (port DOB3
            (direction OUTPUT))
          (port DOB2
            (direction OUTPUT))
          (port DOB1
            (direction OUTPUT))
          (port DOB0
            (direction OUTPUT)))))
    (cell pmi_ram_dpEbnonesadr1410102414101024p13ba1268
      (cellType GENERIC)
      (view view1
        (viewType NETLIST)
        (interface
          (port (array (rename WrAddress "WrAddress(9:0)") 10)
            (direction INPUT))
          (port (array (rename RdAddress "RdAddress(9:0)") 10)
            (direction INPUT))
          (port (array (rename Data "Data(13:0)") 14)
            (direction INPUT))
          (port WE
            (direction INPUT))
          (port RdClock
            (direction INPUT))
          (port RdClockEn
            (direction INPUT))
          (port Reset
            (direction INPUT))
          (port WrClock
            (direction INPUT))
          (port WrClockEn
            (direction INPUT))
          (port (array (rename Q "Q(13:0)") 14)
            (direction OUTPUT)))
        (property NGD_DRC_MASK (integer 1))
        (contents
          (instance scuba_vhi_inst
            (viewRef view1 
              (cellRef VHI)))
          (instance scuba_vlo_inst
            (viewRef view1 
              (cellRef VLO)))
          (instance pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0
            (viewRef view1 
              (cellRef DP16KD))
            (property INIT_DATA
              (string "STATIC"))
            (property ASYNC_RESET_RELEASE
              (string "ASYNC"))
            (property MEM_LPC_FILE
              (string "pmi_ram_dpEbnonesadr1410102414101024p13ba1268__PMIP__1024__14__14B"))
            (property MEM_INIT_FILE
              (string ""))
            (property CSDECODE_B
              (string "0b000"))
            (property CSDECODE_A
              (string "0b000"))
            (property WRITEMODE_B
              (string "NORMAL"))
            (property WRITEMODE_A
              (string "NORMAL"))
            (property GSR
              (string "ENABLED"))
            (property RESETMODE
              (string "ASYNC"))
            (property REGMODE_B
              (string "OUTREG"))
            (property REGMODE_A
              (string "OUTREG"))
            (property DATA_WIDTH_B
              (string "18"))
            (property DATA_WIDTH_A
              (string "18")))
          (net scuba_vhi
            (joined
              (portRef Z (instanceRef scuba_vhi_inst))
              (portRef ADA1 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef ADA0 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net scuba_vlo
            (joined
              (portRef Z (instanceRef scuba_vlo_inst))
              (portRef CSB2 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef CSA2 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef CSB1 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef CSA1 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef CSB0 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef CSA0 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef WEB (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef ADB3 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef ADA3 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef ADB2 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef ADA2 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef ADB1 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef ADB0 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB17 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIA17 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB16 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIA16 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB15 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIA15 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB14 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIA14 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB13 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB12 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB11 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB10 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB9 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB8 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB7 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB6 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB5 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB4 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB3 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB2 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB1 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef DIB0 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout13
            (joined
              (portRef (member Q 0))
              (portRef DOB13 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout12
            (joined
              (portRef (member Q 1))
              (portRef DOB12 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout11
            (joined
              (portRef (member Q 2))
              (portRef DOB11 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout10
            (joined
              (portRef (member Q 3))
              (portRef DOB10 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout9
            (joined
              (portRef (member Q 4))
              (portRef DOB9 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout8
            (joined
              (portRef (member Q 5))
              (portRef DOB8 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout7
            (joined
              (portRef (member Q 6))
              (portRef DOB7 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout6
            (joined
              (portRef (member Q 7))
              (portRef DOB6 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout5
            (joined
              (portRef (member Q 8))
              (portRef DOB5 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout4
            (joined
              (portRef (member Q 9))
              (portRef DOB4 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout3
            (joined
              (portRef (member Q 10))
              (portRef DOB3 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout2
            (joined
              (portRef (member Q 11))
              (portRef DOB2 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout1
            (joined
              (portRef (member Q 12))
              (portRef DOB1 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net dataout0
            (joined
              (portRef (member Q 13))
              (portRef DOB0 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net WrClockEn
            (joined
              (portRef WrClockEn)
              (portRef OCEA (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef CEA (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net clk
            (joined
              (portRef WrClock)
              (portRef CLKA (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net Reset
            (joined
              (portRef Reset)
              (portRef RSTB (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef RSTA (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net RdClockEn
            (joined
              (portRef RdClockEn)
              (portRef OCEB (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))
              (portRef CEB (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net rdclk
            (joined
              (portRef RdClock)
              (portRef CLKB (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net wren
            (joined
              (portRef WE)
              (portRef WEA (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain13
            (joined
              (portRef (member Data 0))
              (portRef DIA13 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain12
            (joined
              (portRef (member Data 1))
              (portRef DIA12 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain11
            (joined
              (portRef (member Data 2))
              (portRef DIA11 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain10
            (joined
              (portRef (member Data 3))
              (portRef DIA10 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain9
            (joined
              (portRef (member Data 4))
              (portRef DIA9 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain8
            (joined
              (portRef (member Data 5))
              (portRef DIA8 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain7
            (joined
              (portRef (member Data 6))
              (portRef DIA7 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain6
            (joined
              (portRef (member Data 7))
              (portRef DIA6 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain5
            (joined
              (portRef (member Data 8))
              (portRef DIA5 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain4
            (joined
              (portRef (member Data 9))
              (portRef DIA4 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain3
            (joined
              (portRef (member Data 10))
              (portRef DIA3 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain2
            (joined
              (portRef (member Data 11))
              (portRef DIA2 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain1
            (joined
              (portRef (member Data 12))
              (portRef DIA1 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net datain0
            (joined
              (portRef (member Data 13))
              (portRef DIA0 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr9
            (joined
              (portRef (member RdAddress 0))
              (portRef ADB13 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr8
            (joined
              (portRef (member RdAddress 1))
              (portRef ADB12 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr7
            (joined
              (portRef (member RdAddress 2))
              (portRef ADB11 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr6
            (joined
              (portRef (member RdAddress 3))
              (portRef ADB10 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr5
            (joined
              (portRef (member RdAddress 4))
              (portRef ADB9 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr4
            (joined
              (portRef (member RdAddress 5))
              (portRef ADB8 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr3
            (joined
              (portRef (member RdAddress 6))
              (portRef ADB7 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr2
            (joined
              (portRef (member RdAddress 7))
              (portRef ADB6 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr1
            (joined
              (portRef (member RdAddress 8))
              (portRef ADB5 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net raddr0
            (joined
              (portRef (member RdAddress 9))
              (portRef ADB4 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr9
            (joined
              (portRef (member WrAddress 0))
              (portRef ADA13 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr8
            (joined
              (portRef (member WrAddress 1))
              (portRef ADA12 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr7
            (joined
              (portRef (member WrAddress 2))
              (portRef ADA11 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr6
            (joined
              (portRef (member WrAddress 3))
              (portRef ADA10 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr5
            (joined
              (portRef (member WrAddress 4))
              (portRef ADA9 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr4
            (joined
              (portRef (member WrAddress 5))
              (portRef ADA8 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr3
            (joined
              (portRef (member WrAddress 6))
              (portRef ADA7 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr2
            (joined
              (portRef (member WrAddress 7))
              (portRef ADA6 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr1
            (joined
              (portRef (member WrAddress 8))
              (portRef ADA5 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))
          (net waddr0
            (joined
              (portRef (member WrAddress 9))
              (portRef ADA4 (instanceRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268_0_0_0))))))))
  (design pmi_ram_dpEbnonesadr1410102414101024p13ba1268
    (cellRef pmi_ram_dpEbnonesadr1410102414101024p13ba1268
      (libraryRef ORCLIB)))
)
