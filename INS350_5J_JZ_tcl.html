<HTML>
<HEAD><TITLE>La<PERSON>ce TCL Log</TITLE>
<STYLE TYPE="text/css">
<!--
 body,pre{
    font-family:'Courier New', monospace;
    color: #000000;
    font-size:88%;
    background-color: #ffffff;
}
h1 {
    font-weight: bold;
    margin-top: 24px;
    margin-bottom: 10px;
    border-bottom: 3px solid #000;    font-size: 1em;
}
h2 {
    font-weight: bold;
    margin-top: 18px;
    margin-bottom: 5px;
    font-size: 0.90em;
}
h3 {
    font-weight: bold;
    margin-top: 12px;
    margin-bottom: 5px;
    font-size: 0.80em;
}
p {
    font-size:78%;
}
P.Table {
    margin-top: 4px;
    margin-bottom: 4px;
    margin-right: 4px;
    margin-left: 4px;
}
table
{
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    border-collapse: collapse;
}
th {
    font-weight:bold;
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    text-align:left;
    font-size:78%;
}
td {
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    font-size:78%;
}
a {
    color:#013C9A;
    text-decoration:none;
}

a:visited {
    color:#013C9A;
}

a:hover, a:active {
    text-decoration:underline;
    color:#5BAFD4;
}
.pass
{
background-color: #00ff00;
}
.fail
{
background-color: #ff0000;
}
.comment
{
    font-size: 90%;
    font-style: italic;
}

-->
</STYLE>
</HEAD>
<PRE><A name="pn250508172107"></A><B><U><big>pn250508172107</big></U></B>
#Start recording tcl command: 5/8/2025 16:22:33
#Project Location: D:/Project/INS350_5J_JZ _copy; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _copy/INS350_5J_JZ.ldf"
pgr_project close
pgr_project open "D:/Project/INS350_5J_JZ _copy/impl1/impl1.xcf"
pgr_program run
pgr_program run
pgr_project save "D:/Project/INS350_5J_JZ _copy/impl1/impl1.xcf"
prj_project close
#Stop recording: 5/8/2025 17:21:07



<A name="pn250509180817"></A><B><U><big>pn250509180817</big></U></B>
#Start recording tcl command: 5/9/2025 11:14:09
#Project Location: D:/Project/INS350_5J_JZ _copy; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _copy/INS350_5J_JZ.ldf"
prj_run Export -impl impl1
#Stop recording: 5/9/2025 18:08:17



<A name="pn250512095443"></A><B><U><big>pn250512095443</big></U></B>
#Start recording tcl command: 5/12/2025 08:44:29
#Project Location: D:/Project/INS350_5J_JZ _copy; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _copy/INS350_5J_JZ.ldf"
pgr_project open "D:/Project/INS350_5J_JZ _copy/impl1/impl1.xcf"
prj_run Export -impl impl1
pgr_program run
prj_run Export -impl impl1
pgr_program run
pgr_program run
prj_run Export -impl impl1
pgr_program run
prj_run Export -impl impl1
pgr_program run
prj_run Export -impl impl1
pgr_program run
pgr_program run
pgr_program run
prj_run Export -impl impl1
pgr_project save "D:/Project/INS350_5J_JZ _copy/impl1/impl1.xcf"
prj_project close
#Stop recording: 5/12/2025 09:54:43



<A name="pn250521170508"></A><B><U><big>pn250521170508</big></U></B>
#Start recording tcl command: 5/21/2025 10:30:51
#Project Location: D:/Project/INS350_5J_JZ _copy; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _copy/INS350_5J_JZ.ldf"
#Stop recording: 5/21/2025 17:05:08



<A name="pn250523181158"></A><B><U><big>pn250523181158</big></U></B>
#Start recording tcl command: 5/23/2025 10:05:47
#Project Location: D:/Project/INS350_5J_JZ _copy; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _copy/INS350_5J_JZ.ldf"
#Stop recording: 5/23/2025 18:11:58



<A name="pn250526181519"></A><B><U><big>pn250526181519</big></U></B>
#Start recording tcl command: 5/26/2025 10:50:51
#Project Location: D:/Project/INS350_5J_JZ _copy; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _copy/INS350_5J_JZ.ldf"
#Stop recording: 5/26/2025 18:15:19



<A name="pn250528170910"></A><B><U><big>pn250528170910</big></U></B>
#Start recording tcl command: 5/28/2025 08:50:06
#Project Location: D:/Project/INS350_5J_JZ _copy; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _copy/INS350_5J_JZ.ldf"
#Stop recording: 5/28/2025 17:09:10



<A name="pn250609161919"></A><B><U><big>pn250609161919</big></U></B>
#Start recording tcl command: 6/9/2025 16:17:32
#Project Location: D:/Project/INS350_5J_JZ _copy; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _copy/INS350_5J_JZ.ldf"
#Stop recording: 6/9/2025 16:19:19



<A name="pn250624171437"></A><B><U><big>pn250624171437</big></U></B>
#Start recording tcl command: 6/24/2025 11:14:35
#Project Location: D:/Project/INS350_5J_JZ _V2; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.ldf"
prj_run Export -impl impl1
#Stop recording: 6/24/2025 17:14:37



<A name="pn250624183954"></A><B><U><big>pn250624183954</big></U></B>
#Start recording tcl command: 6/24/2025 17:14:49
#Project Location: D:/Project/INS350_5J_JZ _V2; Project name: INS350_5J_JZ
prj_project open "D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.ldf"
prj_run Map -impl impl1
#Stop recording: 6/24/2025 18:39:54



<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
</PRE></FONT>
</BODY>
</HTML>
