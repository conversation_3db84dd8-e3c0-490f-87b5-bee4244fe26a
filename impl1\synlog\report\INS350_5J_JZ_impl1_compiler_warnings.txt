@W: CS141 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":55:34:55:37|Unrecognized synthesis directive keep. Verify the correct directive name.
@W: CS141 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":62:33:62:36|Unrecognized synthesis directive keep. Verify the correct directive name.
@W: CL168 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\global_clock\global_clock.v":23:8:23:21|Removing instance scuba_vhi_inst because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
@W: CL190 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":157:0:157:5|Optimizing register bit dq_out to a constant 0. To keep the instance, apply constraint syn_preserve=1 on the instance.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":157:0:157:5|Pruning unused register dq_out. Make sure that there are no unused intermediate registers.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\speed_select_Tx.v":34:16:34:24|Object uart_ctrl is declared but not assigned. Either assign a value or remove the declaration.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":69:0:69:5|Pruning unused register odd_bit. Make sure that there are no unused intermediate registers.
@W: CG360 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\UART_Control.v":31:16:31:22|Removing wire rx_data, as there is no assignment to it.
@W: CL318 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\UART_Control.v":31:16:31:22|*Output rx_data has undriven bits; assigning undriven bits to 'Z'.  Simulation mismatch possible. Assign all bits of the output.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalGenerator.v":56:16:56:27|Object output_drive is declared but not assigned. Either assign a value or remove the declaration.
@W: CL168 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":177:8:177:12|Removing instance INV_1 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
@W: CL168 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":171:8:171:12|Removing instance INV_4 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
@W: CL168 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":169:9:169:15|Removing instance AND2_t0 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":158:0:158:5|Pruning unused register sample_sum_DY2[55:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":139:0:139:5|Pruning unused register Read_enDY[1:0]. Make sure that there are no unused intermediate registers.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":64:19:64:22|Object step is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":67:20:67:28|Object stair_dy1 is declared but not assigned. Either assign a value or remove the declaration.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":127:0:127:5|Pruning unused register c_stair[13:0]. Make sure that there are no unused intermediate registers.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":142:0:142:5|Pruning unused bits 13 to 0 of DADY_dout[27:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":134:0:134:5|Pruning unused bits 13 to 0 of dout_mult[27:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":194:0:194:5|Pruning unused register sum_dy[55:0]. Make sure that there are no unused intermediate registers.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":208:0:208:5|Pruning unused bits 79 to 56 of p_sum_dy[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":208:0:208:5|Pruning unused bits 23 to 0 of p_sum_dy[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":198:0:198:5|Pruning unused bits 79 to 56 of p_sum[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":198:0:198:5|Pruning unused bits 23 to 0 of p_sum[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CG360 :"D:\Project\INS350_5J_JZ _copy\Src_al\INS350_5J_JZ.v":54:9:54:19|Removing wire TxTransmitt, as there is no assignment to it.
@W: CG360 :"D:\Project\INS350_5J_JZ _copy\Src_al\INS350_5J_JZ.v":61:10:61:18|Removing wire CLKFX_OUT, as there is no assignment to it.
@W: CL247 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":57:22:57:24|Input port bit 55 of din[55:0] is unused
@W: CL279 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":96:0:96:5|Pruning register bits 13 to 12 of square[13:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL279 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":107:0:107:5|Pruning register bits 13 to 12 of square_dy[13:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL279 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":96:0:96:5|Pruning register bits 11 to 1 of square[11:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL279 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":107:0:107:5|Pruning register bits 11 to 1 of square_dy[11:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL246 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":56:28:56:30|Input port bits 55 to 24 of din[55:0] are unused. Assign logic for all port bits or change the input port size.

