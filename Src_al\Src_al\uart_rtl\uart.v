/*************************************************************************
**************************************************************************/

// `define UART_LOOPBACK

module uart
#(
    parameter BAUDRATE = 115200,
    parameter CLK_RATE = 50000000 // Hz
)
(
    input   wire        clk,
    input   wire        rst_n,

    // uart
    input   wire        rx,
    output  wire        tx,
    
    output      [15:0]  cr_para_addr_i,
    output      [15:0]  cr_rapa_wr_data_i,
    output              cr_para_wr_en_i,
    output              cr_para_rd_en_i,
    input               cr_para_ack_o,
    input       [15:0]  cr_para_rd_data_o,
    
    input [31:0]r_reg00,
    input [31:0]r_reg01,
    input [31:0]r_reg02,
    input [31:0]r_reg03,
    input [31:0]r_reg04,
    input [31:0]r_reg05,
    input [31:0]r_reg06,
    input [31:0]r_reg07,
    output [31:0] w_reg00,
    output [31:0] w_reg01,
    output [31:0] w_reg02,
    output [31:0] w_reg03,
    output [31:0] w_reg04,
    output [31:0] w_reg05,
    output [31:0] w_reg06,
    output [31:0] w_reg07    
    //----------------------------------------------

);
    reg    [15:0]  cr_para_addr_i   ;
    reg    [15:0]  cr_rapa_wr_data_i;
    reg            cr_para_wr_en_i  ;
    reg            cr_para_rd_en_i  ;

wire rx_en;
wire [7:0] rx_data;
wire tx_en;
wire [7:0] tx_data;
wire tx_ready;
wire ubus_wen;          // keep synthesis
wire [31:0] ubus_waddr; // keep synthesis
wire [31:0] ubus_wdata; // keep synthesis
wire ubus_ren;
wire [31:0] ubus_raddr;


reg  ubus_bken;
reg [31:0] ubus_rdata;

uart_rx_al
#(
    .BAUDRATE   (BAUDRATE       ),
    .CLK_RATE   (CLK_RATE       )
)
u_uart_rx
(
    .clk        (clk            ),
    .rx         (rx             ),
    .rx_en      (rx_en          ),
    .rx_data    (rx_data        )
);

uart_tx_al
#(
    .BAUDRATE   (BAUDRATE       ),
    .CLK_RATE   (CLK_RATE       )
)
u_uart_tx
(
    .clk        (clk            ),
    .tx         (tx             ),
    
//`ifdef UART_LOOPBACK // ���ڻػ�����
//    .tx_en      (rx_en          ),
//    .tx_data    (rx_data        ),
//`else
    .tx_en      (tx_en          ),
    .tx_data    (tx_data        ),
//`endif

    .tx_ready   (tx_ready       )
);

// ����ͨ��Э��
uprotocol u_uprotocol
(
    .clk        (clk            ),
    .rst_n      (rst_n          ),
    // uart-rx
    .rx_en      (rx_en          ),
    .rx_data    (rx_data        ),
    
    // uart-tx
    .tx_en      (tx_en          ),
    .tx_data    (tx_data        ),
    .tx_ready   (tx_ready       ),
    
    // ubus write
    .ubus_wen   (ubus_wen       ),
    .ubus_waddr (ubus_waddr     ),
    .ubus_wdata (ubus_wdata     ),
    
    // ubus read
    .ubus_ren   (ubus_ren       ),
    .ubus_raddr (ubus_raddr     ),
    .ubus_bken  (ubus_bken      ),
    .ubus_rdata (ubus_rdata     )
);


reg       [3:0] cri_state;
parameter       cri_idle=4'd0;
parameter       cri_wait=4'd1;
parameter       cri_write=4'd2;
parameter       cri_read=4'd3;

reg  [31:0]     w_data_error;


always@(posedge clk)
begin
    if(!rst_n)
        cri_state<=cri_idle;
    else
    begin
    case(cri_state)
    cri_idle:if(cr_para_ack_o==0)
                cri_state<=cri_wait;
             else
                cri_state<=cri_idle;
    cri_wait:if(ubus_ren && ubus_raddr[16] == 1'b0)
                    cri_state<=cri_read;    
             else   if(ubus_wen && ubus_waddr[16] == 1'b0)
                    cri_state<=cri_write;   
             else
                cri_state<=cri_wait;
    cri_write:if(cr_para_ack_o==1)
                    cri_state<=cri_wait;
              else
                    cri_state<=cri_write;
    cri_read:if(cr_para_ack_o==1)
                    cri_state<=cri_wait;
              else
                    cri_state<=cri_read;
    default:cri_state<=cri_idle;
    endcase
    end 
end


always@(posedge clk)
begin
    if(cri_state==cri_wait)
        if(ubus_ren && ubus_raddr[16] == 1'b0)
            begin
                cr_para_rd_en_i<=1;
                cr_para_wr_en_i<=0;
                cr_para_addr_i<=ubus_raddr[15:0];
                cr_rapa_wr_data_i<=16'd0;
            end
        else if(ubus_wen && ubus_waddr[16] == 1'b0)
            begin
                cr_para_rd_en_i<=0;
                cr_para_wr_en_i<=1;
                cr_para_addr_i<=ubus_waddr[15:0];
                cr_rapa_wr_data_i<=ubus_wdata[15:0];
            end
        else
        begin
            cr_para_rd_en_i<=0;
            cr_para_wr_en_i<=0;
        end
    else
        begin
            cr_para_rd_en_i<=0;
            cr_para_wr_en_i<=0;
        end
end


//======================================================================
// 串口读数据
always@(posedge clk) begin
    if(ubus_ren && ubus_raddr[16] == 1'b1)
                case(ubus_raddr[16:0])
                17'h10000:  ubus_rdata <= r_reg00;
                17'h10001:  ubus_rdata <= r_reg01;
                17'h10002:  ubus_rdata <= r_reg02;
                17'h10003:  ubus_rdata <= r_reg03;
                17'h10004:  ubus_rdata <= r_reg04;
                17'h10005:  ubus_rdata <= r_reg05;
                17'h10006:  ubus_rdata <= r_reg06;
                17'h10007:  ubus_rdata <= r_reg07;                
                default:    ubus_rdata <= 32'd0;
                endcase
    else if(cri_state==cri_read&&cr_para_ack_o)
        begin
            ubus_rdata<={16'd0,cr_para_rd_data_o};
        end
        else  begin 
            ubus_rdata<=ubus_rdata;
        end 

end


//======================================================================
// 串口写数据
reg [31:0] w_reg00;
reg [31:0] w_reg01;
reg [31:0] w_reg02;
reg [31:0] w_reg03;
reg [31:0] w_reg04;
reg [31:0] w_reg05;
reg [31:0] w_reg06;
reg [31:0] w_reg07;



always@(posedge clk) begin
    if( ubus_wen==1'b1 && ubus_waddr[16]==1'b1)begin
         case(ubus_waddr[16:0])
             17'h10000:  w_reg00 <= ubus_wdata   ;
             17'h10001:  w_reg01 <= ubus_wdata   ;
             17'h10002:  w_reg02 <= ubus_wdata   ;
             17'h10003:  w_reg03 <= ubus_wdata   ;
             17'h10004:  w_reg04 <= ubus_wdata   ;
             17'h10005:  w_reg05 <= ubus_wdata   ;
             17'h10006:  w_reg06 <= ubus_wdata   ;
             17'h10007:  w_reg07 <= ubus_wdata   ;

           endcase
      end
                
end

//========================================================================
// 串口数据返回(根据实际数据返回时间,调节对应延时)
 
always@(posedge clk)
begin
    if(cri_state==cri_read&&cr_para_ack_o)
        begin
            ubus_bken <= 1;
//          ubus_rdata<={16'd0,cr_para_rd_data_o};
        end
    else
        begin
            ubus_bken <= ubus_ren;
//          ubus_rdata<=ubus_rdata;
        end
end


endmodule
