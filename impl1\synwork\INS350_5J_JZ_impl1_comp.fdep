#OPTIONS:"|-layerid|0|-orig_srs|D:\\Project\\INS350_5J_JZ _copy\\impl1\\synwork\\INS350_5J_JZ_impl1_comp.srs|-top|INS350_5J_JZ|-prodtype|synplify_pro|-dspmac|-fixsmult|-infer_seqShift|-nram|-sdff_counter|-divnmod|-nostructver|-I|D:\\Project\\INS350_5J_JZ _copy|-I|D:\\Project\\INS350_5J_JZ _copy\\impl1\\|-I|D:\\Software\\lscc\\diamond\\3.12\\synpbase\\lib|-v2001|-devicelib|D:\\Software\\lscc\\diamond\\3.12\\synpbase\\lib\\lucent\\ecp5u.v|-devicelib|D:\\Software\\lscc\\diamond\\3.12\\synpbase\\lib\\lucent\\pmi_def.v|-encrypt|-pro|-ui|-fid2|-ram|-sharing|on|-ll|2000|-autosm|-D_MULTIPLE_FILE_COMPILATION_UNIT_|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001|-lib|work|-fv2001"
#CUR:"D:\\Software\\lscc\\diamond\\3.12\\synpbase\\bin64\\c_ver.exe":1640161271
#CUR:"D:\\Software\\lscc\\diamond\\3.12\\synpbase\\lib\\lucent\\ecp5u.v":1640161406
#CUR:"D:\\Software\\lscc\\diamond\\3.12\\synpbase\\lib\\lucent\\pmi_def.v":1640161408
#CUR:"D:\\Software\\lscc\\diamond\\3.12\\synpbase\\lib\\vlog\\hypermods.v":1640160580
#CUR:"D:\\Software\\lscc\\diamond\\3.12\\synpbase\\lib\\vlog\\umr_capim.v":1640160580
#CUR:"D:\\Software\\lscc\\diamond\\3.12\\synpbase\\lib\\vlog\\scemi_objects.v":1640160580
#CUR:"D:\\Software\\lscc\\diamond\\3.12\\synpbase\\lib\\vlog\\scemi_pipes.svh":1640160580
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\INS350_5J_JZ.v":1750664408
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\SignalProcessing.v":1689057255
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\speed_select_Tx.v":1685017348
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\SquareWaveGenerator.v":1681552246
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\UART_Control.v":1684217355
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\uart_tx.v":1685017355
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\SignalGenerator.v":1702292523
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\Ctrl_Data.v":1746599164
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\DS18B20.v":1685017368
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\Integration.v":1702263016
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\Modulation.v":1749188399
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Src_al\\Rs422Output.v":1702984130
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\Src_al\\Demodulation.v":1742461147
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\IP_al\\global_clock1\\Asys_fifo56X16\\Asys_fifo56X16.v":1741848625
#CUR:"D:\\Project\\INS350_5J_JZ _copy\\IP_al\\global_clock1\\global_clock\\global_clock.v":1741855944
0			"D:\Project\INS350_5J_JZ _copy\Src_al\INS350_5J_JZ.v" verilog
1			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalProcessing.v" verilog
2			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\speed_select_Tx.v" verilog
3			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SquareWaveGenerator.v" verilog
4			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\UART_Control.v" verilog
5			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v" verilog
6			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalGenerator.v" verilog
7			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v" verilog
8			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v" verilog
9			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Integration.v" verilog
10			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v" verilog
11			"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v" verilog
12			"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v" verilog
13			"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v" verilog
14			"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\global_clock\global_clock.v" verilog
#Dependency Lists(Uses List)
0 1 4 8 14
1 3 11 10 9 12 6
2 -1
3 -1
4 7 5 2
5 -1
6 -1
7 -1
8 -1
9 -1
10 -1
11 -1
12 13
13 -1
14 -1
#Dependency Lists(Users Of)
0 -1
1 0
2 4
3 1
4 0
5 4
6 1
7 4
8 0
9 1
10 1
11 1
12 1
13 12
14 0
#Design Unit to File Association
module work Asys_fifo56X16 13
module work Ctrl_Data 7
module work uart_tx 5
module work speed_select_Tx 2
module work SquareWaveGenerator 3
module work Rs422Output 11
module work Modulation 10
module work Integration 9
module work Demodulation 12
module work SignalGenerator 6
module work SignalProcessing 1
module work UART_Control 4
module work DS18B20 8
module work global_clock 14
module work INS350_5J_JZ 0
