<!DOCTYPE global_clock1>
<lattice:project>
    <spirit:component>
        <spirit:vendor>LATTICE</spirit:vendor>
        <spirit:library>LOCAL</spirit:library>
        <spirit:name>global_clock1</spirit:name>
        <spirit:version>1.0</spirit:version>
        <spirit:fileSets>
            <spirit:fileset>
                <spirit:name>Diamond_Synthesis</spirit:name>
                <spirit:group>synthesis</spirit:group>
            </spirit:fileset>
            <spirit:fileset>
                <spirit:name>Diamond_Simulation</spirit:name>
                <spirit:group>simulation</spirit:group>
            </spirit:fileset>
        </spirit:fileSets>
        <spirit:componentGenerators/>
        <spirit:model>
            <spirit:views/>
            <spirit:ports>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_AlmostEmpty</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_AlmostEmpty</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>out</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.AlmostEmpty</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_AlmostFull</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_AlmostFull</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>out</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.AlmostFull</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_Clock</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_Clock</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>in</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.Clock</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_Empty</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_Empty</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>out</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.Empty</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_Full</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_Full</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>out</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.Full</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_RdEn</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_RdEn</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>in</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.RdEn</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_Reset</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_Reset</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>in</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.Reset</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_WrEn</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_WrEn</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>in</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.WrEn</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>global_clock_CLKI</spirit:name>
                    <spirit:displayName>global_clock_CLKI</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>in</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">global_clock.CLKI</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>global_clock_CLKOP</spirit:name>
                    <spirit:displayName>global_clock_CLKOP</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>out</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">global_clock.CLKOP</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>global_clock_CLKOS</spirit:name>
                    <spirit:displayName>global_clock_CLKOS</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>out</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">global_clock.CLKOS</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>global_clock_CLKOS2</spirit:name>
                    <spirit:displayName>global_clock_CLKOS2</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>out</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">global_clock.CLKOS2</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>global_clock_LOCK</spirit:name>
                    <spirit:displayName>global_clock_LOCK</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>out</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">global_clock.LOCK</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>global_clock_RST</spirit:name>
                    <spirit:displayName>global_clock_RST</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>in</spirit:direction>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">global_clock.RST</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_Data</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_Data</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>in</spirit:direction>
                        <spirit:vector>
                            <spirit:left>55</spirit:left>
                            <spirit:right>0</spirit:right>
                        </spirit:vector>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.Data</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
                <spirit:port>
                    <spirit:name>Asys_fifo56X16_Q</spirit:name>
                    <spirit:displayName>Asys_fifo56X16_Q</spirit:displayName>
                    <spirit:wire>
                        <spirit:direction>out</spirit:direction>
                        <spirit:vector>
                            <spirit:left>55</spirit:left>
                            <spirit:right>0</spirit:right>
                        </spirit:vector>
                    </spirit:wire>
                    <spirit:vendorExtensions>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="exportFrom">Asys_fifo56X16.Q</lattice:attribute>
                        </lattice:attributes>
                    </spirit:vendorExtensions>
                </spirit:port>
            </spirit:ports>
        </spirit:model>
        <spirit:vendorExtensions>
            <lattice:device>LFE5U-25F-7BG256C</lattice:device>
            <lattice:synthesis>lse</lattice:synthesis>
            <lattice:date>2025-03-13.14:31:13</lattice:date>
            <lattice:modified>2025-03-13.16:53:06</lattice:modified>
            <lattice:diamond>3.12.1.454</lattice:diamond>
            <lattice:language>Verilog</lattice:language>
            <lattice:attributes>
                <lattice:attribute lattice:name="AddComponent">true</lattice:attribute>
                <lattice:attribute lattice:name="Change4to5">false</lattice:attribute>
                <lattice:attribute lattice:name="ChangeConfig">false</lattice:attribute>
                <lattice:attribute lattice:name="ChangeConnect">true</lattice:attribute>
                <lattice:attribute lattice:name="ChangeDevice">false</lattice:attribute>
                <lattice:attribute lattice:name="ChangeLocate">false</lattice:attribute>
                <lattice:attribute lattice:name="ChangePack">false</lattice:attribute>
                <lattice:attribute lattice:name="ChangePart">false</lattice:attribute>
                <lattice:attribute lattice:name="ChangeSynthesis">true</lattice:attribute>
                <lattice:attribute lattice:name="Migrate">false</lattice:attribute>
                <lattice:attribute lattice:name="RemovedComponent">false</lattice:attribute>
            </lattice:attributes>
            <lattice:elements/>
            <lattice:lpc/>
            <lattice:groups/>
        </spirit:vendorExtensions>
    </spirit:component>
    <spirit:design>
        <spirit:vendor>LATTICE</spirit:vendor>
        <spirit:library>LOCAL</spirit:library>
        <spirit:name>global_clock1</spirit:name>
        <spirit:version>1.0</spirit:version>
        <spirit:componentInstances>
            <spirit:componentInstance>
                <spirit:instanceName>Asys_fifo56X16</spirit:instanceName>
                <spirit:componentRef>
                    <spirit:vendor>Lattice Semiconductor Corporation</spirit:vendor>
                    <spirit:library>LEGACY</spirit:library>
                    <spirit:name>FIFO</spirit:name>
                    <spirit:version>5.1</spirit:version>
                    <spirit:fileSets>
                        <spirit:fileset>
                            <spirit:name>Diamond_Simulation</spirit:name>
                            <spirit:group>simulation</spirit:group>
                            <spirit:file>
                                <spirit:name>./Asys_fifo56X16/Asys_fifo56X16.v</spirit:name>
                                <spirit:fileType>verilogSource</spirit:fileType>
                            </spirit:file>
                        </spirit:fileset>
                        <spirit:fileset>
                            <spirit:name>Diamond_Synthesis</spirit:name>
                            <spirit:group>synthesis</spirit:group>
                            <spirit:file>
                                <spirit:name>./Asys_fifo56X16/Asys_fifo56X16.v</spirit:name>
                                <spirit:fileType>verilogSource</spirit:fileType>
                            </spirit:file>
                        </spirit:fileset>
                    </spirit:fileSets>
                    <spirit:componentGenerators>
                        <spirit:componentGenerator spirit:hidden="true" spirit:scope="instance">
                            <spirit:name>Configuration</spirit:name>
                            <spirit:apiType>none</spirit:apiType>
                            <spirit:generatorExe>${sbp_path}/${instance}/generate_core.tcl</spirit:generatorExe>
                            <spirit:group>CONFIG</spirit:group>
                        </spirit:componentGenerator>
                        <spirit:componentGenerator spirit:hidden="true" spirit:scope="instance">
                            <spirit:name>CreateNGD</spirit:name>
                            <spirit:apiType>none</spirit:apiType>
                            <spirit:generatorExe>${sbp_path}/${instance}/generate_ngd.tcl</spirit:generatorExe>
                            <spirit:group>CONFIG</spirit:group>
                        </spirit:componentGenerator>
                    </spirit:componentGenerators>
                    <spirit:model>
                        <spirit:views/>
                        <spirit:ports>
                            <spirit:port>
                                <spirit:name>AlmostEmpty</spirit:name>
                                <spirit:displayName>AlmostEmpty</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>out</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>AlmostFull</spirit:name>
                                <spirit:displayName>AlmostFull</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>out</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>Clock</spirit:name>
                                <spirit:displayName>Clock</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>in</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>Empty</spirit:name>
                                <spirit:displayName>Empty</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>out</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>Full</spirit:name>
                                <spirit:displayName>Full</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>out</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>RdEn</spirit:name>
                                <spirit:displayName>RdEn</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>in</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>Reset</spirit:name>
                                <spirit:displayName>Reset</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>in</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>WrEn</spirit:name>
                                <spirit:displayName>WrEn</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>in</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>Data</spirit:name>
                                <spirit:displayName>Data</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>in</spirit:direction>
                                    <spirit:vector>
                                        <spirit:left>55</spirit:left>
                                        <spirit:right>0</spirit:right>
                                    </spirit:vector>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>Q</spirit:name>
                                <spirit:displayName>Q</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>out</spirit:direction>
                                    <spirit:vector>
                                        <spirit:left>55</spirit:left>
                                        <spirit:right>0</spirit:right>
                                    </spirit:vector>
                                </spirit:wire>
                            </spirit:port>
                        </spirit:ports>
                    </spirit:model>
                    <spirit:vendorExtensions>
                        <lattice:synthesis>lse</lattice:synthesis>
                        <lattice:modified>2025-03-13.16:53:06</lattice:modified>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="AddComponent">false</lattice:attribute>
                            <lattice:attribute lattice:name="BBox">false</lattice:attribute>
                            <lattice:attribute lattice:name="Change4to5">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeConfig">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeConnect">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeDevice">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeLocate">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangePack">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangePart">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeSynthesis">false</lattice:attribute>
                            <lattice:attribute lattice:name="CoreType">LPM</lattice:attribute>
                            <lattice:attribute lattice:name="DCU_RXREFCLK">PRIMARY</lattice:attribute>
                            <lattice:attribute lattice:name="DCU_TXREFCLK">PRIMARY</lattice:attribute>
                            <lattice:attribute lattice:name="Migrate">false</lattice:attribute>
                            <lattice:attribute lattice:name="RemovedComponent">false</lattice:attribute>
                        </lattice:attributes>
                        <lattice:elements/>
                        <lattice:lpc>
                            <lattice:lpcsection lattice:name="Device"/>
                            <lattice:lpcentry>
                                <lattice:lpckey>Family</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">ecp5u</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>OperatingCondition</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">COM</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Package</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">CABGA256</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PartName</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">LFE5U-25F-7BG256C</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PartType</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">LFE5U-25F</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>SpeedGrade</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">7</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Status</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">S</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcsection lattice:name="IP"/>
                            <lattice:lpcentry>
                                <lattice:lpckey>CoreName</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">FIFO</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CoreRevision</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">5.1</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CoreStatus</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Demo</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CoreType</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">LPM</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Date</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">03/13/2025</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>ModuleName</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Asys_fifo56X16</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>ParameterFileVersion</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1.0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>SourceFormat</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">verilog</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Time</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">14:50:20</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>VendorName</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Lattice Semiconductor Corporation</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcsection lattice:name="Parameters"/>
                            <lattice:lpcentry>
                                <lattice:lpckey>CtrlByRdEn</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Depth</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">128</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Destination</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Synplicity</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>EDIF</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>EmpFlg</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>EnECC</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>EnFWFT</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Expression</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">BusA(0 to 7)</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>FIFOImp</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">EBR Based</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>FullFlg</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>IO</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Order</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Big Endian [MSB:LSB]</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PeAssert</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">10</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PeDeassert</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">12</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PeMode</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Static - Dual Threshold</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PfAssert</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">128</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PfDeassert</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">126</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PfMode</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Static - Dual Threshold</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>RDataCount</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Reset</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Async</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Reset1</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Sync</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>VHDL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Verilog</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Width</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">56</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>regout</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcsection lattice:name="Command"/>
                            <lattice:lpcentry>
                                <lattice:lpckey>cmd_line</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">-w -n Asys_fifo56X16 -lang verilog -synth synplify -bus_exp 7 -bb -arch sa5p00 -type ebfifo -sync_mode -depth 128 -width 56 -no_enable -pe 10 -pe2 12 -pf 128 -pf2 126 -reset_rel SYNC</lattice:lpcvalue>
                            </lattice:lpcentry>
                        </lattice:lpc>
                        <lattice:groups/>
                    </spirit:vendorExtensions>
                </spirit:componentRef>
            </spirit:componentInstance>
            <spirit:componentInstance>
                <spirit:instanceName>global_clock</spirit:instanceName>
                <spirit:componentRef>
                    <spirit:vendor>Lattice Semiconductor Corporation</spirit:vendor>
                    <spirit:library>LEGACY</spirit:library>
                    <spirit:name>PLL</spirit:name>
                    <spirit:version>5.8</spirit:version>
                    <spirit:fileSets>
                        <spirit:fileset>
                            <spirit:name>Diamond_Simulation</spirit:name>
                            <spirit:group>simulation</spirit:group>
                            <spirit:file>
                                <spirit:name>./global_clock/global_clock.v</spirit:name>
                                <spirit:fileType>verilogSource</spirit:fileType>
                            </spirit:file>
                        </spirit:fileset>
                        <spirit:fileset>
                            <spirit:name>Diamond_Synthesis</spirit:name>
                            <spirit:group>synthesis</spirit:group>
                            <spirit:file>
                                <spirit:name>./global_clock/global_clock.v</spirit:name>
                                <spirit:fileType>verilogSource</spirit:fileType>
                            </spirit:file>
                        </spirit:fileset>
                    </spirit:fileSets>
                    <spirit:componentGenerators>
                        <spirit:componentGenerator spirit:hidden="true" spirit:scope="instance">
                            <spirit:name>Configuration</spirit:name>
                            <spirit:apiType>none</spirit:apiType>
                            <spirit:generatorExe>${sbp_path}/${instance}/generate_core.tcl</spirit:generatorExe>
                            <spirit:group>CONFIG</spirit:group>
                        </spirit:componentGenerator>
                        <spirit:componentGenerator spirit:hidden="true" spirit:scope="instance">
                            <spirit:name>CreateNGD</spirit:name>
                            <spirit:apiType>none</spirit:apiType>
                            <spirit:generatorExe>${sbp_path}/${instance}/generate_ngd.tcl</spirit:generatorExe>
                            <spirit:group>CONFIG</spirit:group>
                        </spirit:componentGenerator>
                        <spirit:componentGenerator spirit:hidden="true" spirit:scope="instance">
                            <spirit:name>Generation</spirit:name>
                            <spirit:apiType>none</spirit:apiType>
                            <spirit:generatorExe>${sbp_path}/${instance}/generate_core.tcl</spirit:generatorExe>
                            <spirit:group>GENERATE</spirit:group>
                        </spirit:componentGenerator>
                    </spirit:componentGenerators>
                    <spirit:model>
                        <spirit:views/>
                        <spirit:ports>
                            <spirit:port>
                                <spirit:name>CLKI</spirit:name>
                                <spirit:displayName>CLKI</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>in</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>CLKOP</spirit:name>
                                <spirit:displayName>CLKOP</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>out</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>CLKOS</spirit:name>
                                <spirit:displayName>CLKOS</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>out</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>CLKOS2</spirit:name>
                                <spirit:displayName>CLKOS2</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>out</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>LOCK</spirit:name>
                                <spirit:displayName>LOCK</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>out</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                            <spirit:port>
                                <spirit:name>RST</spirit:name>
                                <spirit:displayName>RST</spirit:displayName>
                                <spirit:wire>
                                    <spirit:direction>in</spirit:direction>
                                </spirit:wire>
                            </spirit:port>
                        </spirit:ports>
                    </spirit:model>
                    <spirit:vendorExtensions>
                        <lattice:synthesis>lse</lattice:synthesis>
                        <lattice:modified>2025-03-13.16:53:06</lattice:modified>
                        <lattice:attributes>
                            <lattice:attribute lattice:name="AddComponent">false</lattice:attribute>
                            <lattice:attribute lattice:name="BBox">false</lattice:attribute>
                            <lattice:attribute lattice:name="Change4to5">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeConfig">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeConnect">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeDevice">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeLocate">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangePack">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangePart">false</lattice:attribute>
                            <lattice:attribute lattice:name="ChangeSynthesis">false</lattice:attribute>
                            <lattice:attribute lattice:name="CoreType">LPM</lattice:attribute>
                            <lattice:attribute lattice:name="DCU_RXREFCLK">PRIMARY</lattice:attribute>
                            <lattice:attribute lattice:name="DCU_TXREFCLK">PRIMARY</lattice:attribute>
                            <lattice:attribute lattice:name="Migrate">false</lattice:attribute>
                            <lattice:attribute lattice:name="RemovedComponent">false</lattice:attribute>
                        </lattice:attributes>
                        <lattice:elements/>
                        <lattice:lpc>
                            <lattice:lpcsection lattice:name="Device"/>
                            <lattice:lpcentry>
                                <lattice:lpckey>Family</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">ecp5u</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>OperatingCondition</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">COM</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Package</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">CABGA256</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PartName</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">LFE5U-25F-7BG256C</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PartType</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">LFE5U-25F</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>SpeedGrade</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">7</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Status</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">S</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcsection lattice:name="IP"/>
                            <lattice:lpcentry>
                                <lattice:lpckey>CoreName</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">PLL</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CoreRevision</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">5.8</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CoreStatus</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Demo</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CoreType</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">LPM</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Date</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">03/13/2025</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>ModuleName</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">global_clock</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>ParameterFileVersion</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1.0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>SourceFormat</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">verilog</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Time</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">16:52:22</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>VendorName</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Lattice Semiconductor Corporation</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcsection lattice:name="Parameters"/>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKFB_DIV</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">6</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKI_DIV</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKI_FREQ</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">20.00</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOP_ACTUAL_FREQ</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">120.000000</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOP_APHASE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0.00</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOP_DIV</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">5</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOP_DPHASE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOP_FREQ</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">120.00</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOP_MUXA</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOP_TOL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0.0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOP_TRIM_DELAY</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOP_TRIM_POL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Rising</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_ACTUAL_FREQ</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">60.000000</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_APHASE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0.00</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_DIV</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">10</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_DPHASE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_Enable</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">ENABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_FREQ</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">60.00</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_MUXC</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_TOL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0.0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_TRIM_DELAY</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS2_TRIM_POL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Rising</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_ACTUAL_FREQ</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant"></lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_APHASE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0.00</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_DIV</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_DPHASE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_Enable</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_FREQ</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">100.00</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_MUXD</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_TOL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0.0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_TRIM_DELAY</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS3_TRIM_POL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Rising</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_ACTUAL_FREQ</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">60.000000</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_APHASE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0.00</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_DIV</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">10</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_DPHASE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_Enable</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">ENABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_FREQ</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">60.00</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_MUXB</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_TOL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0.0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_TRIM_DELAY</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKOS_TRIM_POL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Rising</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>CLKSEL_ENA</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>DPHASE_SOURCE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">STATIC</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Destination</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Synplicity</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>EDIF</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>ENABLE_CLKOP</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>ENABLE_CLKOS</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>ENABLE_CLKOS2</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>ENABLE_CLKOS3</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>ENABLE_HBW</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Expression</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">BusA(0 to 7)</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>FEEDBK_PATH</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">CLKOP</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>FRACN_DIV</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant"></lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>FRACN_ENABLE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>IO</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>IOBUF</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">LVDS</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Order</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">Big Endian [MSB:LSB]</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PLLRST_ENA</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">ENABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PLL_BW</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">2.292</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PLL_LOCK_MODE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">ENABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PLL_LOCK_STK</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">ENABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>PLL_USE_SMI</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>REFERENCE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>STDBY_ENABLE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">DISABLED</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>VCO_RATE</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">600.000</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>VHDL</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">0</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcentry>
                                <lattice:lpckey>Verilog</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">1</lattice:lpcvalue>
                            </lattice:lpcentry>
                            <lattice:lpcsection lattice:name="Command"/>
                            <lattice:lpcentry>
                                <lattice:lpckey>cmd_line</lattice:lpckey>
                                <lattice:lpcvalue lattice:resolve="constant">-w -n global_clock -lang verilog -synth lse -bus_exp 7 -bb -arch sa5p00 -type pll -fin 20.00 -fclkop 120.00 -fclkop_tol 0.0 -fclkos 60.00 -fclkos_tol 0.0 -phases 0 -fclkos2 60.00 -fclkos2_tol 0.0 -phases2 0 -phase_cntl STATIC -rst -lock -sticky -fb_mode 1</lattice:lpcvalue>
                            </lattice:lpcentry>
                        </lattice:lpc>
                        <lattice:groups/>
                    </spirit:vendorExtensions>
                </spirit:componentRef>
            </spirit:componentInstance>
        </spirit:componentInstances>
        <spirit:adHocConnections>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_AlmostEmpty</spirit:name>
                <spirit:displayName>Asys_fifo56X16_AlmostEmpty</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="AlmostEmpty" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_AlmostEmpty"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_AlmostFull</spirit:name>
                <spirit:displayName>Asys_fifo56X16_AlmostFull</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="AlmostFull" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_AlmostFull"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Clock</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Clock</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Clock" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Clock"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Empty</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Empty</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Empty" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Empty"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Full</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Full</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Full" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Full"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_RdEn</spirit:name>
                <spirit:displayName>Asys_fifo56X16_RdEn</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="RdEn" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_RdEn"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Reset</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Reset</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Reset" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Reset"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_WrEn</spirit:name>
                <spirit:displayName>Asys_fifo56X16_WrEn</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="WrEn" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_WrEn"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>global_clock_CLKI</spirit:name>
                <spirit:displayName>global_clock_CLKI</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="CLKI" spirit:componentRef="global_clock"/>
                <spirit:externalPortReference spirit:portRef="global_clock_CLKI"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>global_clock_CLKOP</spirit:name>
                <spirit:displayName>global_clock_CLKOP</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="CLKOP" spirit:componentRef="global_clock"/>
                <spirit:externalPortReference spirit:portRef="global_clock_CLKOP"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>global_clock_CLKOS</spirit:name>
                <spirit:displayName>global_clock_CLKOS</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="CLKOS" spirit:componentRef="global_clock"/>
                <spirit:externalPortReference spirit:portRef="global_clock_CLKOS"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>global_clock_CLKOS2</spirit:name>
                <spirit:displayName>global_clock_CLKOS2</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="CLKOS2" spirit:componentRef="global_clock"/>
                <spirit:externalPortReference spirit:portRef="global_clock_CLKOS2"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>global_clock_LOCK</spirit:name>
                <spirit:displayName>global_clock_LOCK</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="LOCK" spirit:componentRef="global_clock"/>
                <spirit:externalPortReference spirit:portRef="global_clock_LOCK"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>global_clock_RST</spirit:name>
                <spirit:displayName>global_clock_RST</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="RST" spirit:componentRef="global_clock"/>
                <spirit:externalPortReference spirit:portRef="global_clock_RST"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data</spirit:displayName>
                <spirit:internalPortReference spirit:right="0" spirit:portRef="Data" spirit:componentRef="Asys_fifo56X16" spirit:left="55"/>
                <spirit:externalPortReference spirit:right="0" spirit:portRef="Asys_fifo56X16_Data" spirit:left="55"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[0]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[0]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[0]"/>
                <spirit:internalPortReference spirit:portRef="Data[0]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[10]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[10]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[10]"/>
                <spirit:internalPortReference spirit:portRef="Data[10]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[11]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[11]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[11]"/>
                <spirit:internalPortReference spirit:portRef="Data[11]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[12]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[12]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[12]"/>
                <spirit:internalPortReference spirit:portRef="Data[12]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[13]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[13]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[13]"/>
                <spirit:internalPortReference spirit:portRef="Data[13]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[14]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[14]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[14]"/>
                <spirit:internalPortReference spirit:portRef="Data[14]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[15]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[15]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[15]"/>
                <spirit:internalPortReference spirit:portRef="Data[15]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[16]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[16]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[16]"/>
                <spirit:internalPortReference spirit:portRef="Data[16]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[17]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[17]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[17]"/>
                <spirit:internalPortReference spirit:portRef="Data[17]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[18]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[18]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[18]"/>
                <spirit:internalPortReference spirit:portRef="Data[18]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[19]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[19]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[19]"/>
                <spirit:internalPortReference spirit:portRef="Data[19]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[1]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[1]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[1]"/>
                <spirit:internalPortReference spirit:portRef="Data[1]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[20]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[20]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[20]"/>
                <spirit:internalPortReference spirit:portRef="Data[20]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[21]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[21]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[21]"/>
                <spirit:internalPortReference spirit:portRef="Data[21]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[22]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[22]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[22]"/>
                <spirit:internalPortReference spirit:portRef="Data[22]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[23]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[23]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[23]"/>
                <spirit:internalPortReference spirit:portRef="Data[23]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[24]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[24]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[24]"/>
                <spirit:internalPortReference spirit:portRef="Data[24]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[25]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[25]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[25]"/>
                <spirit:internalPortReference spirit:portRef="Data[25]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[26]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[26]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[26]"/>
                <spirit:internalPortReference spirit:portRef="Data[26]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[27]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[27]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[27]"/>
                <spirit:internalPortReference spirit:portRef="Data[27]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[28]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[28]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[28]"/>
                <spirit:internalPortReference spirit:portRef="Data[28]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[29]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[29]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[29]"/>
                <spirit:internalPortReference spirit:portRef="Data[29]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[2]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[2]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[2]"/>
                <spirit:internalPortReference spirit:portRef="Data[2]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[30]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[30]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[30]"/>
                <spirit:internalPortReference spirit:portRef="Data[30]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[31]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[31]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[31]"/>
                <spirit:internalPortReference spirit:portRef="Data[31]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[32]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[32]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[32]"/>
                <spirit:internalPortReference spirit:portRef="Data[32]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[33]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[33]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[33]"/>
                <spirit:internalPortReference spirit:portRef="Data[33]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[34]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[34]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[34]"/>
                <spirit:internalPortReference spirit:portRef="Data[34]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[35]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[35]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[35]"/>
                <spirit:internalPortReference spirit:portRef="Data[35]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[36]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[36]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[36]"/>
                <spirit:internalPortReference spirit:portRef="Data[36]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[37]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[37]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[37]"/>
                <spirit:internalPortReference spirit:portRef="Data[37]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[38]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[38]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[38]"/>
                <spirit:internalPortReference spirit:portRef="Data[38]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[39]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[39]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[39]"/>
                <spirit:internalPortReference spirit:portRef="Data[39]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[3]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[3]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[3]"/>
                <spirit:internalPortReference spirit:portRef="Data[3]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[40]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[40]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[40]"/>
                <spirit:internalPortReference spirit:portRef="Data[40]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[41]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[41]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[41]"/>
                <spirit:internalPortReference spirit:portRef="Data[41]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[42]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[42]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[42]"/>
                <spirit:internalPortReference spirit:portRef="Data[42]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[43]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[43]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[43]"/>
                <spirit:internalPortReference spirit:portRef="Data[43]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[44]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[44]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[44]"/>
                <spirit:internalPortReference spirit:portRef="Data[44]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[45]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[45]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[45]"/>
                <spirit:internalPortReference spirit:portRef="Data[45]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[46]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[46]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[46]"/>
                <spirit:internalPortReference spirit:portRef="Data[46]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[47]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[47]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[47]"/>
                <spirit:internalPortReference spirit:portRef="Data[47]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[48]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[48]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[48]"/>
                <spirit:internalPortReference spirit:portRef="Data[48]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[49]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[49]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[49]"/>
                <spirit:internalPortReference spirit:portRef="Data[49]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[4]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[4]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[4]"/>
                <spirit:internalPortReference spirit:portRef="Data[4]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[50]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[50]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[50]"/>
                <spirit:internalPortReference spirit:portRef="Data[50]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[51]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[51]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[51]"/>
                <spirit:internalPortReference spirit:portRef="Data[51]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[52]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[52]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[52]"/>
                <spirit:internalPortReference spirit:portRef="Data[52]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[53]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[53]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[53]"/>
                <spirit:internalPortReference spirit:portRef="Data[53]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[54]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[54]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[54]"/>
                <spirit:internalPortReference spirit:portRef="Data[54]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[55]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[55]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[55]"/>
                <spirit:internalPortReference spirit:portRef="Data[55]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[5]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[5]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[5]"/>
                <spirit:internalPortReference spirit:portRef="Data[5]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[6]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[6]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[6]"/>
                <spirit:internalPortReference spirit:portRef="Data[6]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[7]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[7]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[7]"/>
                <spirit:internalPortReference spirit:portRef="Data[7]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[8]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[8]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[8]"/>
                <spirit:internalPortReference spirit:portRef="Data[8]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Data[9]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Data[9]</spirit:displayName>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Data[9]"/>
                <spirit:internalPortReference spirit:portRef="Data[9]" spirit:componentRef="Asys_fifo56X16"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q</spirit:displayName>
                <spirit:internalPortReference spirit:right="0" spirit:portRef="Q" spirit:componentRef="Asys_fifo56X16" spirit:left="55"/>
                <spirit:externalPortReference spirit:right="0" spirit:portRef="Asys_fifo56X16_Q" spirit:left="55"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[0]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[0]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[0]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[0]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[10]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[10]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[10]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[10]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[11]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[11]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[11]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[11]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[12]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[12]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[12]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[12]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[13]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[13]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[13]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[13]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[14]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[14]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[14]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[14]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[15]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[15]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[15]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[15]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[16]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[16]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[16]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[16]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[17]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[17]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[17]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[17]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[18]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[18]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[18]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[18]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[19]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[19]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[19]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[19]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[1]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[1]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[1]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[1]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[20]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[20]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[20]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[20]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[21]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[21]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[21]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[21]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[22]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[22]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[22]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[22]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[23]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[23]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[23]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[23]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[24]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[24]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[24]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[24]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[25]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[25]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[25]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[25]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[26]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[26]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[26]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[26]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[27]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[27]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[27]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[27]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[28]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[28]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[28]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[28]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[29]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[29]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[29]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[29]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[2]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[2]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[2]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[2]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[30]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[30]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[30]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[30]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[31]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[31]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[31]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[31]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[32]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[32]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[32]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[32]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[33]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[33]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[33]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[33]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[34]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[34]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[34]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[34]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[35]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[35]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[35]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[35]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[36]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[36]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[36]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[36]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[37]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[37]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[37]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[37]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[38]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[38]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[38]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[38]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[39]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[39]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[39]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[39]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[3]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[3]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[3]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[3]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[40]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[40]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[40]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[40]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[41]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[41]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[41]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[41]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[42]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[42]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[42]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[42]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[43]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[43]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[43]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[43]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[44]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[44]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[44]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[44]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[45]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[45]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[45]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[45]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[46]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[46]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[46]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[46]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[47]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[47]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[47]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[47]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[48]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[48]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[48]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[48]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[49]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[49]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[49]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[49]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[4]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[4]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[4]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[4]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[50]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[50]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[50]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[50]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[51]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[51]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[51]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[51]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[52]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[52]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[52]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[52]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[53]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[53]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[53]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[53]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[54]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[54]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[54]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[54]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[55]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[55]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[55]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[55]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[5]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[5]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[5]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[5]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[6]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[6]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[6]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[6]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[7]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[7]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[7]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[7]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[8]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[8]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[8]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[8]"/>
            </spirit:adHocConnection>
            <spirit:adHocConnection>
                <spirit:name>Asys_fifo56X16_Q[9]</spirit:name>
                <spirit:displayName>Asys_fifo56X16_Q[9]</spirit:displayName>
                <spirit:internalPortReference spirit:portRef="Q[9]" spirit:componentRef="Asys_fifo56X16"/>
                <spirit:externalPortReference spirit:portRef="Asys_fifo56X16_Q[9]"/>
            </spirit:adHocConnection>
        </spirit:adHocConnections>
    </spirit:design>
</lattice:project>
