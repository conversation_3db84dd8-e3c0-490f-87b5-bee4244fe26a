`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	Integration
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	
// Revision 1.01 - File Created
// Additional Comments: 
//Time sequence diagram
//   ___
//  |   |
//__|   |_________________________________________//clk_DA
//0 1   4
//     ________________________
//    |                        |
//____|                        |__________________//AD_valid
//0……59                       229
//                               ___
//                              |   |
//______________________________|   |_____________//demodulate
//0……………………………………………………………………………230 233
//                                     ___
//                                    |   |
//____________________________________|   |_______//integrate
//0……………………………………………………………………………………… 234 237
//                                          ___
//                                         |   |
//_________________________________________|   |__//output_drive
//0……………………………………………………………………………………………………… 238 244
//                                            ___
//                                           |   |
//___________________________________________|   |//modulate
//0………………………………………………………………………………………………………  242 245
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////
module Integration
#(
	parameter iWID_IN=56,
	parameter iWID_OUT=56
)
(
	input								rst_n,
	input								clk, //120MHz reference clock
	input								polarity,   //flag of transmit-time	
	input								integrate,  //integrate积分
	input   	signed[iWID_IN-1:0]		din, 
	output reg  signed[iWID_OUT-1:0]	dout
);

reg 		[1:0] 			inte_dy;	//integrate Signal delay register
reg signed	[iWID_OUT-1:0]	DA_dout;

//integrate:Delay two beats to generate rising edge signal
always@(posedge clk)begin
	inte_dy <= {inte_dy[0],integrate};
end

//integrate velocity//
//每两个渡越时间累加一次角度数字量
always@(posedge clk)begin
	if((inte_dy == 2'b01) && (polarity == 1'b1))begin//integrate Rising edge
		DA_dout<=DA_dout+din;
	end
	else begin
		DA_dout<=DA_dout;
	end
end

//DA_dout:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	dout<=DA_dout;
end

endmodule

