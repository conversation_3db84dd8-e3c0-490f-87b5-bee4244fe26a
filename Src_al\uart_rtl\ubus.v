/*************************************************************************

**************************************************************************/

module ubus
(
	input	wire		clk,
	input	wire		rst_n,
	
	// ubus write
	input	wire		ubus_wen,
	input	wire [31:0]	ubus_waddr,
	input	wire [31:0]	ubus_wdata,
	
	// ubus read
	input	wire		ubus_ren,
	input	wire [31:0]	ubus_raddr,
    output  reg			ubus_bken,
	output	reg  [31:0] ubus_rdata,
	
	////cri io
	output	reg		[15:0]	cr_para_addr_i,
	output	reg		[15:0]	cr_rapa_wr_data_i,
	output	reg				cr_para_wr_en_i,
	output	reg				cr_para_rd_en_i,
	input	wire			cr_para_ack_o,
	input	wire	[15:0]	cr_para_rd_data_o,
    
    //----------------------------------------------
    //read serdes port(input and output)
    input [15:0] r_mplla_bandwidth,
    input        r_mplla_force_ack,
    input [7:0]  r_mplla_multiplier,
    input [1:0]  r_mplla_recal_bank_sel,
    input        r_mplla_state,
    input        r_mpllb_force_ack,
    input        r_mpllb_state,
    input        r_pcs_pwr_en,         
    input        r_pg_reset,                     
    input        r_pma_pwr_en,                 
    input        r_phy_reset_i,             
    input        r_ref_clk_en_i,          
    input        r_ref_clk_req,        
    input        r_ref_clkdet_result,
    input        r_rtune_ack,
    input        r_scan_mode,
    input        r_scan_set_rst,
    input        r_scan_shift,
    input        r_scan_shift_cg,	
    input [5:0]  r_scan_sel,   
    input        r_scan_phy_clk,
//  input [78:0] r_scan_data_in,
//  input [78:0] r_scan_data_out,
    input        r_sram_bypass_i,                  
    input        r_sram_ext_ld_done_i,            
    input        r_sram_init_done_o,      
    input [7:0]  r_sup_misc,
    input        r_test_burnin,
    input        r_test_flyover_en,   
    input        r_test_powerdown_i,
    input        r_test_stop_clk_en,
    input        r_sram_sel_i,

    input        r_rx0_ack,          
    input        r_rx0_adapt_ack,
    input [7:0]  r_rx0_adapt_fom,            
    input        r_rx0_adapt_in_prog,
    input [1:0]  r_rx0_adapt_mode,
    input        r_rx0_adapt_req,
    input        r_rx0_adapt_sel,
    input [4:0]  r_rx0_cdr_ppm_max,
    input        r_rx0_cdr_ssc_en,
    input [1:0]  r_rx0_cdr_vco_freqband,
    input        r_rx0_cdr_vco_step_ctrl,
    input        r_rx0_data_en,
    input [3:0]  r_rx0_delta_iq,
    input        r_rx0_dfe_bypass,
    input        r_rx0_disable,
    input [4:0]  r_rx0_eq_ctle_boost,
    input [7:0]  r_rx0_eq_dfe_tap,
    input [2:0]  r_rx0_eq_vga1_gain,
    input [2:0]  r_rx0_eq_vga2_gain,
    input        r_rx0_flyover_data_m,   
    input        r_rx0_flyover_data_p,  
    input        r_rx0_invert,
    input        r_rx0_los,
    input        r_rx0_lpd,
    input [7:0]  r_rx0_misc,
    input [5:0]  r_rx0_ppm_drift,
    input        r_rx0_ppm_drift_vld,
    input [1:0]  r_rx0_pstate,
    input [1:0]  r_rx0_rate,
    input [6:0]  r_rx0_ref_ld_val,
    input        r_rx0_req,                        
    input        r_rx0_reset_i,              
    input        r_rx0_term_acdc,
    input        r_rx0_term_en,
    input [1:0]  r_rx0_txmain_dir,     
    input [1:0]  r_rx0_txpost_dir,     
    input [1:0]  r_rx0_txpre_dir,     
    input        r_rx0_valid,                 
    input [12:0] r_rx0_vco_ld_val,

    input        r_rx1_ack,          
    input        r_rx1_adapt_ack,
    input [7:0]  r_rx1_adapt_fom,            
    input        r_rx1_adapt_in_prog,
    input [1:0]  r_rx1_adapt_mode,
    input        r_rx1_adapt_req,
    input        r_rx1_adapt_sel,
    input [4:0]  r_rx1_cdr_ppm_max,
    input        r_rx1_cdr_ssc_en,
    input [1:0]  r_rx1_cdr_vco_freqband,
    input        r_rx1_cdr_vco_step_ctrl,
    input        r_rx1_data_en,
    input [3:0]  r_rx1_delta_iq,
    input        r_rx1_dfe_bypass,
    input        r_rx1_disable,
    input [4:0]  r_rx1_eq_ctle_boost,
    input [7:0]  r_rx1_eq_dfe_tap,
    input [2:0]  r_rx1_eq_vga1_gain,
    input [2:0]  r_rx1_eq_vga2_gain,
    input        r_rx1_flyover_data_m,   
    input        r_rx1_flyover_data_p,   
    input        r_rx1_invert,
    input        r_rx1_los,
    input        r_rx1_lpd,
    input [7:0]  r_rx1_misc,
    input [5:0]  r_rx1_ppm_drift,
    input        r_rx1_ppm_drift_vld,
    input [1:0]  r_rx1_pstate,
    input [1:0]  r_rx1_rate,
    input [6:0]  r_rx1_ref_ld_val,
    input        r_rx1_req,                        
    input        r_rx1_reset_i,              
    input        r_rx1_term_acdc,
    input        r_rx1_term_en,
    input [1:0]  r_rx1_txmain_dir,     
    input [1:0]  r_rx1_txpost_dir,     
    input [1:0]  r_rx1_txpre_dir,     
    input        r_rx1_valid,                 
    input [12:0] r_rx1_vco_ld_val,

    input        r_tx0_ack,                  
    input        r_tx0_beacon_en,
    input        r_tx0_clk_rdy,
    input        r_tx0_data_en,
    input        r_tx0_detrx_req,
    input        r_tx0_detrx_result,
    input        r_tx0_disable,
    input [5:0]  r_tx0_eq_main,
    input [5:0]  r_tx0_eq_post,
    input [5:0]  r_tx0_eq_pre,
    input        r_tx0_flyover_data_m,        
    input        r_tx0_flyover_data_p,    
    input        r_tx0_lpd,
    input        r_tx0_master_mplla_state,
    input        r_tx0_master_mpllb_state,
    input        r_tx0_mpll_en,
    input        r_tx0_mpllb_sel,
    input [1:0]  r_tx0_pstate,
    input [2:0]  r_tx0_rate,
    input        r_tx0_req,               
    input        r_tx0_reset_i,

    input        r_tx1_ack,                  
    input        r_tx1_beacon_en,
    input        r_tx1_clk_rdy,
    input        r_tx1_data_en,
    input        r_tx1_detrx_req,
    input        r_tx1_detrx_result,
    input        r_tx1_disable,
    input [5:0]  r_tx1_eq_main,
    input [5:0]  r_tx1_eq_post,
    input [5:0]  r_tx1_eq_pre,
    input        r_tx1_flyover_data_m,        
    input        r_tx1_flyover_data_p,    
    input        r_tx1_lpd,
    input        r_tx1_master_mplla_state,
    input        r_tx1_master_mpllb_state,
    input        r_tx1_mpll_en,
    input        r_tx1_mpllb_sel,
    input [1:0]  r_tx1_pstate,
    input [2:0]  r_tx1_rate,
    input        r_tx1_req,               
    input        r_tx1_reset_i,

    input        r_l0_pcs_native_en,
    input        r_l0_pcs_standard_en,
    input        r_l0_gate_stclk_en,
    input        r_l0_gate_drclk_en,
//  input        r_l0_tx_outclk,
//  input        r_l0_tx_out2clk,
//  input        r_l0_rx_outclk,
//  input        r_l0_rx_out2clk,
//  input        r_l0_tx_usrclk,
//  input        r_l0_tx_usr2clk,
//  input        r_l0_rx_usrclk,
//  input        r_l0_rx_usr2clk,
    input [3:0]  r_l0_tx_outclk_sel,   
    input [3:0]  r_l0_tx_out2clk_sel,
    input [1:0]  r_l0_tx_xclk_sel,
    input [1:0]  r_l0_tx_iclk_sel,
    input [1:0]  r_l0_rx_outclk_sel,
    input [1:0]  r_l0_rx_out2clk_sel,  
    input [1:0]  r_l0_rx_xclk_sel,
    input [1:0]  r_l0_rx_rmatclk_sel,
    input [2:0]  r_l0_rx_oclk_sel,
    input        r_l0_tx_pcs_rst_n,
    input        r_l0_tx_pcsfifo_rst_n,
//  input [79:0] r_l0_tx_usrdata_in,
    input [2:0]  r_l0_tx_usrdata_width,
    input [7:0]  r_l0_tx_usrdatak_in,
    input [7:0]  r_l0_tx_forcedisp_in,
    input [7:0]  r_l0_tx_forcedispval_in,
    input        r_l0_tx_byteserial_en,
    input        r_l0_tx_byteserial_mode,
    input        r_l0_tx_pcsfifo_en,
    input        r_l0_tx_enc_en,
    input        r_l0_tx_bitslip_en,
    input [4:0]  r_l0_tx_bitslip_width,
    input        r_l0_tx_prbs_en,
    input [2:0]  r_l0_tx_prbs_sel,
    input        r_l0_tx_prbs_force_err,
    input [1:0]  r_l0_tx_pmadata_width,     
    input        r_l0_tx_pcsfifo_err,

    input        r_l1_pcs_native_en,
    input        r_l1_pcs_standard_en,
    input        r_l1_gate_stclk_en,
    input        r_l1_gate_drclk_en,
//  input        r_l1_tx_outclk,
//  input        r_l1_tx_out2clk,
//  input        r_l1_rx_outclk,
//  input        r_l1_rx_out2clk,
//  input        r_l1_tx_usrclk,
//  input        r_l1_tx_usr2clk,
//  input        r_l1_rx_usrclk,
//  input        r_l1_rx_usr2clk,
    input [3:0]  r_l1_tx_outclk_sel,   
    input [3:0]  r_l1_tx_out2clk_sel,
    input [1:0]  r_l1_tx_xclk_sel,
    input [1:0]  r_l1_tx_iclk_sel,
    input [1:0]  r_l1_rx_outclk_sel,
    input [1:0]  r_l1_rx_out2clk_sel,  
    input [1:0]  r_l1_rx_xclk_sel,
    input [1:0]  r_l1_rx_rmatclk_sel,
    input [2:0]  r_l1_rx_oclk_sel,
    input        r_l1_tx_pcs_rst_n,
    input        r_l1_tx_pcsfifo_rst_n,
//  input [79:0] r_l1_tx_usrdata_in,
    input [2:0]  r_l1_tx_usrdata_width,
    input [7:0]  r_l1_tx_usrdatak_in,
    input [7:0]  r_l1_tx_forcedisp_in,
    input [7:0]  r_l1_tx_forcedispval_in,
    input        r_l1_tx_byteserial_en,
    input        r_l1_tx_byteserial_mode,
    input        r_l1_tx_pcsfifo_en,
    input        r_l1_tx_enc_en,
    input        r_l1_tx_bitslip_en,
    input [4:0]  r_l1_tx_bitslip_width,
    input        r_l1_tx_prbs_en,
    input [2:0]  r_l1_tx_prbs_sel,
    input        r_l1_tx_prbs_force_err,
    input [1:0]  r_l1_tx_pmadata_width,     
    input        r_l1_tx_pcsfifo_err,

    input        r_l0_rx_pcs_rst_n,
    input        r_l0_rx_pcsfifo_rst_n,
    input        r_l0_rx_rmatchfifo_rst_n,
    input [1:0]  r_l0_rx_pmadata_width,
    input        r_l0_rx_prbs_en,
    input [2:0]  r_l0_rx_prbs_sel,
    input        r_l0_rx_wordalign_en,
    input        r_l0_rx_pcommaalign_en,
    input        r_l0_rx_mcommaalign_en,
    input        r_l0_rx_slide,
    input [9:0]  r_l0_rx_aligncomma_mask,
    input        r_l0_rx_aligncomma_double,
    input [9:0]  r_l0_rx_alignmcomma_value,
    input        r_l0_rx_alignmcomma_det,
    input [9:0]  r_l0_rx_alignpcomma_value,
    input        r_l0_rx_alignpcomma_det,
    input        r_l0_rx_show_realigncomma,
    input [1:0]  r_l0_rx_slidemode,
    input        r_l0_rx_ratmatfifo_en,
    input [9:0]  r_l0_rx_rmfifo_skip_1byte,
    input [9:0]  r_l0_rx_rmfifo_skip_2byte,
    input [9:0]  r_l0_rx_rmfifo_skip_3byte,
    input [9:0]  r_l0_rx_rmfifo_skip_4byte,
    input        r_l0_rx_rmfifo_skip4byte_en,
    input        r_l0_rx_rmfifo_skip2byte_en,
    input [3:0]  r_l0_rx_rmfifo_min_ipg_cnt,
    input [4:0]  r_l0_rx_rmfifo_high_mark,
    input [4:0]  r_l0_rx_rmfifo_low_mark,
    input        r_l0_rx_dec_en,
    input        r_l0_rx_pcsfifo_en,
    input        r_l0_rx_bytedeserial_en,
    input        r_l0_rx_bytedeserial_mode,
    input [2:0]  r_l0_rx_usrdata_width,
    input        r_l0_rx_prbs_err_clr,
    input [7:0]  r_l0_rx_prbs_err_cnt,
    input        r_l0_rx_wordaligned,
    input        r_l0_rx_wordrealign,
    input [1:0]  r_l0_rx_commadet,   
    input [3:0]  r_l0_rx_align_sel,   
    input        r_l0_rx_rmfifo_re_out,      
    input        r_l0_rx_rmfifo_we_out,       
    input        r_l0_rx_rmfifo_overflow,
    input        r_l0_rx_rmfifo_underflow,
    input        r_l0_rx_pcsfifo_err,
//  input [79:0] r_l0_rx_usrdata_out,
    input [7:0]  r_l0_rx_usrdatak_out,
    input [7:0]  r_l0_rx_usrdisperr_out,
    input [7:0]  r_l0_rx_usrcodeerr_out,
    input        r_l0_loopback_en,
    input [1:0]  r_l0_loopback_mode,

    input        r_l1_rx_pcs_rst_n,
    input        r_l1_rx_pcsfifo_rst_n,
    input        r_l1_rx_rmatchfifo_rst_n,
    input [1:0]  r_l1_rx_pmadata_width,
    input        r_l1_rx_prbs_en,
    input [2:0]  r_l1_rx_prbs_sel,
    input        r_l1_rx_wordalign_en,
    input        r_l1_rx_pcommaalign_en,
    input        r_l1_rx_mcommaalign_en,
    input        r_l1_rx_slide,
    input [9:0]  r_l1_rx_aligncomma_mask,
    input        r_l1_rx_aligncomma_double,
    input [9:0]  r_l1_rx_alignmcomma_value,
    input        r_l1_rx_alignmcomma_det,
    input [9:0]  r_l1_rx_alignpcomma_value,
    input        r_l1_rx_alignpcomma_det,
    input        r_l1_rx_show_realigncomma,
    input [1:0]  r_l1_rx_slidemode,
    input        r_l1_rx_ratmatfifo_en,
    input [9:0]  r_l1_rx_rmfifo_skip_1byte,
    input [9:0]  r_l1_rx_rmfifo_skip_2byte,
    input [9:0]  r_l1_rx_rmfifo_skip_3byte,
    input [9:0]  r_l1_rx_rmfifo_skip_4byte,
    input        r_l1_rx_rmfifo_skip4byte_en,
    input        r_l1_rx_rmfifo_skip2byte_en,
    input [3:0]  r_l1_rx_rmfifo_min_ipg_cnt,
    input [4:0]  r_l1_rx_rmfifo_high_mark,
    input [4:0]  r_l1_rx_rmfifo_low_mark,
    input        r_l1_rx_dec_en,
    input        r_l1_rx_pcsfifo_en,
    input        r_l1_rx_bytedeserial_en,
    input        r_l1_rx_bytedeserial_mode,
    input [2:0]  r_l1_rx_usrdata_width,
    input        r_l1_rx_prbs_err_clr,
    input [7:0]  r_l1_rx_prbs_err_cnt,
    input        r_l1_rx_wordaligned,
    input        r_l1_rx_wordrealign,
    input [1:0]  r_l1_rx_commadet,   
    input [3:0]  r_l1_rx_align_sel,   
    input        r_l1_rx_rmfifo_re_out,      
    input        r_l1_rx_rmfifo_we_out,       
    input        r_l1_rx_rmfifo_overflow,
    input        r_l1_rx_rmfifo_underflow,
    input        r_l1_rx_pcsfifo_err,
//  input [79:0] r_l1_rx_usrdata_out,
    input [7:0]  r_l1_rx_usrdatak_out,
    input [7:0]  r_l1_rx_usrdisperr_out,
    input [7:0]  r_l1_rx_usrcodeerr_out,
    input        r_l1_loopback_en,
    input [1:0]  r_l1_loopback_mode,

    input [3:0]  r_l0_debug_sel,
//  input [57:0] r_l0_data_debug,
    input [3:0]  r_l1_debug_sel,
//  input [57:0] r_l1_data_debug,
    input [1:0]  r_clk0_sel,
    input [1:0]  r_clk1_sel,
    input [1:0]  r_clk2_sel,
//  input        r_clk0_out,
//  input        r_clk1_out,
//  input        r_clk2_out,
    input [1:0]  r_data_locked,  
    //----------------------------------------------
    //configuration serdes input port                               
    output    [15:0] w_mplla_bandwidth,                          
    output    [7:0]  w_mplla_multiplier,                         
    output    [1:0]  w_mplla_recal_bank_sel,                     
    output           w_pg_reset,                                      
    output           w_phy_reset_i,                              
    output           w_ref_clk_en_i,                             
    output           w_scan_mode,                                
    output           w_scan_set_rst,                             
    output           w_scan_shift,                               
    output           w_scan_shift_cg,	                        
    output    [5:0]  w_scan_sel,                                 
    output           w_scan_phy_clk,                             
//  output    [78:0] w_scan_data_in,                             
    output           w_sram_bypass_i,                            
//  output           w_sram_ext_ld_done_i,                       
    output    [7:0]  w_sup_misc,                                 
    output           w_test_burnin,                              
    output           w_test_flyover_en,                          
    output           w_test_powerdown_i,                         
    output           w_test_stop_clk_en,                         
    output           w_sram_sel_i,                               
                                                                 
    output           w_rx0_adapt_in_prog,                        
    output    [1:0]  w_rx0_adapt_mode,                           
    output           w_rx0_adapt_req,                            
    output           w_rx0_adapt_sel,                            
    output    [4:0]  w_rx0_cdr_ppm_max,                          
    output           w_rx0_cdr_ssc_en,                           
    output    [1:0]  w_rx0_cdr_vco_freqband,                     
    output           w_rx0_cdr_vco_step_ctrl,                    
    output           w_rx0_data_en,                              
    output    [3:0]  w_rx0_delta_iq,                             
    output           w_rx0_dfe_bypass,                           
    output           w_rx0_disable,                              
    output    [4:0]  w_rx0_eq_ctle_boost,                        
    output    [7:0]  w_rx0_eq_dfe_tap,                           
    output    [2:0]  w_rx0_eq_vga1_gain,                         
    output    [2:0]  w_rx0_eq_vga2_gain,                         
    output           w_rx0_invert,                               
    output           w_rx0_lpd,                                  
    output    [7:0]  w_rx0_misc,                                 
    output    [1:0]  w_rx0_pstate,                               
    output    [1:0]  w_rx0_rate,                                 
    output    [6:0]  w_rx0_ref_ld_val,                           
    output           w_rx0_req,                                  
    output           w_rx0_reset_i,                              
    output           w_rx0_term_acdc,                            
    output           w_rx0_term_en,                              
    output    [12:0] w_rx0_vco_ld_val,                           
                                                                 
    output           w_rx1_adapt_in_prog,                        
    output    [1:0]  w_rx1_adapt_mode,                           
    output           w_rx1_adapt_req,                            
    output           w_rx1_adapt_sel,                            
    output    [4:0]  w_rx1_cdr_ppm_max,                          
    output           w_rx1_cdr_ssc_en,                           
    output    [1:0]  w_rx1_cdr_vco_freqband,                     
    output           w_rx1_cdr_vco_step_ctrl,                    
    output           w_rx1_data_en,                              
    output    [3:0]  w_rx1_delta_iq,                             
    output           w_rx1_dfe_bypass,                           
    output           w_rx1_disable,                              
    output    [4:0]  w_rx1_eq_ctle_boost,                        
    output    [7:0]  w_rx1_eq_dfe_tap,                           
    output    [2:0]  w_rx1_eq_vga1_gain,                         
    output    [2:0]  w_rx1_eq_vga2_gain,                         
    output           w_rx1_invert,                               
    output           w_rx1_lpd,                                  
    output    [7:0]  w_rx1_misc,                                 
    output    [1:0]  w_rx1_pstate,                               
    output    [1:0]  w_rx1_rate,                                 
    output    [6:0]  w_rx1_ref_ld_val,                           
    output           w_rx1_req,                                  
    output           w_rx1_reset_i,                              
    output           w_rx1_term_acdc,                            
    output           w_rx1_term_en,                              
    output    [12:0] w_rx1_vco_ld_val,                           
                                                                 
    output           w_tx0_beacon_en,                            
    output           w_tx0_clk_rdy,                              
    output           w_tx0_data_en,                              
    output           w_tx0_detrx_req,                            
    output           w_tx0_disable,                              
    output    [5:0]  w_tx0_eq_main,                              
    output    [5:0]  w_tx0_eq_post,                              
    output    [5:0]  w_tx0_eq_pre,                               
    output           w_tx0_flyover_data_m,                       
    output           w_tx0_flyover_data_p,                       
    output           w_tx0_lpd,                                  
    output           w_tx0_master_mplla_state,                   
    output           w_tx0_master_mpllb_state,                   
    output           w_tx0_mpll_en,                              
    output           w_tx0_mpllb_sel,                            
    output    [1:0]  w_tx0_pstate,                               
    output    [2:0]  w_tx0_rate,                                 
    output           w_tx0_req,                                  
    output           w_tx0_reset_i,                              
                                                                 
    output           w_tx1_beacon_en,                            
    output           w_tx1_clk_rdy,                              
    output           w_tx1_data_en,                              
    output           w_tx1_detrx_req,                            
    output           w_tx1_disable,                              
    output    [5:0]  w_tx1_eq_main,                              
    output    [5:0]  w_tx1_eq_post,                              
    output    [5:0]  w_tx1_eq_pre,                               
    output           w_tx1_flyover_data_m,                       
    output           w_tx1_flyover_data_p,                       
    output           w_tx1_lpd,                                  
    output           w_tx1_master_mplla_state,                   
    output           w_tx1_master_mpllb_state,                   
    output           w_tx1_mpll_en,                              
    output           w_tx1_mpllb_sel,                            
    output    [1:0]  w_tx1_pstate,                               
    output    [2:0]  w_tx1_rate,                                 
    output           w_tx1_req,                                  
    output           w_tx1_reset_i,                              
                                                                 
    output           w_l0_pcs_native_en,                         
    output           w_l0_pcs_standard_en,                       
    output           w_l0_gate_stclk_en,                         
    output           w_l0_gate_drclk_en,                         
//  output           w_l0_tx_usrclk,                             
//  output           w_l0_tx_usr2clk,                            
//  output           w_l0_rx_usrclk,                             
//  output           w_l0_rx_usr2clk,                            
    output    [3:0]  w_l0_tx_outclk_sel,                         
    output    [3:0]  w_l0_tx_out2clk_sel,                        
    output    [1:0]  w_l0_tx_xclk_sel,                           
    output    [1:0]  w_l0_tx_iclk_sel,                           
    output    [1:0]  w_l0_rx_outclk_sel,                         
    output    [1:0]  w_l0_rx_out2clk_sel,                        
    output    [1:0]  w_l0_rx_xclk_sel,                           
    output    [1:0]  w_l0_rx_rmatclk_sel,                        
    output    [2:0]  w_l0_rx_oclk_sel,                           
    output           w_l0_tx_pcs_rst_n,                          
    output           w_l0_tx_pcsfifo_rst_n,                      
//  output    [79:0] w_l0_tx_usrdata_in,                         
    output    [2:0]  w_l0_tx_usrdata_width,                      
    output    [7:0]  w_l0_tx_usrdatak_in,                        
    output    [7:0]  w_l0_tx_forcedisp_in,                       
    output    [7:0]  w_l0_tx_forcedispval_in,                    
    output           w_l0_tx_byteserial_en,                      
    output           w_l0_tx_byteserial_mode,                    
    output           w_l0_tx_pcsfifo_en,                         
    output           w_l0_tx_enc_en,                             
    output           w_l0_tx_bitslip_en,                         
    output    [4:0]  w_l0_tx_bitslip_width,                      
    output           w_l0_tx_prbs_en,                            
    output    [2:0]  w_l0_tx_prbs_sel,                           
    output           w_l0_tx_prbs_force_err,                     
    output    [1:0]  w_l0_tx_pmadata_width,                      
                                                                 
    output           w_l1_pcs_native_en,                         
    output           w_l1_pcs_standard_en,                       
    output           w_l1_gate_stclk_en,                         
    output           w_l1_gate_drclk_en,                         
//  output           w_l1_tx_usrclk,                             
//  output           w_l1_tx_usr2clk,                            
//  output           w_l1_rx_usrclk,                             
//  output           w_l1_rx_usr2clk,                            
    output    [3:0]  w_l1_tx_outclk_sel,                         
    output    [3:0]  w_l1_tx_out2clk_sel,                        
    output    [1:0]  w_l1_tx_xclk_sel,                           
    output    [1:0]  w_l1_tx_iclk_sel,                           
    output    [1:0]  w_l1_rx_outclk_sel,                         
    output    [1:0]  w_l1_rx_out2clk_sel,                        
    output    [1:0]  w_l1_rx_xclk_sel,                           
    output    [1:0]  w_l1_rx_rmatclk_sel,                        
    output    [2:0]  w_l1_rx_oclk_sel,                           
    output           w_l1_tx_pcs_rst_n,                          
    output           w_l1_tx_pcsfifo_rst_n,                      
//  output    [79:0] w_l1_tx_usrdata_in,                         
    output    [2:0]  w_l1_tx_usrdata_width,                      
    output    [7:0]  w_l1_tx_usrdatak_in,                        
    output    [7:0]  w_l1_tx_forcedisp_in,                       
    output    [7:0]  w_l1_tx_forcedispval_in,                    
    output           w_l1_tx_byteserial_en,                      
    output           w_l1_tx_byteserial_mode,                    
    output           w_l1_tx_pcsfifo_en,                         
    output           w_l1_tx_enc_en,                             
    output           w_l1_tx_bitslip_en,                         
    output    [4:0]  w_l1_tx_bitslip_width,                      
    output           w_l1_tx_prbs_en,                            
    output    [2:0]  w_l1_tx_prbs_sel,                           
    output           w_l1_tx_prbs_force_err,                     
    output    [1:0]  w_l1_tx_pmadata_width,                      
                                                                 
    output           w_l0_rx_pcs_rst_n,                          
    output           w_l0_rx_pcsfifo_rst_n,                      
    output           w_l0_rx_rmatchfifo_rst_n,                   
    output    [1:0]  w_l0_rx_pmadata_width,                      
    output           w_l0_rx_prbs_en,                            
    output    [2:0]  w_l0_rx_prbs_sel,                           
    output           w_l0_rx_wordalign_en,                       
    output           w_l0_rx_pcommaalign_en,                     
    output           w_l0_rx_mcommaalign_en,                     
    output           w_l0_rx_slide,                              
    output    [9:0]  w_l0_rx_aligncomma_mask,                    
    output           w_l0_rx_aligncomma_double,                  
    output    [9:0]  w_l0_rx_alignmcomma_value,                  
    output           w_l0_rx_alignmcomma_det,                    
    output    [9:0]  w_l0_rx_alignpcomma_value,                  
    output           w_l0_rx_alignpcomma_det,                    
    output           w_l0_rx_show_realigncomma,                  
    output    [1:0]  w_l0_rx_slidemode,                          
    output           w_l0_rx_ratmatfifo_en,                      
    output    [9:0]  w_l0_rx_rmfifo_skip_1byte,                  
    output    [9:0]  w_l0_rx_rmfifo_skip_2byte,                  
    output    [9:0]  w_l0_rx_rmfifo_skip_3byte,                  
    output    [9:0]  w_l0_rx_rmfifo_skip_4byte,                  
    output           w_l0_rx_rmfifo_skip4byte_en,                
    output           w_l0_rx_rmfifo_skip2byte_en,                
    output    [3:0]  w_l0_rx_rmfifo_min_ipg_cnt,                 
    output    [4:0]  w_l0_rx_rmfifo_high_mark,                   
    output    [4:0]  w_l0_rx_rmfifo_low_mark,                    
    output           w_l0_rx_dec_en,                             
    output           w_l0_rx_pcsfifo_en,                         
    output           w_l0_rx_bytedeserial_en,                    
    output           w_l0_rx_bytedeserial_mode,                  
    output    [2:0]  w_l0_rx_usrdata_width,                      
    output           w_l0_rx_prbs_err_clr,                       
    output           w_l0_loopback_en,                           
    output    [1:0]  w_l0_loopback_mode,                         
                                                                 
    output           w_l1_rx_pcs_rst_n,                          
    output           w_l1_rx_pcsfifo_rst_n,                      
    output           w_l1_rx_rmatchfifo_rst_n,                   
    output    [1:0]  w_l1_rx_pmadata_width,                      
    output           w_l1_rx_prbs_en,                            
    output    [2:0]  w_l1_rx_prbs_sel,                           
    output           w_l1_rx_wordalign_en,                       
    output           w_l1_rx_pcommaalign_en,                     
    output           w_l1_rx_mcommaalign_en,                     
    output           w_l1_rx_slide,                              
    output    [9:0]  w_l1_rx_aligncomma_mask,                    
    output           w_l1_rx_aligncomma_double,                  
    output    [9:0]  w_l1_rx_alignmcomma_value,                  
    output           w_l1_rx_alignmcomma_det,                    
    output    [9:0]  w_l1_rx_alignpcomma_value,                  
    output           w_l1_rx_alignpcomma_det,                    
    output           w_l1_rx_show_realigncomma,                  
    output    [1:0]  w_l1_rx_slidemode,                          
    output           w_l1_rx_ratmatfifo_en,                      
    output    [9:0]  w_l1_rx_rmfifo_skip_1byte,                  
    output    [9:0]  w_l1_rx_rmfifo_skip_2byte,                  
    output    [9:0]  w_l1_rx_rmfifo_skip_3byte,                  
    output    [9:0]  w_l1_rx_rmfifo_skip_4byte,                  
    output           w_l1_rx_rmfifo_skip4byte_en,                
    output           w_l1_rx_rmfifo_skip2byte_en,                
    output    [3:0]  w_l1_rx_rmfifo_min_ipg_cnt,                 
    output    [4:0]  w_l1_rx_rmfifo_high_mark,                   
    output    [4:0]  w_l1_rx_rmfifo_low_mark,                    
    output           w_l1_rx_dec_en,                             
    output           w_l1_rx_pcsfifo_en,                         
    output           w_l1_rx_bytedeserial_en,                    
    output           w_l1_rx_bytedeserial_mode,                  
    output    [2:0]  w_l1_rx_usrdata_width,                      
    output           w_l1_rx_prbs_err_clr,                       
    output           w_l1_loopback_en,                           
    output    [1:0]  w_l1_loopback_mode,                         
                                                                 
    output    [3:0]  w_l0_debug_sel,                             
    output    [3:0]  w_l1_debug_sel,                             
    output    [1:0]  w_clk0_sel,                                 
    output    [1:0]  w_clk1_sel,                                 
    output    [1:0]  w_clk2_sel                                  
           	
);




    //----------------------------------------------
    //configuration serdes input port   
     
      reg  [15:0] we_mplla_bandwidth           =  16'd41031       ;              
      reg  [7:0]  we_mplla_multiplier          =  8'd40           ;              
      reg  [1:0]  we_mplla_recal_bank_sel      =  2'd0            ;               
      reg         we_pg_reset                  =  1'b0            ;                    
      reg         we_phy_reset_i               =  1'b0            ;               
      reg         we_ref_clk_en_i              =  1'b1            ;               
      reg         we_scan_mode                 =  1'b0            ;              
      reg         we_scan_set_rst              =  1'b0            ;              
      reg         we_scan_shift                =  1'b0            ;              
      reg         we_scan_shift_cg             =  1'b0            ;            
      reg  [5:0]  we_scan_sel                  =  6'd0            ;               
      reg         we_scan_phy_clk              =  1'b0            ;              
//    reg  [78:0] we_scan_data_in              =                  ;              
      reg         we_sram_bypass_i             =  1'b1            ;               
//    reg         we_sram_ext_ld_done_i        =                  ;               
      reg  [7:0]  we_sup_misc                  =  8'd1            ;              
      reg         we_test_burnin               =  1'b0            ;              
      reg         we_test_flyover_en           =  1'b0            ;               
      reg         we_test_powerdown_i          =  1'b0            ;              
      reg         we_test_stop_clk_en          =  1'b0            ;              
      reg         we_sram_sel_i                =  1'b1            ;              
                            
      reg         we_rx0_adapt_in_prog         =  1'b0            ;              
      reg  [1:0]  we_rx0_adapt_mode            =  2'b00           ;              
      reg         we_rx0_adapt_req             =  1'b0            ;              
      reg         we_rx0_adapt_sel             =  1'b0            ;              
      reg  [4:0]  we_rx0_cdr_ppm_max           =  5'd18           ;              
      reg         we_rx0_cdr_ssc_en            =  1'b0            ;              
      reg  [1:0]  we_rx0_cdr_vco_freqband      =  2'd1            ;              
      reg         we_rx0_cdr_vco_step_ctrl     =  1'b1            ;              
      reg         we_rx0_data_en               =  1'b1            ;              
      reg  [3:0]  we_rx0_delta_iq              =  4'd0            ;              
      reg         we_rx0_dfe_bypass            =  1'b1            ;              
      reg         we_rx0_disable               =  1'b0            ;              
      reg  [4:0]  we_rx0_eq_ctle_boost         =  5'd6            ;              
      reg  [7:0]  we_rx0_eq_dfe_tap            =  8'd0            ;              
      reg  [2:0]  we_rx0_eq_vga1_gain          =  3'd4            ;              
      reg  [2:0]  we_rx0_eq_vga2_gain          =  3'd4            ;               
      reg         we_rx0_invert                =  1'b0            ;              
      reg         we_rx0_lpd                   =  1'b0            ;              
      reg  [7:0]  we_rx0_misc                  =  8'd6            ;              
      reg  [1:0]  we_rx0_pstate                =  2'b00           ;              
      reg  [1:0]  we_rx0_rate                  =  2'd3            ;              
      reg  [6:0]  we_rx0_ref_ld_val            =  7'd34           ;              
      reg         we_rx0_req                   =  1'b0            ;               
      reg         we_rx0_reset_i               =  1'b0            ;               
      reg         we_rx0_term_acdc             =  1'b1            ;              
      reg         we_rx0_term_en               =  1'b1            ;               
      reg  [12:0] we_rx0_vco_ld_val            =  13'd1360        ;              
                                
      reg         we_rx1_adapt_in_prog         =  1'b0            ;              
      reg  [1:0]  we_rx1_adapt_mode            =  2'b00           ;              
      reg         we_rx1_adapt_req             =  1'b0            ;              
      reg         we_rx1_adapt_sel             =  1'b0            ;              
      reg  [4:0]  we_rx1_cdr_ppm_max           =  5'd18           ;              
      reg         we_rx1_cdr_ssc_en            =  1'b0            ;              
      reg  [1:0]  we_rx1_cdr_vco_freqband      =  2'd1            ;              
      reg         we_rx1_cdr_vco_step_ctrl     =  1'b1            ;              
      reg         we_rx1_data_en               =  1'b1            ;              
      reg  [3:0]  we_rx1_delta_iq              =  4'd0            ;              
      reg         we_rx1_dfe_bypass            =  1'b1            ;              
      reg         we_rx1_disable               =  1'b0            ;              
      reg  [4:0]  we_rx1_eq_ctle_boost         =  5'd6            ;              
      reg  [7:0]  we_rx1_eq_dfe_tap            =  8'd0            ;              
      reg  [2:0]  we_rx1_eq_vga1_gain          =  3'd4            ;              
      reg  [2:0]  we_rx1_eq_vga2_gain          =  3'd4            ;               
      reg         we_rx1_invert                =  1'b0            ;              
      reg         we_rx1_lpd                   =  1'b0            ;              
      reg  [7:0]  we_rx1_misc                  =  8'd6            ;              
      reg  [1:0]  we_rx1_pstate                =  2'b00           ;              
      reg  [1:0]  we_rx1_rate                  =  2'd3            ;              
      reg  [6:0]  we_rx1_ref_ld_val            =  7'd34           ;              
      reg         we_rx1_req                   =  1'b0            ;               
      reg         we_rx1_reset_i               =  1'b0            ;               
      reg         we_rx1_term_acdc             =  1'b1            ;              
      reg         we_rx1_term_en               =  1'b1            ;               
      reg  [12:0] we_rx1_vco_ld_val            =  13'd1360        ;              
                            
      reg         we_tx0_beacon_en             =  1'b0            ;              
      reg         we_tx0_clk_rdy               =  1'b1            ;              
      reg         we_tx0_data_en               =  1'b1            ;              
      reg         we_tx0_detrx_req             =  1'b0            ;              
      reg         we_tx0_disable               =  1'b0            ;              
      reg  [5:0]  we_tx0_eq_main               =  6'd20           ;              
      reg  [5:0]  we_tx0_eq_post               =  6'd0            ;              
      reg  [5:0]  we_tx0_eq_pre                =  6'd0            ;              
      reg         we_tx0_flyover_data_m        =  1'b0            ;               
      reg         we_tx0_flyover_data_p        =  1'b0            ;               
      reg         we_tx0_lpd                   =  1'b0            ;              
      reg         we_tx0_master_mplla_state    =  1'b1            ;              
      reg         we_tx0_master_mpllb_state    =  1'b1            ;              
      reg         we_tx0_mpll_en               =  1'b1            ;              
      reg         we_tx0_mpllb_sel             =  1'b0            ;              
      reg  [1:0]  we_tx0_pstate                =  2'b00           ;              
      reg  [2:0]  we_tx0_rate                  =  3'd3            ;              
      reg         we_tx0_req                   =  1'b0            ;               
      reg         we_tx0_reset_i               =  1'b0            ;              
                              
      reg         we_tx1_beacon_en             =  1'b0            ;              
      reg         we_tx1_clk_rdy               =  1'b1            ;              
      reg         we_tx1_data_en               =  1'b1            ;              
      reg         we_tx1_detrx_req             =  1'b0            ;              
      reg         we_tx1_disable               =  1'b0            ;              
      reg  [5:0]  we_tx1_eq_main               =  6'd20           ;              
      reg  [5:0]  we_tx1_eq_post               =  6'd0            ;              
      reg  [5:0]  we_tx1_eq_pre                =  6'd0            ;              
      reg         we_tx1_flyover_data_m        =  1'b0            ;               
      reg         we_tx1_flyover_data_p        =  1'b0            ;               
      reg         we_tx1_lpd                   =  1'b0            ;              
      reg         we_tx1_master_mplla_state    =  1'b1            ;              
      reg         we_tx1_master_mpllb_state    =  1'b1            ;              
      reg         we_tx1_mpll_en               =  1'b1            ;              
      reg         we_tx1_mpllb_sel             =  1'b0            ;              
      reg  [1:0]  we_tx1_pstate                =  2'b00           ;              
      reg  [2:0]  we_tx1_rate                  =  3'd3            ;              
      reg         we_tx1_req                   =  1'b0            ;               
      reg         we_tx1_reset_i               =  1'b0            ;              
                               
      reg         we_l0_pcs_native_en          =  1'b1            ;              
      reg         we_l0_pcs_standard_en        =  1'b0            ;              
      reg         we_l0_gate_stclk_en          =  1'b1            ;              
      reg         we_l0_gate_drclk_en          =  1'b0            ;              
//    reg         we_l0_tx_usrclk              =                  ;              
//    reg         we_l0_tx_usr2clk             =                  ;              
//    reg         we_l0_rx_usrclk              =                  ;              
//    reg         we_l0_rx_usr2clk             =                  ;              
      reg  [3:0]  we_l0_tx_outclk_sel          =  4'd3            ;               
      reg  [3:0]  we_l0_tx_out2clk_sel         =  4'd4            ;              
      reg  [1:0]  we_l0_tx_xclk_sel            =  2'b00           ;              
      reg  [1:0]  we_l0_tx_iclk_sel            =  2'b01           ;              
      reg  [1:0]  we_l0_rx_outclk_sel          =  2'b01           ;              
      reg  [1:0]  we_l0_rx_out2clk_sel         =  2'b00           ;               
      reg  [1:0]  we_l0_rx_xclk_sel            =  2'b00           ;               
      reg  [1:0]  we_l0_rx_rmatclk_sel         =  2'b01           ;              
      reg  [2:0]  we_l0_rx_oclk_sel            =  3'b011          ;              
      reg         we_l0_tx_pcs_rst_n           =  1'b1            ;              
      reg         we_l0_tx_pcsfifo_rst_n       =  1'b1            ;              
//    reg  [79:0] we_l0_tx_usrdata_in          =                  ;              
      reg  [2:0]  we_l0_tx_usrdata_width       =  3'd1            ;              
      reg  [7:0]  we_l0_tx_usrdatak_in         =  8'b0000_0000    ;              
      reg  [7:0]  we_l0_tx_forcedisp_in        =  8'b0000_0000    ;              
      reg  [7:0]  we_l0_tx_forcedispval_in     =  8'b0000_0000    ;              
      reg         we_l0_tx_byteserial_en       =  1'b0            ;              
      reg         we_l0_tx_byteserial_mode     =  1'b0            ;              
      reg         we_l0_tx_pcsfifo_en          =  1'b0            ;              
      reg         we_l0_tx_enc_en              =  1'b0            ;              
      reg         we_l0_tx_bitslip_en          =  1'b0            ;              
      reg  [4:0]  we_l0_tx_bitslip_width       =  5'd0            ;              
      reg         we_l0_tx_prbs_en             =  1'b0            ;              
      reg  [2:0]  we_l0_tx_prbs_sel            =  3'b100          ;              
      reg         we_l0_tx_prbs_force_err      =  1'b0            ;              
      reg  [1:0]  we_l0_tx_pmadata_width       =  2'b01           ;               
                               
      reg         we_l1_pcs_native_en          =  1'b1            ;              
      reg         we_l1_pcs_standard_en        =  1'b0            ;              
      reg         we_l1_gate_stclk_en          =  1'b1            ;              
      reg         we_l1_gate_drclk_en          =  1'b0            ;              
//    reg         we_l1_tx_usrclk              =                  ;              
//    reg         we_l1_tx_usr2clk             =                  ;              
//    reg         we_l1_rx_usrclk              =                  ;              
//    reg         we_l1_rx_usr2clk             =                  ;              
      reg  [3:0]  we_l1_tx_outclk_sel          =  4'd3            ;               
      reg  [3:0]  we_l1_tx_out2clk_sel         =  4'd4            ;              
      reg  [1:0]  we_l1_tx_xclk_sel            =  2'b00           ;              
      reg  [1:0]  we_l1_tx_iclk_sel            =  2'b01           ;              
      reg  [1:0]  we_l1_rx_outclk_sel          =  2'b01           ;              
      reg  [1:0]  we_l1_rx_out2clk_sel         =  2'b00           ;               
      reg  [1:0]  we_l1_rx_xclk_sel            =  2'b00           ;              
      reg  [1:0]  we_l1_rx_rmatclk_sel         =  2'b01           ;              
      reg  [2:0]  we_l1_rx_oclk_sel            =  3'b011          ;              
      reg         we_l1_tx_pcs_rst_n           =  1'b1            ;              
      reg         we_l1_tx_pcsfifo_rst_n       =  1'b1            ;              
//    reg  [79:0] we_l1_tx_usrdata_in          =                  ;              
      reg  [2:0]  we_l1_tx_usrdata_width       =  3'd1            ;              
      reg  [7:0]  we_l1_tx_usrdatak_in         =  8'b0000_0001    ;              
      reg  [7:0]  we_l1_tx_forcedisp_in        =  8'b0000_0000    ;              
      reg  [7:0]  we_l1_tx_forcedispval_in     =  8'b0000_0000    ;              
      reg         we_l1_tx_byteserial_en       =  1'b0            ;              
      reg         we_l1_tx_byteserial_mode     =  1'b0            ;              
      reg         we_l1_tx_pcsfifo_en          =  1'b0            ;              
      reg         we_l1_tx_enc_en              =  1'b0            ;              
      reg         we_l1_tx_bitslip_en          =  1'b0            ;              
      reg  [4:0]  we_l1_tx_bitslip_width       =  5'd0            ;              
      reg         we_l1_tx_prbs_en             =  1'b0            ;              
      reg  [2:0]  we_l1_tx_prbs_sel            =  3'b100          ;              
      reg         we_l1_tx_prbs_force_err      =  1'b0            ;              
      reg  [1:0]  we_l1_tx_pmadata_width       =  2'b01           ;               
                              
      reg         we_l0_rx_pcs_rst_n           =  1'b1            ;              
      reg         we_l0_rx_pcsfifo_rst_n       =  1'b1            ;              
      reg         we_l0_rx_rmatchfifo_rst_n    =  1'b1            ;              
      reg  [1:0]  we_l0_rx_pmadata_width       =  2'b01           ;              
      reg         we_l0_rx_prbs_en             =  1'b0            ;              
      reg  [2:0]  we_l0_rx_prbs_sel            =  3'b100          ;              
      reg         we_l0_rx_wordalign_en        =  1'b0            ;              
      reg         we_l0_rx_pcommaalign_en      =  1'b1            ;              
      reg         we_l0_rx_mcommaalign_en      =  1'b1            ;              
      reg         we_l0_rx_slide               =  1'b0            ;              
      reg  [9:0]  we_l0_rx_aligncomma_mask     =  10'b11_1111_1111;              
      reg         we_l0_rx_aligncomma_double   =  1'b0            ;              
      reg  [9:0]  we_l0_rx_alignmcomma_value   =  10'b11000_00101 ;              
      reg         we_l0_rx_alignmcomma_det     =  1'b0            ;              
      reg  [9:0]  we_l0_rx_alignpcomma_value   =  10'b00111_11010 ;              
      reg         we_l0_rx_alignpcomma_det     =  1'b0            ;              
      reg         we_l0_rx_show_realigncomma   =  1'b0            ;              
      reg  [1:0]  we_l0_rx_slidemode           =  2'd3            ;              
      reg         we_l0_rx_ratmatfifo_en       =  1'b1            ;              
      reg  [9:0]  we_l0_rx_rmfifo_skip_1byte   =  10'd0           ;              
      reg  [9:0]  we_l0_rx_rmfifo_skip_2byte   =  10'd0           ;              
      reg  [9:0]  we_l0_rx_rmfifo_skip_3byte   =  10'd0           ;              
      reg  [9:0]  we_l0_rx_rmfifo_skip_4byte   =  10'd0           ;              
      reg         we_l0_rx_rmfifo_skip4byte_en =  1'd0            ;              
      reg         we_l0_rx_rmfifo_skip2byte_en =  1'd0            ;              
      reg  [3:0]  we_l0_rx_rmfifo_min_ipg_cnt  =  4'b0            ;              
      reg  [4:0]  we_l0_rx_rmfifo_high_mark    =  5'd29           ;              
      reg  [4:0]  we_l0_rx_rmfifo_low_mark     =  5'd3            ;              
      reg         we_l0_rx_dec_en              =  1'b0            ;              
      reg         we_l0_rx_pcsfifo_en          =  1'b0            ;              
      reg         we_l0_rx_bytedeserial_en     =  1'b0            ;              
      reg         we_l0_rx_bytedeserial_mode   =  1'b0            ;              
      reg  [2:0]  we_l0_rx_usrdata_width       =  3'd1            ;              
      reg         we_l0_rx_prbs_err_clr        =  1'b0            ;              
      reg         we_l0_loopback_en            =  1'b0            ;              
      reg  [1:0]  we_l0_loopback_mode          =  2'b00           ;              
                            
      reg         we_l1_rx_pcs_rst_n           =  1'b1            ;              
      reg         we_l1_rx_pcsfifo_rst_n       =  1'b1            ;              
      reg         we_l1_rx_rmatchfifo_rst_n    =  1'b1            ;              
      reg  [1:0]  we_l1_rx_pmadata_width       =  2'b01           ;              
      reg         we_l1_rx_prbs_en             =  1'b0            ;              
      reg  [2:0]  we_l1_rx_prbs_sel            =  3'b100          ;              
      reg         we_l1_rx_wordalign_en        =  1'b0            ;              
      reg         we_l1_rx_pcommaalign_en      =  1'b1            ;              
      reg         we_l1_rx_mcommaalign_en      =  1'b1            ;              
      reg         we_l1_rx_slide               =  1'b0            ;              
      reg  [9:0]  we_l1_rx_aligncomma_mask     =  10'b11_1111_1111;              
      reg         we_l1_rx_aligncomma_double   =  1'b0            ;              
      reg  [9:0]  we_l1_rx_alignmcomma_value   =  10'b11000_00101 ;              
      reg         we_l1_rx_alignmcomma_det     =  1'b0            ;              
      reg  [9:0]  we_l1_rx_alignpcomma_value   =  10'b00111_11010 ;              
      reg         we_l1_rx_alignpcomma_det     =  1'b0            ;              
      reg         we_l1_rx_show_realigncomma   =  1'b0            ;              
      reg  [1:0]  we_l1_rx_slidemode           =  2'd3            ;              
      reg         we_l1_rx_ratmatfifo_en       =  1'b1            ;              
      reg  [9:0]  we_l1_rx_rmfifo_skip_1byte   =  10'd0           ;              
      reg  [9:0]  we_l1_rx_rmfifo_skip_2byte   =  10'd0           ;              
      reg  [9:0]  we_l1_rx_rmfifo_skip_3byte   =  10'd0           ;              
      reg  [9:0]  we_l1_rx_rmfifo_skip_4byte   =  10'd0           ;              
      reg         we_l1_rx_rmfifo_skip4byte_en =  1'd0            ;              
      reg         we_l1_rx_rmfifo_skip2byte_en =  1'd0            ;              
      reg  [3:0]  we_l1_rx_rmfifo_min_ipg_cnt  =  4'b0            ;              
      reg  [4:0]  we_l1_rx_rmfifo_high_mark    =  5'd29           ;              
      reg  [4:0]  we_l1_rx_rmfifo_low_mark     =  5'd3            ;              
      reg         we_l1_rx_dec_en              =  1'b0            ;              
      reg         we_l1_rx_pcsfifo_en          =  1'b0            ;              
      reg         we_l1_rx_bytedeserial_en     =  1'b0            ;              
      reg         we_l1_rx_bytedeserial_mode   =  1'b0            ;              
      reg  [2:0]  we_l1_rx_usrdata_width       =  3'd1            ;              
      reg         we_l1_rx_prbs_err_clr        =  1'b0            ;              
      reg         we_l1_loopback_en            =  1'b0            ;              
      reg  [1:0]  we_l1_loopback_mode          =  2'b00           ;              
                             
      reg  [3:0]  we_l0_debug_sel              =  4'd0            ;              
      reg  [3:0]  we_l1_debug_sel              =  4'd0            ;              
      reg  [1:0]  we_clk0_sel                  =  2'b0            ;              
      reg  [1:0]  we_clk1_sel                  =  2'b0            ;              
      reg  [1:0]  we_clk2_sel                  =  2'b0            ; 

//===================================================================
    
      assign w_mplla_bandwidth           =  we_mplla_bandwidth           ;              
      assign w_mplla_multiplier          =  we_mplla_multiplier          ;              
      assign w_mplla_recal_bank_sel      =  we_mplla_recal_bank_sel      ;               
      assign w_pg_reset                  =  we_pg_reset                  ;                    
      assign w_phy_reset_i               =  we_phy_reset_i               ;               
      assign w_ref_clk_en_i              =  we_ref_clk_en_i              ;               
      assign w_scan_mode                 =  we_scan_mode                 ;              
      assign w_scan_set_rst              =  we_scan_set_rst              ;              
      assign w_scan_shift                =  we_scan_shift                ;              
      assign w_scan_shift_cg             =  we_scan_shift_cg             ;            
      assign w_scan_sel                  =  we_scan_sel                  ;               
      assign w_scan_phy_clk              =  we_scan_phy_clk              ;              
//    assign w_scan_data_in              =  we_scan_data_in              ;              
      assign w_sram_bypass_i             =  we_sram_bypass_i             ;               
//    assign w_sram_ext_ld_done_i        =  we_sram_ext_ld_done_i        ;               
      assign w_sup_misc                  =  we_sup_misc                  ;              
      assign w_test_burnin               =  we_test_burnin               ;              
      assign w_test_flyover_en           =  we_test_flyover_en           ;               
      assign w_test_powerdown_i          =  we_test_powerdown_i          ;              
      assign w_test_stop_clk_en          =  we_test_stop_clk_en          ;              
      assign w_sram_sel_i                =  we_sram_sel_i                ;              
                                            
      assign w_rx0_adapt_in_prog         =  we_rx0_adapt_in_prog         ;              
      assign w_rx0_adapt_mode            =  we_rx0_adapt_mode            ;              
      assign w_rx0_adapt_req             =  we_rx0_adapt_req             ;              
      assign w_rx0_adapt_sel             =  we_rx0_adapt_sel             ;              
      assign w_rx0_cdr_ppm_max           =  we_rx0_cdr_ppm_max           ;              
      assign w_rx0_cdr_ssc_en            =  we_rx0_cdr_ssc_en            ;              
      assign w_rx0_cdr_vco_freqband      =  we_rx0_cdr_vco_freqband      ;              
      assign w_rx0_cdr_vco_step_ctrl     =  we_rx0_cdr_vco_step_ctrl     ;              
      assign w_rx0_data_en               =  we_rx0_data_en               ;              
      assign w_rx0_delta_iq              =  we_rx0_delta_iq              ;              
      assign w_rx0_dfe_bypass            =  we_rx0_dfe_bypass            ;              
      assign w_rx0_disable               =  we_rx0_disable               ;              
      assign w_rx0_eq_ctle_boost         =  we_rx0_eq_ctle_boost         ;              
      assign w_rx0_eq_dfe_tap            =  we_rx0_eq_dfe_tap            ;              
      assign w_rx0_eq_vga1_gain          =  we_rx0_eq_vga1_gain          ;              
      assign w_rx0_eq_vga2_gain          =  we_rx0_eq_vga2_gain          ;               
      assign w_rx0_invert                =  we_rx0_invert                ;              
      assign w_rx0_lpd                   =  we_rx0_lpd                   ;              
      assign w_rx0_misc                  =  we_rx0_misc                  ;              
      assign w_rx0_pstate                =  we_rx0_pstate                ;              
      assign w_rx0_rate                  =  we_rx0_rate                  ;              
      assign w_rx0_ref_ld_val            =  we_rx0_ref_ld_val            ;              
      assign w_rx0_req                   =  we_rx0_req                   ;               
      assign w_rx0_reset_i               =  we_rx0_reset_i               ;               
      assign w_rx0_term_acdc             =  we_rx0_term_acdc             ;              
      assign w_rx0_term_en               =  we_rx0_term_en               ;               
      assign w_rx0_vco_ld_val            =  we_rx0_vco_ld_val            ;              
                                                  
      assign w_rx1_adapt_in_prog         =  we_rx1_adapt_in_prog         ;              
      assign w_rx1_adapt_mode            =  we_rx1_adapt_mode            ;              
      assign w_rx1_adapt_req             =  we_rx1_adapt_req             ;              
      assign w_rx1_adapt_sel             =  we_rx1_adapt_sel             ;              
      assign w_rx1_cdr_ppm_max           =  we_rx1_cdr_ppm_max           ;              
      assign w_rx1_cdr_ssc_en            =  we_rx1_cdr_ssc_en            ;              
      assign w_rx1_cdr_vco_freqband      =  we_rx1_cdr_vco_freqband      ;              
      assign w_rx1_cdr_vco_step_ctrl     =  we_rx1_cdr_vco_step_ctrl     ;              
      assign w_rx1_data_en               =  we_rx1_data_en               ;              
      assign w_rx1_delta_iq              =  we_rx1_delta_iq              ;              
      assign w_rx1_dfe_bypass            =  we_rx1_dfe_bypass            ;              
      assign w_rx1_disable               =  we_rx1_disable               ;              
      assign w_rx1_eq_ctle_boost         =  we_rx1_eq_ctle_boost         ;              
      assign w_rx1_eq_dfe_tap            =  we_rx1_eq_dfe_tap            ;              
      assign w_rx1_eq_vga1_gain          =  we_rx1_eq_vga1_gain          ;              
      assign w_rx1_eq_vga2_gain          =  we_rx1_eq_vga2_gain          ;               
      assign w_rx1_invert                =  we_rx1_invert                ;              
      assign w_rx1_lpd                   =  we_rx1_lpd                   ;              
      assign w_rx1_misc                  =  we_rx1_misc                  ;              
      assign w_rx1_pstate                =  we_rx1_pstate                ;              
      assign w_rx1_rate                  =  we_rx1_rate                  ;              
      assign w_rx1_ref_ld_val            =  we_rx1_ref_ld_val            ;              
      assign w_rx1_req                   =  we_rx1_req                   ;               
      assign w_rx1_reset_i               =  we_rx1_reset_i               ;               
      assign w_rx1_term_acdc             =  we_rx1_term_acdc             ;              
      assign w_rx1_term_en               =  we_rx1_term_en               ;               
      assign w_rx1_vco_ld_val            =  we_rx1_vco_ld_val            ;              
                                             
      assign w_tx0_beacon_en             =  we_tx0_beacon_en             ;              
      assign w_tx0_clk_rdy               =  we_tx0_clk_rdy               ;              
      assign w_tx0_data_en               =  we_tx0_data_en               ;              
      assign w_tx0_detrx_req             =  we_tx0_detrx_req             ;              
      assign w_tx0_disable               =  we_tx0_disable               ;              
      assign w_tx0_eq_main               =  we_tx0_eq_main               ;              
      assign w_tx0_eq_post               =  we_tx0_eq_post               ;              
      assign w_tx0_eq_pre                =  we_tx0_eq_pre                ;              
      assign w_tx0_flyover_data_m        =  we_tx0_flyover_data_m        ;               
      assign w_tx0_flyover_data_p        =  we_tx0_flyover_data_p        ;               
      assign w_tx0_lpd                   =  we_tx0_lpd                   ;              
      assign w_tx0_master_mplla_state    =  we_tx0_master_mplla_state    ;              
      assign w_tx0_master_mpllb_state    =  we_tx0_master_mpllb_state    ;              
      assign w_tx0_mpll_en               =  we_tx0_mpll_en               ;              
      assign w_tx0_mpllb_sel             =  we_tx0_mpllb_sel             ;              
      assign w_tx0_pstate                =  we_tx0_pstate                ;              
      assign w_tx0_rate                  =  we_tx0_rate                  ;              
      assign w_tx0_req                   =  we_tx0_req                   ;               
      assign w_tx0_reset_i               =  we_tx0_reset_i               ;              
                                                
      assign w_tx1_beacon_en             =  we_tx1_beacon_en             ;              
      assign w_tx1_clk_rdy               =  we_tx1_clk_rdy               ;              
      assign w_tx1_data_en               =  we_tx1_data_en               ;              
      assign w_tx1_detrx_req             =  we_tx1_detrx_req             ;              
      assign w_tx1_disable               =  we_tx1_disable               ;              
      assign w_tx1_eq_main               =  we_tx1_eq_main               ;              
      assign w_tx1_eq_post               =  we_tx1_eq_post               ;              
      assign w_tx1_eq_pre                =  we_tx1_eq_pre                ;              
      assign w_tx1_flyover_data_m        =  we_tx1_flyover_data_m        ;               
      assign w_tx1_flyover_data_p        =  we_tx1_flyover_data_p        ;               
      assign w_tx1_lpd                   =  we_tx1_lpd                   ;              
      assign w_tx1_master_mplla_state    =  we_tx1_master_mplla_state    ;              
      assign w_tx1_master_mpllb_state    =  we_tx1_master_mpllb_state    ;              
      assign w_tx1_mpll_en               =  we_tx1_mpll_en               ;              
      assign w_tx1_mpllb_sel             =  we_tx1_mpllb_sel             ;              
      assign w_tx1_pstate                =  we_tx1_pstate                ;              
      assign w_tx1_rate                  =  we_tx1_rate                  ;              
      assign w_tx1_req                   =  we_tx1_req                   ;               
      assign w_tx1_reset_i               =  we_tx1_reset_i               ;              
                                               
      assign w_l0_pcs_native_en          =  we_l0_pcs_native_en          ;              
      assign w_l0_pcs_standard_en        =  we_l0_pcs_standard_en        ;              
      assign w_l0_gate_stclk_en          =  we_l0_gate_stclk_en          ;              
      assign w_l0_gate_drclk_en          =  we_l0_gate_drclk_en          ;              
//    assign w_l0_tx_usrclk              =  we_l0_tx_usrclk              ;              
//    assign w_l0_tx_usr2clk             =  we_l0_tx_usr2clk             ;              
//    assign w_l0_rx_usrclk              =  we_l0_rx_usrclk              ;              
//    assign w_l0_rx_usr2clk             =  we_l0_rx_usr2clk             ;              
      assign w_l0_tx_outclk_sel          =  we_l0_tx_outclk_sel          ;               
      assign w_l0_tx_out2clk_sel         =  we_l0_tx_out2clk_sel         ;              
      assign w_l0_tx_xclk_sel            =  we_l0_tx_xclk_sel            ;              
      assign w_l0_tx_iclk_sel            =  we_l0_tx_iclk_sel            ;              
      assign w_l0_rx_outclk_sel          =  we_l0_rx_outclk_sel          ;              
      assign w_l0_rx_out2clk_sel         =  we_l0_rx_out2clk_sel         ;               
      assign w_l0_rx_xclk_sel            =  we_l0_rx_xclk_sel            ;               
      assign w_l0_rx_rmatclk_sel         =  we_l0_rx_rmatclk_sel         ;              
      assign w_l0_rx_oclk_sel            =  we_l0_rx_oclk_sel            ;              
      assign w_l0_tx_pcs_rst_n           =  we_l0_tx_pcs_rst_n           ;              
      assign w_l0_tx_pcsfifo_rst_n       =  we_l0_tx_pcsfifo_rst_n       ;              
//    assign w_l0_tx_usrdata_in          =  we_l0_tx_usrdata_in          ;              
      assign w_l0_tx_usrdata_width       =  we_l0_tx_usrdata_width       ;              
      assign w_l0_tx_usrdatak_in         =  we_l0_tx_usrdatak_in         ;              
      assign w_l0_tx_forcedisp_in        =  we_l0_tx_forcedisp_in        ;              
      assign w_l0_tx_forcedispval_in     =  we_l0_tx_forcedispval_in     ;              
      assign w_l0_tx_byteserial_en       =  we_l0_tx_byteserial_en       ;              
      assign w_l0_tx_byteserial_mode     =  we_l0_tx_byteserial_mode     ;              
      assign w_l0_tx_pcsfifo_en          =  we_l0_tx_pcsfifo_en          ;              
      assign w_l0_tx_enc_en              =  we_l0_tx_enc_en              ;              
      assign w_l0_tx_bitslip_en          =  we_l0_tx_bitslip_en          ;              
      assign w_l0_tx_bitslip_width       =  we_l0_tx_bitslip_width       ;              
      assign w_l0_tx_prbs_en             =  we_l0_tx_prbs_en             ;              
      assign w_l0_tx_prbs_sel            =  we_l0_tx_prbs_sel            ;              
      assign w_l0_tx_prbs_force_err      =  we_l0_tx_prbs_force_err      ;              
      assign w_l0_tx_pmadata_width       =  we_l0_tx_pmadata_width       ;               
                                                
      assign w_l1_pcs_native_en          =  we_l1_pcs_native_en          ;              
      assign w_l1_pcs_standard_en        =  we_l1_pcs_standard_en        ;              
      assign w_l1_gate_stclk_en          =  we_l1_gate_stclk_en          ;              
      assign w_l1_gate_drclk_en          =  we_l1_gate_drclk_en          ;              
//    assign w_l1_tx_usrclk              =  we_l1_tx_usrclk              ;              
//    assign w_l1_tx_usr2clk             =  we_l1_tx_usr2clk             ;              
//    assign w_l1_rx_usrclk              =  we_l1_rx_usrclk              ;              
//    assign w_l1_rx_usr2clk             =  we_l1_rx_usr2clk             ;              
      assign w_l1_tx_outclk_sel          =  we_l1_tx_outclk_sel          ;               
      assign w_l1_tx_out2clk_sel         =  we_l1_tx_out2clk_sel         ;              
      assign w_l1_tx_xclk_sel            =  we_l1_tx_xclk_sel            ;              
      assign w_l1_tx_iclk_sel            =  we_l1_tx_iclk_sel            ;              
      assign w_l1_rx_outclk_sel          =  we_l1_rx_outclk_sel          ;              
      assign w_l1_rx_out2clk_sel         =  we_l1_rx_out2clk_sel         ;               
      assign w_l1_rx_xclk_sel            =  we_l1_rx_xclk_sel            ;              
      assign w_l1_rx_rmatclk_sel         =  we_l1_rx_rmatclk_sel         ;              
      assign w_l1_rx_oclk_sel            =  we_l1_rx_oclk_sel            ;              
      assign w_l1_tx_pcs_rst_n           =  we_l1_tx_pcs_rst_n           ;              
      assign w_l1_tx_pcsfifo_rst_n       =  we_l1_tx_pcsfifo_rst_n       ;              
//    assign w_l1_tx_usrdata_in          =  we_l1_tx_usrdata_in          ;              
      assign w_l1_tx_usrdata_width       =  we_l1_tx_usrdata_width       ;              
      assign w_l1_tx_usrdatak_in         =  we_l1_tx_usrdatak_in         ;              
      assign w_l1_tx_forcedisp_in        =  we_l1_tx_forcedisp_in        ;              
      assign w_l1_tx_forcedispval_in     =  we_l1_tx_forcedispval_in     ;              
      assign w_l1_tx_byteserial_en       =  we_l1_tx_byteserial_en       ;              
      assign w_l1_tx_byteserial_mode     =  we_l1_tx_byteserial_mode     ;              
      assign w_l1_tx_pcsfifo_en          =  we_l1_tx_pcsfifo_en          ;              
      assign w_l1_tx_enc_en              =  we_l1_tx_enc_en              ;              
      assign w_l1_tx_bitslip_en          =  we_l1_tx_bitslip_en          ;              
      assign w_l1_tx_bitslip_width       =  we_l1_tx_bitslip_width       ;              
      assign w_l1_tx_prbs_en             =  we_l1_tx_prbs_en             ;              
      assign w_l1_tx_prbs_sel            =  we_l1_tx_prbs_sel            ;              
      assign w_l1_tx_prbs_force_err      =  we_l1_tx_prbs_force_err      ;              
      assign w_l1_tx_pmadata_width       =  we_l1_tx_pmadata_width       ;               
                                             
      assign w_l0_rx_pcs_rst_n           =  we_l0_rx_pcs_rst_n           ;              
      assign w_l0_rx_pcsfifo_rst_n       =  we_l0_rx_pcsfifo_rst_n       ;              
      assign w_l0_rx_rmatchfifo_rst_n    =  we_l0_rx_rmatchfifo_rst_n    ;              
      assign w_l0_rx_pmadata_width       =  we_l0_rx_pmadata_width       ;              
      assign w_l0_rx_prbs_en             =  we_l0_rx_prbs_en             ;              
      assign w_l0_rx_prbs_sel            =  we_l0_rx_prbs_sel            ;              
      assign w_l0_rx_wordalign_en        =  we_l0_rx_wordalign_en        ;              
      assign w_l0_rx_pcommaalign_en      =  we_l0_rx_pcommaalign_en      ;              
      assign w_l0_rx_mcommaalign_en      =  we_l0_rx_mcommaalign_en      ;              
      assign w_l0_rx_slide               =  we_l0_rx_slide               ;              
      assign w_l0_rx_aligncomma_mask     =  we_l0_rx_aligncomma_mask     ;              
      assign w_l0_rx_aligncomma_double   =  we_l0_rx_aligncomma_double   ;              
      assign w_l0_rx_alignmcomma_value   =  we_l0_rx_alignmcomma_value   ;              
      assign w_l0_rx_alignmcomma_det     =  we_l0_rx_alignmcomma_det     ;              
      assign w_l0_rx_alignpcomma_value   =  we_l0_rx_alignpcomma_value   ;              
      assign w_l0_rx_alignpcomma_det     =  we_l0_rx_alignpcomma_det     ;              
      assign w_l0_rx_show_realigncomma   =  we_l0_rx_show_realigncomma   ;              
      assign w_l0_rx_slidemode           =  we_l0_rx_slidemode           ;              
      assign w_l0_rx_ratmatfifo_en       =  we_l0_rx_ratmatfifo_en       ;              
      assign w_l0_rx_rmfifo_skip_1byte   =  we_l0_rx_rmfifo_skip_1byte   ;              
      assign w_l0_rx_rmfifo_skip_2byte   =  we_l0_rx_rmfifo_skip_2byte   ;              
      assign w_l0_rx_rmfifo_skip_3byte   =  we_l0_rx_rmfifo_skip_3byte   ;              
      assign w_l0_rx_rmfifo_skip_4byte   =  we_l0_rx_rmfifo_skip_4byte   ;              
      assign w_l0_rx_rmfifo_skip4byte_en =  we_l0_rx_rmfifo_skip4byte_en ;              
      assign w_l0_rx_rmfifo_skip2byte_en =  we_l0_rx_rmfifo_skip2byte_en ;              
      assign w_l0_rx_rmfifo_min_ipg_cnt  =  we_l0_rx_rmfifo_min_ipg_cnt  ;              
      assign w_l0_rx_rmfifo_high_mark    =  we_l0_rx_rmfifo_high_mark    ;              
      assign w_l0_rx_rmfifo_low_mark     =  we_l0_rx_rmfifo_low_mark     ;              
      assign w_l0_rx_dec_en              =  we_l0_rx_dec_en              ;              
      assign w_l0_rx_pcsfifo_en          =  we_l0_rx_pcsfifo_en          ;              
      assign w_l0_rx_bytedeserial_en     =  we_l0_rx_bytedeserial_en     ;              
      assign w_l0_rx_bytedeserial_mode   =  we_l0_rx_bytedeserial_mode   ;              
      assign w_l0_rx_usrdata_width       =  we_l0_rx_usrdata_width       ;              
      assign w_l0_rx_prbs_err_clr        =  we_l0_rx_prbs_err_clr        ;              
      assign w_l0_loopback_en            =  we_l0_loopback_en            ;              
      assign w_l0_loopback_mode          =  we_l0_loopback_mode          ;              
                                            
      assign w_l1_rx_pcs_rst_n           =  we_l1_rx_pcs_rst_n           ;              
      assign w_l1_rx_pcsfifo_rst_n       =  we_l1_rx_pcsfifo_rst_n       ;              
      assign w_l1_rx_rmatchfifo_rst_n    =  we_l1_rx_rmatchfifo_rst_n    ;              
      assign w_l1_rx_pmadata_width       =  we_l1_rx_pmadata_width       ;              
      assign w_l1_rx_prbs_en             =  we_l1_rx_prbs_en             ;              
      assign w_l1_rx_prbs_sel            =  we_l1_rx_prbs_sel            ;              
      assign w_l1_rx_wordalign_en        =  we_l1_rx_wordalign_en        ;              
      assign w_l1_rx_pcommaalign_en      =  we_l1_rx_pcommaalign_en      ;              
      assign w_l1_rx_mcommaalign_en      =  we_l1_rx_mcommaalign_en      ;              
      assign w_l1_rx_slide               =  we_l1_rx_slide               ;              
      assign w_l1_rx_aligncomma_mask     =  we_l1_rx_aligncomma_mask     ;              
      assign w_l1_rx_aligncomma_double   =  we_l1_rx_aligncomma_double   ;              
      assign w_l1_rx_alignmcomma_value   =  we_l1_rx_alignmcomma_value   ;              
      assign w_l1_rx_alignmcomma_det     =  we_l1_rx_alignmcomma_det     ;              
      assign w_l1_rx_alignpcomma_value   =  we_l1_rx_alignpcomma_value   ;              
      assign w_l1_rx_alignpcomma_det     =  we_l1_rx_alignpcomma_det     ;              
      assign w_l1_rx_show_realigncomma   =  we_l1_rx_show_realigncomma   ;              
      assign w_l1_rx_slidemode           =  we_l1_rx_slidemode           ;              
      assign w_l1_rx_ratmatfifo_en       =  we_l1_rx_ratmatfifo_en       ;              
      assign w_l1_rx_rmfifo_skip_1byte   =  we_l1_rx_rmfifo_skip_1byte   ;              
      assign w_l1_rx_rmfifo_skip_2byte   =  we_l1_rx_rmfifo_skip_2byte   ;              
      assign w_l1_rx_rmfifo_skip_3byte   =  we_l1_rx_rmfifo_skip_3byte   ;              
      assign w_l1_rx_rmfifo_skip_4byte   =  we_l1_rx_rmfifo_skip_4byte   ;              
      assign w_l1_rx_rmfifo_skip4byte_en =  we_l1_rx_rmfifo_skip4byte_en ;              
      assign w_l1_rx_rmfifo_skip2byte_en =  we_l1_rx_rmfifo_skip2byte_en ;              
      assign w_l1_rx_rmfifo_min_ipg_cnt  =  we_l1_rx_rmfifo_min_ipg_cnt  ;              
      assign w_l1_rx_rmfifo_high_mark    =  we_l1_rx_rmfifo_high_mark    ;              
      assign w_l1_rx_rmfifo_low_mark     =  we_l1_rx_rmfifo_low_mark     ;              
      assign w_l1_rx_dec_en              =  we_l1_rx_dec_en              ;              
      assign w_l1_rx_pcsfifo_en          =  we_l1_rx_pcsfifo_en          ;              
      assign w_l1_rx_bytedeserial_en     =  we_l1_rx_bytedeserial_en     ;              
      assign w_l1_rx_bytedeserial_mode   =  we_l1_rx_bytedeserial_mode   ;              
      assign w_l1_rx_usrdata_width       =  we_l1_rx_usrdata_width       ;              
      assign w_l1_rx_prbs_err_clr        =  we_l1_rx_prbs_err_clr        ;              
      assign w_l1_loopback_en            =  we_l1_loopback_en            ;              
      assign w_l1_loopback_mode          =  we_l1_loopback_mode          ;              
                                              
      assign w_l0_debug_sel              =  we_l0_debug_sel              ;              
      assign w_l1_debug_sel              =  we_l1_debug_sel              ;              
      assign w_clk0_sel                  =  we_clk0_sel                  ;              
      assign w_clk1_sel                  =  we_clk1_sel                  ;              
      assign w_clk2_sel                  =  we_clk2_sel                  ;
      




reg		  [3:0]	cri_state;
parameter		cri_idle=4'd0;
parameter		cri_wait=4'd1;
parameter		cri_write=4'd2;
parameter		cri_read=4'd3;

reg  [31:0]     w_data_error;


always@(posedge clk)
begin
	if(!rst_n)
		cri_state<=cri_idle;
	else
	begin
	case(cri_state)
	cri_idle:if(cr_para_ack_o==0)
				cri_state<=cri_wait;
			 else
			 	cri_state<=cri_idle;
	cri_wait:if(ubus_ren && ubus_raddr[16] == 1'b0)
			 		cri_state<=cri_read;	
			 else	if(ubus_wen && ubus_waddr[16] == 1'b0)
			 		cri_state<=cri_write;	
			 else
			 	cri_state<=cri_wait;
	cri_write:if(cr_para_ack_o==1)
					cri_state<=cri_wait;
			  else
			  		cri_state<=cri_write;
	cri_read:if(cr_para_ack_o==1)
					cri_state<=cri_wait;
			  else
			  		cri_state<=cri_read;
	default:cri_state<=cri_idle;
	endcase
	end	
end


always@(posedge clk)
begin
	if(cri_state==cri_wait)
		if(ubus_ren && ubus_raddr[16] == 1'b0)
			begin
				cr_para_rd_en_i<=1;
				cr_para_wr_en_i<=0;
				cr_para_addr_i<=ubus_raddr[15:0];
				cr_rapa_wr_data_i<=16'd0;
			end
		else if(ubus_wen && ubus_waddr[16] == 1'b0)
			begin
				cr_para_rd_en_i<=0;
				cr_para_wr_en_i<=1;
				cr_para_addr_i<=ubus_waddr[15:0];
				cr_rapa_wr_data_i<=ubus_wdata[15:0];
			end
		else
		begin
			cr_para_rd_en_i<=0;
			cr_para_wr_en_i<=0;
		end
	else
		begin
			cr_para_rd_en_i<=0;
			cr_para_wr_en_i<=0;
		end
end


//======================================================================
// 串口读数据
always@(posedge clk)
begin
        if(ubus_ren && ubus_raddr[16] == 1'b1)
        
                case(ubus_raddr[16:0])
                17'h10000:  ubus_rdata <= {16'd0,r_mplla_bandwidth};
                17'h10001:  ubus_rdata <= {31'd0,r_mplla_force_ack};
                17'h10002:  ubus_rdata <= {24'd0,r_mplla_multiplier};
                17'h10003:  ubus_rdata <= {30'd0,r_mplla_recal_bank_sel};
                17'h10004:  ubus_rdata <= {31'd0,r_mplla_state};
                17'h10005:  ubus_rdata <= {31'd0,r_mpllb_force_ack};
                17'h10006:  ubus_rdata <= {31'd0,r_mpllb_state};
                17'h10007:  ubus_rdata <= {31'd0,r_pcs_pwr_en};
                17'h10008:  ubus_rdata <= {31'd0,r_pg_reset};
                17'h10009:  ubus_rdata <= {31'd0,r_pma_pwr_en};
                17'h1000A:  ubus_rdata <= {31'd0,r_phy_reset_i};
                17'h1000B:  ubus_rdata <= {31'd0,r_ref_clk_en_i};
                17'h1000C:  ubus_rdata <= {31'd0,r_ref_clk_req};
                17'h1000D:  ubus_rdata <= {31'd0,r_ref_clkdet_result};
                17'h1000E:  ubus_rdata <= {31'd0,r_rtune_ack};
                17'h1000F:  ubus_rdata <= {31'd0,r_scan_mode};               
                17'h10010:  ubus_rdata <= {31'd0,r_scan_set_rst};
                17'h10011:  ubus_rdata <= {31'd0,r_scan_shift};
                17'h10012:  ubus_rdata <= {31'd0,r_scan_shift_cg};
                17'h10013:  ubus_rdata <= {26'd0,r_scan_sel};
                17'h10014:  ubus_rdata <= {31'd0,r_scan_phy_clk};
            //  17'h10015:  ubus_rdata <= {0'd0,r_scan_data_in};
            //  17'h10016:  ubus_rdata <= {0'd0,r_scan_data_out};
                17'h10017:  ubus_rdata <= {31'd0,r_sram_bypass_i};
                17'h10018:  ubus_rdata <= {31'd0,r_sram_ext_ld_done_i};
                17'h10019:  ubus_rdata <= {31'd0,r_sram_init_done_o};
                17'h1001A:  ubus_rdata <= {24'd0,r_sup_misc};
                17'h1001B:  ubus_rdata <= {31'd0,r_test_burnin};
                17'h1001C:  ubus_rdata <= {31'd0,r_test_flyover_en};
                17'h1001D:  ubus_rdata <= {31'd0,r_test_powerdown_i};
                17'h1001E:  ubus_rdata <= {31'd0,r_test_stop_clk_en};
                17'h1001F:  ubus_rdata <= {31'd0,r_sram_sel_i};
                
                17'h10020:  ubus_rdata <= {31'd0,r_rx0_ack};
                17'h10021:  ubus_rdata <= {31'd0,r_rx0_adapt_ack};
                17'h10022:  ubus_rdata <= {24'd0,r_rx0_adapt_fom};
                17'h10023:  ubus_rdata <= {31'd0,r_rx0_adapt_in_prog};
                17'h10024:  ubus_rdata <= {30'd0,r_rx0_adapt_mode};
                17'h10025:  ubus_rdata <= {31'd0,r_rx0_adapt_req};
                17'h10026:  ubus_rdata <= {31'd0,r_rx0_adapt_sel};
                17'h10027:  ubus_rdata <= {27'd0,r_rx0_cdr_ppm_max};
                17'h10028:  ubus_rdata <= {31'd0,r_rx0_cdr_ssc_en};
                17'h10029:  ubus_rdata <= {30'd0,r_rx0_cdr_vco_freqband};
                17'h1002A:  ubus_rdata <= {31'd0,r_rx0_cdr_vco_step_ctrl};
                17'h1002B:  ubus_rdata <= {31'd0,r_rx0_data_en};
                17'h1002C:  ubus_rdata <= {28'd0,r_rx0_delta_iq};
                17'h1002D:  ubus_rdata <= {31'd0,r_rx0_dfe_bypass};
                17'h1002E:  ubus_rdata <= {31'd0,r_rx0_disable};
                17'h1002F:  ubus_rdata <= {27'd0,r_rx0_eq_ctle_boost};                
                17'h10030:  ubus_rdata <= {24'd0,r_rx0_eq_dfe_tap};
                17'h10031:  ubus_rdata <= {29'd0,r_rx0_eq_vga1_gain};
                17'h10032:  ubus_rdata <= {29'd0,r_rx0_eq_vga2_gain};
                17'h10033:  ubus_rdata <= {31'd0,r_rx0_flyover_data_m};
                17'h10034:  ubus_rdata <= {31'd0,r_rx0_flyover_data_p};
                17'h10035:  ubus_rdata <= {31'd0,r_rx0_invert};
                17'h10036:  ubus_rdata <= {31'd0,r_rx0_los};
                17'h10037:  ubus_rdata <= {31'd0,r_rx0_lpd};
                17'h10038:  ubus_rdata <= {24'd0,r_rx0_misc};
                17'h10039:  ubus_rdata <= {26'd0,r_rx0_ppm_drift};
                17'h1003A:  ubus_rdata <= {31'd0,r_rx0_ppm_drift_vld};
                17'h1003B:  ubus_rdata <= {30'd0,r_rx0_pstate};
                17'h1003C:  ubus_rdata <= {30'd0,r_rx0_rate};
                17'h1003D:  ubus_rdata <= {25'd0,r_rx0_ref_ld_val};
                17'h1003E:  ubus_rdata <= {31'd0,r_rx0_req};
                17'h1003F:  ubus_rdata <= {31'd0,r_rx0_reset_i};                
                17'h10040:  ubus_rdata <= {31'd0,r_rx0_term_acdc};
                17'h10041:  ubus_rdata <= {31'd0,r_rx0_term_en};
                17'h10042:  ubus_rdata <= {30'd0,r_rx0_txmain_dir};
                17'h10043:  ubus_rdata <= {30'd0,r_rx0_txpost_dir};
                17'h10044:  ubus_rdata <= {30'd0,r_rx0_txpre_dir};
                17'h10045:  ubus_rdata <= {31'd0,r_rx0_valid};
                17'h10046:  ubus_rdata <= {19'd0,r_rx0_vco_ld_val};
                                         
                17'h10047:  ubus_rdata <= {31'd0,r_rx1_ack};
                17'h10048:  ubus_rdata <= {31'd0,r_rx1_adapt_ack};
                17'h10049:  ubus_rdata <= {24'd0,r_rx1_adapt_fom};
                17'h1004A:  ubus_rdata <= {31'd0,r_rx1_adapt_in_prog};
                17'h1004B:  ubus_rdata <= {30'd0,r_rx1_adapt_mode};
                17'h1004C:  ubus_rdata <= {31'd0,r_rx1_adapt_req};
                17'h1004D:  ubus_rdata <= {31'd0,r_rx1_adapt_sel};
                17'h1004E:  ubus_rdata <= {27'd0,r_rx1_cdr_ppm_max};
                17'h1004F:  ubus_rdata <= {31'd0,r_rx1_cdr_ssc_en};                
                17'h10050:  ubus_rdata <= {30'd0,r_rx1_cdr_vco_freqband};
                17'h10051:  ubus_rdata <= {31'd0,r_rx1_cdr_vco_step_ctrl};
                17'h10052:  ubus_rdata <= {31'd0,r_rx1_data_en};
                17'h10053:  ubus_rdata <= {28'd0,r_rx1_delta_iq};
                17'h10054:  ubus_rdata <= {31'd0,r_rx1_dfe_bypass};
                17'h10055:  ubus_rdata <= {31'd0,r_rx1_disable};
                17'h10056:  ubus_rdata <= {27'd0,r_rx1_eq_ctle_boost};
                17'h10057:  ubus_rdata <= {24'd0,r_rx1_eq_dfe_tap};
                17'h10058:  ubus_rdata <= {29'd0,r_rx1_eq_vga1_gain};
                17'h10059:  ubus_rdata <= {29'd0,r_rx1_eq_vga2_gain};
                17'h1005A:  ubus_rdata <= {31'd0,r_rx1_flyover_data_m};
                17'h1005B:  ubus_rdata <= {31'd0,r_rx1_flyover_data_p};
                17'h1005C:  ubus_rdata <= {31'd0,r_rx1_invert};
                17'h1005D:  ubus_rdata <= {31'd0,r_rx1_los};
                17'h1005E:  ubus_rdata <= {31'd0,r_rx1_lpd};
                17'h1005F:  ubus_rdata <= {24'd0,r_rx1_misc};                
                17'h10060:  ubus_rdata <= {26'd0,r_rx1_ppm_drift};
                17'h10061:  ubus_rdata <= {31'd0,r_rx1_ppm_drift_vld};
                17'h10062:  ubus_rdata <= {30'd0,r_rx1_pstate};
                17'h10063:  ubus_rdata <= {30'd0,r_rx1_rate};
                17'h10064:  ubus_rdata <= {25'd0,r_rx1_ref_ld_val};
                17'h10065:  ubus_rdata <= {31'd0,r_rx1_req};
                17'h10066:  ubus_rdata <= {31'd0,r_rx1_reset_i};
                17'h10067:  ubus_rdata <= {31'd0,r_rx1_term_acdc};
                17'h10068:  ubus_rdata <= {31'd0,r_rx1_term_en};
                17'h10069:  ubus_rdata <= {30'd0,r_rx1_txmain_dir};
                17'h1006A:  ubus_rdata <= {30'd0,r_rx1_txpost_dir};
                17'h1006B:  ubus_rdata <= {30'd0,r_rx1_txpre_dir};
                17'h1006C:  ubus_rdata <= {31'd0,r_rx1_valid};
                17'h1006D:  ubus_rdata <= {19'd0,r_rx1_vco_ld_val};
                                  
                17'h1006E:  ubus_rdata <= {31'd0,r_tx0_ack};
                17'h1006F:  ubus_rdata <= {31'd0,r_tx0_beacon_en};                
                17'h10070:  ubus_rdata <= {31'd0,r_tx0_clk_rdy};
                17'h10071:  ubus_rdata <= {31'd0,r_tx0_data_en};
                17'h10072:  ubus_rdata <= {31'd0,r_tx0_detrx_req};
                17'h10073:  ubus_rdata <= {31'd0,r_tx0_detrx_result};
                17'h10074:  ubus_rdata <= {31'd0,r_tx0_disable};
                17'h10075:  ubus_rdata <= {26'd0,r_tx0_eq_main};
                17'h10076:  ubus_rdata <= {26'd0,r_tx0_eq_post};
                17'h10077:  ubus_rdata <= {26'd0,r_tx0_eq_pre};
                17'h10078:  ubus_rdata <= {31'd0,r_tx0_flyover_data_m};
                17'h10079:  ubus_rdata <= {31'd0,r_tx0_flyover_data_p};
                17'h1007A:  ubus_rdata <= {31'd0,r_tx0_lpd};
                17'h1007B:  ubus_rdata <= {31'd0,r_tx0_master_mplla_state};
                17'h1007C:  ubus_rdata <= {31'd0,r_tx0_master_mpllb_state};
                17'h1007D:  ubus_rdata <= {31'd0,r_tx0_mpll_en};
                17'h1007E:  ubus_rdata <= {31'd0,r_tx0_mpllb_sel};
                17'h1007F:  ubus_rdata <= {30'd0,r_tx0_pstate};                
                17'h10080:  ubus_rdata <= {29'd0,r_tx0_rate};
                17'h10081:  ubus_rdata <= {31'd0,r_tx0_req};
                17'h10082:  ubus_rdata <= {31'd0,r_tx0_reset_i};
                                  
                17'h10083:  ubus_rdata <= {31'd0,r_tx1_ack};
                17'h10084:  ubus_rdata <= {31'd0,r_tx1_beacon_en};
                17'h10085:  ubus_rdata <= {31'd0,r_tx1_clk_rdy};
                17'h10086:  ubus_rdata <= {31'd0,r_tx1_data_en};
                17'h10087:  ubus_rdata <= {31'd0,r_tx1_detrx_req};
                17'h10088:  ubus_rdata <= {31'd0,r_tx1_detrx_result};
                17'h10089:  ubus_rdata <= {31'd0,r_tx1_disable};
                17'h1008A:  ubus_rdata <= {26'd0,r_tx1_eq_main};
                17'h1008B:  ubus_rdata <= {26'd0,r_tx1_eq_post};
                17'h1008C:  ubus_rdata <= {26'd0,r_tx1_eq_pre};
                17'h1008D:  ubus_rdata <= {31'd0,r_tx1_flyover_data_m};
                17'h1008E:  ubus_rdata <= {31'd0,r_tx1_flyover_data_p};
                17'h1008F:  ubus_rdata <= {31'd0,r_tx1_lpd};                
                17'h10090:  ubus_rdata <= {31'd0,r_tx1_master_mplla_state};
                17'h10091:  ubus_rdata <= {31'd0,r_tx1_master_mpllb_state};
                17'h10092:  ubus_rdata <= {31'd0,r_tx1_mpll_en};
                17'h10093:  ubus_rdata <= {31'd0,r_tx1_mpllb_sel};
                17'h10094:  ubus_rdata <= {30'd0,r_tx1_pstate};
                17'h10095:  ubus_rdata <= {29'd0,r_tx1_rate};
                17'h10096:  ubus_rdata <= {31'd0,r_tx1_req};
                17'h10097:  ubus_rdata <= {31'd0,r_tx1_reset_i};
                            
                17'h10098:  ubus_rdata <= {31'd0,r_l0_pcs_native_en};
                17'h10099:  ubus_rdata <= {31'd0,r_l0_pcs_standard_en};
                17'h1009A:  ubus_rdata <= {31'd0,r_l0_gate_stclk_en};
                17'h1009B:  ubus_rdata <= {31'd0,r_l0_gate_drclk_en};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l0_tx_outclk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l0_tx_out2clk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l0_rx_outclk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l0_rx_out2clk};                
            //  17'h10000:  ubus_rdata <= {31'd0,r_l0_tx_usrclk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l0_tx_usr2clk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l0_rx_usrclk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l0_rx_usr2clk};
                17'h1009C:  ubus_rdata <= {28'd0,r_l0_tx_outclk_sel};
                17'h1009D:  ubus_rdata <= {28'd0,r_l0_tx_out2clk_sel};
                17'h1009E:  ubus_rdata <= {30'd0,r_l0_tx_xclk_sel};
                17'h1009F:  ubus_rdata <= {30'd0,r_l0_tx_iclk_sel};
                17'h100A0:  ubus_rdata <= {30'd0,r_l0_rx_outclk_sel};
                17'h100A1:  ubus_rdata <= {30'd0,r_l0_rx_out2clk_sel};
                17'h100A2:  ubus_rdata <= {30'd0,r_l0_rx_xclk_sel};
                17'h100A3:  ubus_rdata <= {30'd0,r_l0_rx_rmatclk_sel};
                17'h100A4:  ubus_rdata <= {29'd0,r_l0_rx_oclk_sel};
                17'h100A5:  ubus_rdata <= {31'd0,r_l0_tx_pcs_rst_n};
                17'h100A6:  ubus_rdata <= {31'd0,r_l0_tx_pcsfifo_rst_n};
            //  17'h100A7:  ubus_rdata <= {0'd0,r_l0_tx_usrdata_in};
                17'h100A8:  ubus_rdata <= {29'd0,r_l0_tx_usrdata_width};
                17'h100A9:  ubus_rdata <= {24'd0,r_l0_tx_usrdatak_in};
                17'h100AA:  ubus_rdata <= {24'd0,r_l0_tx_forcedisp_in};
                17'h100AB:  ubus_rdata <= {24'd0,r_l0_tx_forcedispval_in};
                17'h100AC:  ubus_rdata <= {31'd0,r_l0_tx_byteserial_en};
                17'h100AD:  ubus_rdata <= {31'd0,r_l0_tx_byteserial_mode};
                17'h100AE:  ubus_rdata <= {31'd0,r_l0_tx_pcsfifo_en};
                17'h100AF:  ubus_rdata <= {31'd0,r_l0_tx_enc_en};                
                17'h100B0:  ubus_rdata <= {31'd0,r_l0_tx_bitslip_en};
                17'h100B1:  ubus_rdata <= {27'd0,r_l0_tx_bitslip_width};
                17'h100B2:  ubus_rdata <= {31'd0,r_l0_tx_prbs_en};
                17'h100B3:  ubus_rdata <= {29'd0,r_l0_tx_prbs_sel};
                17'h100B4:  ubus_rdata <= {31'd0,r_l0_tx_prbs_force_err};
                17'h100B5:  ubus_rdata <= {30'd0,r_l0_tx_pmadata_width};
                17'h100B6:  ubus_rdata <= {31'd0,r_l0_tx_pcsfifo_err};
                                     
                17'h100B7:  ubus_rdata <= {31'd0,r_l1_pcs_native_en};
                17'h100B8:  ubus_rdata <= {31'd0,r_l1_pcs_standard_en};
                17'h100B9:  ubus_rdata <= {31'd0,r_l1_gate_stclk_en};
                17'h100BA:  ubus_rdata <= {31'd0,r_l1_gate_drclk_en};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l1_tx_outclk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l1_tx_out2clk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l1_rx_outclk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l1_rx_out2clk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l1_tx_usrclk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l1_tx_usr2clk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l1_rx_usrclk};
            //  17'h10000:  ubus_rdata <= {31'd0,r_l1_rx_usr2clk};
                17'h100BB:  ubus_rdata <= {28'd0,r_l1_tx_outclk_sel};
                17'h100BC:  ubus_rdata <= {28'd0,r_l1_tx_out2clk_sel};
                17'h100BD:  ubus_rdata <= {30'd0,r_l1_tx_xclk_sel};
                17'h100BE:  ubus_rdata <= {30'd0,r_l1_tx_iclk_sel};
                17'h100BF:  ubus_rdata <= {30'd0,r_l1_rx_outclk_sel};                
                17'h100C0:  ubus_rdata <= {30'd0,r_l1_rx_out2clk_sel};
                17'h100C1:  ubus_rdata <= {30'd0,r_l1_rx_xclk_sel};
                17'h100C2:  ubus_rdata <= {30'd0,r_l1_rx_rmatclk_sel};
                17'h100C3:  ubus_rdata <= {29'd0,r_l1_rx_oclk_sel};
                17'h100C4:  ubus_rdata <= {31'd0,r_l1_tx_pcs_rst_n};
                17'h100C5:  ubus_rdata <= {31'd0,r_l1_tx_pcsfifo_rst_n};
            //  17'h100C6:  ubus_rdata <= {0'd0,r_l1_tx_usrdata_in};
                17'h100C7:  ubus_rdata <= {29'd0,r_l1_tx_usrdata_width};
                17'h100C8:  ubus_rdata <= {24'd0,r_l1_tx_usrdatak_in};
                17'h100C9:  ubus_rdata <= {24'd0,r_l1_tx_forcedisp_in};
                17'h100CA:  ubus_rdata <= {24'd0,r_l1_tx_forcedispval_in};
                17'h100CB:  ubus_rdata <= {31'd0,r_l1_tx_byteserial_en};
                17'h100CC:  ubus_rdata <= {31'd0,r_l1_tx_byteserial_mode};
                17'h100CD:  ubus_rdata <= {31'd0,r_l1_tx_pcsfifo_en};
                17'h100CE:  ubus_rdata <= {31'd0,r_l1_tx_enc_en};
                17'h100CF:  ubus_rdata <= {31'd0,r_l1_tx_bitslip_en};
                17'h100D0:  ubus_rdata <= {27'd0,r_l1_tx_bitslip_width};
                17'h100D1:  ubus_rdata <= {31'd0,r_l1_tx_prbs_en};
                17'h100D2:  ubus_rdata <= {29'd0,r_l1_tx_prbs_sel};
                17'h100D3:  ubus_rdata <= {31'd0,r_l1_tx_prbs_force_err};
                17'h100D4:  ubus_rdata <= {30'd0,r_l1_tx_pmadata_width};
                17'h100D5:  ubus_rdata <= {31'd0,r_l1_tx_pcsfifo_err};
                                  
                17'h100D6:  ubus_rdata <= {31'd0,r_l0_rx_pcs_rst_n};
                17'h100D7:  ubus_rdata <= {31'd0,r_l0_rx_pcsfifo_rst_n};                
                17'h100D8:  ubus_rdata <= {31'd0,r_l0_rx_rmatchfifo_rst_n};
                17'h100D9:  ubus_rdata <= {30'd0,r_l0_rx_pmadata_width};
                17'h100DA:  ubus_rdata <= {31'd0,r_l0_rx_prbs_en};
                17'h100DB:  ubus_rdata <= {29'd0,r_l0_rx_prbs_sel};
                17'h100DC:  ubus_rdata <= {31'd0,r_l0_rx_wordalign_en};
                17'h100DD:  ubus_rdata <= {31'd0,r_l0_rx_pcommaalign_en};
                17'h100DE:  ubus_rdata <= {31'd0,r_l0_rx_mcommaalign_en};
                17'h100DF:  ubus_rdata <= {31'd0,r_l0_rx_slide};
                17'h100E0:  ubus_rdata <= {22'd0,r_l0_rx_aligncomma_mask};
                17'h100E1:  ubus_rdata <= {31'd0,r_l0_rx_aligncomma_double};
                17'h100E2:  ubus_rdata <= {22'd0,r_l0_rx_alignmcomma_value};
                17'h100E3:  ubus_rdata <= {31'd0,r_l0_rx_alignmcomma_det};
                17'h100E4:  ubus_rdata <= {22'd0,r_l0_rx_alignpcomma_value};
                17'h100E5:  ubus_rdata <= {31'd0,r_l0_rx_alignpcomma_det};
                17'h100E6:  ubus_rdata <= {31'd0,r_l0_rx_show_realigncomma};
                17'h100E7:  ubus_rdata <= {30'd0,r_l0_rx_slidemode};
                17'h100E8:  ubus_rdata <= {31'd0,r_l0_rx_ratmatfifo_en};
                17'h100E9:  ubus_rdata <= {22'd0,r_l0_rx_rmfifo_skip_1byte};
                17'h100EA:  ubus_rdata <= {22'd0,r_l0_rx_rmfifo_skip_2byte};
                17'h100EB:  ubus_rdata <= {22'd0,r_l0_rx_rmfifo_skip_3byte};
                17'h100EC:  ubus_rdata <= {22'd0,r_l0_rx_rmfifo_skip_4byte};
                17'h100ED:  ubus_rdata <= {31'd0,r_l0_rx_rmfifo_skip4byte_en};
                17'h100EE:  ubus_rdata <= {31'd0,r_l0_rx_rmfifo_skip2byte_en};
                17'h100EF:  ubus_rdata <= {28'd0,r_l0_rx_rmfifo_min_ipg_cnt};                
                17'h100F0:  ubus_rdata <= {27'd0,r_l0_rx_rmfifo_high_mark};
                17'h100F1:  ubus_rdata <= {27'd0,r_l0_rx_rmfifo_low_mark};
                17'h100F2:  ubus_rdata <= {31'd0,r_l0_rx_dec_en};
                17'h100F3:  ubus_rdata <= {31'd0,r_l0_rx_pcsfifo_en};
                17'h100F4:  ubus_rdata <= {31'd0,r_l0_rx_bytedeserial_en};
                17'h100F5:  ubus_rdata <= {31'd0,r_l0_rx_bytedeserial_mode};
                17'h100F6:  ubus_rdata <= {29'd0,r_l0_rx_usrdata_width};
                17'h100F7:  ubus_rdata <= {31'd0,r_l0_rx_prbs_err_clr};
                17'h100F8:  ubus_rdata <= {24'd0,r_l0_rx_prbs_err_cnt};
                17'h100F9:  ubus_rdata <= {31'd0,r_l0_rx_wordaligned};
                17'h100FA:  ubus_rdata <= {31'd0,r_l0_rx_wordrealign};
                17'h100FB:  ubus_rdata <= {30'd0,r_l0_rx_commadet};
                17'h100FC:  ubus_rdata <= {28'd0,r_l0_rx_align_sel};
                17'h100FD:  ubus_rdata <= {31'd0,r_l0_rx_rmfifo_re_out};
                17'h100FE:  ubus_rdata <= {31'd0,r_l0_rx_rmfifo_we_out};
                17'h100FF:  ubus_rdata <= {31'd0,r_l0_rx_rmfifo_overflow};
                17'h10100:  ubus_rdata <= {31'd0,r_l0_rx_rmfifo_underflow};
                17'h10101:  ubus_rdata <= {31'd0,r_l0_rx_pcsfifo_err};
            //  17'h10102:  ubus_rdata <= {0'd0,r_l0_rx_usrdata_out};
                17'h10103:  ubus_rdata <= {24'd0,r_l0_rx_usrdatak_out};
                17'h10104:  ubus_rdata <= {24'd0,r_l0_rx_usrdisperr_out};
                17'h10105:  ubus_rdata <= {24'd0,r_l0_rx_usrcodeerr_out};
                17'h10106:  ubus_rdata <= {31'd0,r_l0_loopback_en};
                17'h10107:  ubus_rdata <= {30'd0,r_l0_loopback_mode};  
                              
                17'h10108:  ubus_rdata <= {31'd0,r_l1_rx_pcs_rst_n};
                17'h10109:  ubus_rdata <= {31'd0,r_l1_rx_pcsfifo_rst_n};
                17'h1010A:  ubus_rdata <= {31'd0,r_l1_rx_rmatchfifo_rst_n};
                17'h1010B:  ubus_rdata <= {30'd0,r_l1_rx_pmadata_width};
                17'h1010C:  ubus_rdata <= {31'd0,r_l1_rx_prbs_en};
                17'h1010D:  ubus_rdata <= {29'd0,r_l1_rx_prbs_sel};
                17'h1010E:  ubus_rdata <= {31'd0,r_l1_rx_wordalign_en};
                17'h1010F:  ubus_rdata <= {31'd0,r_l1_rx_pcommaalign_en};
                17'h10110:  ubus_rdata <= {31'd0,r_l1_rx_mcommaalign_en};
                17'h10111:  ubus_rdata <= {31'd0,r_l1_rx_slide};
                17'h10112:  ubus_rdata <= {22'd0,r_l1_rx_aligncomma_mask};
                17'h10113:  ubus_rdata <= {31'd0,r_l1_rx_aligncomma_double};
                17'h10114:  ubus_rdata <= {22'd0,r_l1_rx_alignmcomma_value};
                17'h10115:  ubus_rdata <= {31'd0,r_l1_rx_alignmcomma_det};
                17'h10116:  ubus_rdata <= {22'd0,r_l1_rx_alignpcomma_value};
                17'h10117:  ubus_rdata <= {31'd0,r_l1_rx_alignpcomma_det};
                17'h10118:  ubus_rdata <= {31'd0,r_l1_rx_show_realigncomma};
                17'h10119:  ubus_rdata <= {30'd0,r_l1_rx_slidemode};
                17'h1011A:  ubus_rdata <= {31'd0,r_l1_rx_ratmatfifo_en};
                17'h1011B:  ubus_rdata <= {22'd0,r_l1_rx_rmfifo_skip_1byte};
                17'h1011C:  ubus_rdata <= {22'd0,r_l1_rx_rmfifo_skip_2byte};
                17'h1011D:  ubus_rdata <= {22'd0,r_l1_rx_rmfifo_skip_3byte};
                17'h1011E:  ubus_rdata <= {22'd0,r_l1_rx_rmfifo_skip_4byte};
                17'h1011F:  ubus_rdata <= {31'd0,r_l1_rx_rmfifo_skip4byte_en};                
                17'h10120:  ubus_rdata <= {31'd0,r_l1_rx_rmfifo_skip2byte_en};
                17'h10121:  ubus_rdata <= {28'd0,r_l1_rx_rmfifo_min_ipg_cnt};
                17'h10122:  ubus_rdata <= {27'd0,r_l1_rx_rmfifo_high_mark};
                17'h10123:  ubus_rdata <= {27'd0,r_l1_rx_rmfifo_low_mark};
                17'h10124:  ubus_rdata <= {31'd0,r_l1_rx_dec_en};
                17'h10125:  ubus_rdata <= {31'd0,r_l1_rx_pcsfifo_en};
                17'h10126:  ubus_rdata <= {31'd0,r_l1_rx_bytedeserial_en};
                17'h10127:  ubus_rdata <= {31'd0,r_l1_rx_bytedeserial_mode};
                17'h10128:  ubus_rdata <= {29'd0,r_l1_rx_usrdata_width};
                17'h10129:  ubus_rdata <= {31'd0,r_l1_rx_prbs_err_clr};
                17'h1012A:  ubus_rdata <= {24'd0,r_l1_rx_prbs_err_cnt};
                17'h1012B:  ubus_rdata <= {31'd0,r_l1_rx_wordaligned};
                17'h1012C:  ubus_rdata <= {31'd0,r_l1_rx_wordrealign};
                17'h1012D:  ubus_rdata <= {30'd0,r_l1_rx_commadet};
                17'h1012E:  ubus_rdata <= {28'd0,r_l1_rx_align_sel};
                17'h1012F:  ubus_rdata <= {31'd0,r_l1_rx_rmfifo_re_out};
                17'h10130:  ubus_rdata <= {31'd0,r_l1_rx_rmfifo_we_out};
                17'h10131:  ubus_rdata <= {31'd0,r_l1_rx_rmfifo_overflow};
                17'h10132:  ubus_rdata <= {31'd0,r_l1_rx_rmfifo_underflow};
                17'h10133:  ubus_rdata <= {31'd0,r_l1_rx_pcsfifo_err};
            //  17'h10134:  ubus_rdata <= {0'd0,r_l1_rx_usrdata_out};
                17'h10135:  ubus_rdata <= {24'd0,r_l1_rx_usrdatak_out};
                17'h10136:  ubus_rdata <= {24'd0,r_l1_rx_usrdisperr_out};
                17'h10137:  ubus_rdata <= {24'd0,r_l1_rx_usrcodeerr_out};                
                17'h10138:  ubus_rdata <= {31'd0,r_l1_loopback_en};
                17'h10139:  ubus_rdata <= {30'd0,r_l1_loopback_mode};
                                  
                17'h1013A:  ubus_rdata <= {28'd0,r_l0_debug_sel};
            //  17'h1013B:  ubus_rdata <= {0'd0,r_l0_data_debug};
                17'h1013C:  ubus_rdata <= {28'd0,r_l1_debug_sel};
            //  17'h1013D:  ubus_rdata <= {0'd0,r_l1_data_debug};
                17'h1013E:  ubus_rdata <= {30'd0,r_clk0_sel};
                17'h1013F:  ubus_rdata <= {30'd0,r_clk1_sel};
                17'h10140:  ubus_rdata <= {30'd0,r_clk2_sel};
            //  17'h10141:  ubus_rdata <= {31'd0,r_clk0_out};
            //  17'h10142:  ubus_rdata <= {31'd0,r_clk1_out};
            //  17'h10143:  ubus_rdata <= {31'd0,r_clk2_out};
                17'h11000:  ubus_rdata <= {30'd0,r_data_locked};
                default:    ubus_rdata <= 32'd0;
                
                endcase
                
    else if(cri_state==cri_read&&cr_para_ack_o)
        begin
        ubus_rdata<={16'd0,cr_para_rd_data_o};
        end

end


//======================================================================
// 串口写数据

always@(posedge clk)
begin

    if( ubus_wen==1'b1 && ubus_waddr[16]==1'b1)
   
                begin
                case(ubus_waddr[8:0])
                
                    9'h000:  we_mplla_bandwidth             <= ubus_wdata   ;
                    9'h002:  we_mplla_multiplier            <= ubus_wdata   ;
                    9'h003:  we_mplla_recal_bank_sel        <= ubus_wdata   ;
                    9'h008:  we_pg_reset                    <= ubus_wdata   ;
                    9'h00A:  we_phy_reset_i                 <= ubus_wdata   ;
                    9'h00B:  we_ref_clk_en_i                <= ubus_wdata   ;
                    9'h00F:  we_scan_mode                   <= ubus_wdata   ;
                    9'h010:  we_scan_set_rst                <= ubus_wdata   ;
                    9'h011:  we_scan_shift                  <= ubus_wdata   ;
                    9'h012:  we_scan_shift_cg               <= ubus_wdata   ;
                    9'h013:  we_scan_sel                    <= ubus_wdata   ;
                    9'h014:  we_scan_phy_clk                <= ubus_wdata   ;
                 // 9'h015:  we_scan_data_in                <= ubus_wdata   ;
                    9'h017:  we_sram_bypass_i               <= ubus_wdata   ;
                 // 9'h018:  we_sram_ext_ld_done_i          <= ubus_wdata   ;
                    9'h01A:  we_sup_misc                    <= ubus_wdata   ;
                    9'h01B:  we_test_burnin                 <= ubus_wdata   ;
                    9'h01C:  we_test_flyover_en             <= ubus_wdata   ;
                    9'h01D:  we_test_powerdown_i            <= ubus_wdata   ;
                    9'h01E:  we_test_stop_clk_en            <= ubus_wdata   ;
                    9'h01F:  we_sram_sel_i                  <= ubus_wdata   ;
                             
                    9'h023:  we_rx0_adapt_in_prog           <= ubus_wdata   ;
                    9'h024:  we_rx0_adapt_mode              <= ubus_wdata   ;
                    9'h025:  we_rx0_adapt_req               <= ubus_wdata   ;
                    9'h026:  we_rx0_adapt_sel               <= ubus_wdata   ;
                    9'h027:  we_rx0_cdr_ppm_max             <= ubus_wdata   ;
                    9'h028:  we_rx0_cdr_ssc_en              <= ubus_wdata   ;
                    9'h029:  we_rx0_cdr_vco_freqband        <= ubus_wdata   ;
                    9'h02A:  we_rx0_cdr_vco_step_ctrl       <= ubus_wdata   ;
                    9'h02B:  we_rx0_data_en                 <= ubus_wdata   ;
                    9'h02C:  we_rx0_delta_iq                <= ubus_wdata   ;
                    9'h02D:  we_rx0_dfe_bypass              <= ubus_wdata   ;
                    9'h02E:  we_rx0_disable                 <= ubus_wdata   ;
                    9'h02F:  we_rx0_eq_ctle_boost           <= ubus_wdata   ;
                    9'h030:  we_rx0_eq_dfe_tap              <= ubus_wdata   ;
                    9'h031:  we_rx0_eq_vga1_gain            <= ubus_wdata   ;
                    9'h032:  we_rx0_eq_vga2_gain            <= ubus_wdata   ;
                    9'h035:  we_rx0_invert                  <= ubus_wdata   ;
                    9'h037:  we_rx0_lpd                     <= ubus_wdata   ;
                    9'h038:  we_rx0_misc                    <= ubus_wdata   ;
                    9'h03B:  we_rx0_pstate                  <= ubus_wdata   ;
                    9'h03C:  we_rx0_rate                    <= ubus_wdata   ;
                    9'h03D:  we_rx0_ref_ld_val              <= ubus_wdata   ;
                    9'h03E:  we_rx0_req                     <= ubus_wdata   ;
                    9'h03F:  we_rx0_reset_i                 <= ubus_wdata   ;
                    9'h040:  we_rx0_term_acdc               <= ubus_wdata   ;
                    9'h041:  we_rx0_term_en                 <= ubus_wdata   ;
                    9'h046:  we_rx0_vco_ld_val              <= ubus_wdata   ; 
                                     
                    9'h04A:  we_rx1_adapt_in_prog           <= ubus_wdata   ;
                    9'h04B:  we_rx1_adapt_mode              <= ubus_wdata   ;
                    9'h04C:  we_rx1_adapt_req               <= ubus_wdata   ;
                    9'h04D:  we_rx1_adapt_sel               <= ubus_wdata   ;
                    9'h04E:  we_rx1_cdr_ppm_max             <= ubus_wdata   ;
                    9'h04F:  we_rx1_cdr_ssc_en              <= ubus_wdata   ;
                    9'h050:  we_rx1_cdr_vco_freqband        <= ubus_wdata   ;
                    9'h051:  we_rx1_cdr_vco_step_ctrl       <= ubus_wdata   ;
                    9'h052:  we_rx1_data_en                 <= ubus_wdata   ;
                    9'h053:  we_rx1_delta_iq                <= ubus_wdata   ;
                    9'h054:  we_rx1_dfe_bypass              <= ubus_wdata   ;
                    9'h055:  we_rx1_disable                 <= ubus_wdata   ;
                    9'h056:  we_rx1_eq_ctle_boost           <= ubus_wdata   ;
                    9'h057:  we_rx1_eq_dfe_tap              <= ubus_wdata   ;
                    9'h058:  we_rx1_eq_vga1_gain            <= ubus_wdata   ;
                    9'h059:  we_rx1_eq_vga2_gain            <= ubus_wdata   ;
                    9'h05C:  we_rx1_invert                  <= ubus_wdata   ;
                    9'h05E:  we_rx1_lpd                     <= ubus_wdata   ;
                    9'h05F:  we_rx1_misc                    <= ubus_wdata   ;
                    9'h062:  we_rx1_pstate                  <= ubus_wdata   ;
                    9'h063:  we_rx1_rate                    <= ubus_wdata   ;
                    9'h064:  we_rx1_ref_ld_val              <= ubus_wdata   ;
                    9'h065:  we_rx1_req                     <= ubus_wdata   ;
                    9'h066:  we_rx1_reset_i                 <= ubus_wdata   ;
                    9'h067:  we_rx1_term_acdc               <= ubus_wdata   ;
                    9'h068:  we_rx1_term_en                 <= ubus_wdata   ;
                    9'h06D:  we_rx1_vco_ld_val              <= ubus_wdata   ;
                              
                    9'h06F:  we_tx0_beacon_en               <= ubus_wdata   ;
                    9'h070:  we_tx0_clk_rdy                 <= ubus_wdata   ;
                    9'h071:  we_tx0_data_en                 <= ubus_wdata   ;
                    9'h072:  we_tx0_detrx_req               <= ubus_wdata   ;
                    9'h074:  we_tx0_disable                 <= ubus_wdata   ;
                    9'h075:  we_tx0_eq_main                 <= ubus_wdata   ;
                    9'h076:  we_tx0_eq_post                 <= ubus_wdata   ;
                    9'h077:  we_tx0_eq_pre                  <= ubus_wdata   ;
                    9'h078:  we_tx0_flyover_data_m          <= ubus_wdata   ;
                    9'h079:  we_tx0_flyover_data_p          <= ubus_wdata   ;
                    9'h07A:  we_tx0_lpd                     <= ubus_wdata   ;
                    9'h07B:  we_tx0_master_mplla_state      <= ubus_wdata   ;
                    9'h07C:  we_tx0_master_mpllb_state      <= ubus_wdata   ;
                    9'h07D:  we_tx0_mpll_en                 <= ubus_wdata   ;
                    9'h07E:  we_tx0_mpllb_sel               <= ubus_wdata   ;
                    9'h07F:  we_tx0_pstate                  <= ubus_wdata   ;
                    9'h080:  we_tx0_rate                    <= ubus_wdata   ;
                    9'h081:  we_tx0_req                     <= ubus_wdata   ;
                    9'h082:  we_tx0_reset_i                 <= ubus_wdata   ;
                                 
                    9'h084:  we_tx1_beacon_en               <= ubus_wdata   ;
                    9'h085:  we_tx1_clk_rdy                 <= ubus_wdata   ;
                    9'h086:  we_tx1_data_en                 <= ubus_wdata   ;
                    9'h087:  we_tx1_detrx_req               <= ubus_wdata   ;
                    9'h089:  we_tx1_disable                 <= ubus_wdata   ;
                    9'h08A:  we_tx1_eq_main                 <= ubus_wdata   ;
                    9'h08B:  we_tx1_eq_post                 <= ubus_wdata   ;
                    9'h08C:  we_tx1_eq_pre                  <= ubus_wdata   ;
                    9'h08D:  we_tx1_flyover_data_m          <= ubus_wdata   ;
                    9'h08E:  we_tx1_flyover_data_p          <= ubus_wdata   ;
                    9'h08F:  we_tx1_lpd                     <= ubus_wdata   ;
                    9'h090:  we_tx1_master_mplla_state      <= ubus_wdata   ;
                    9'h091:  we_tx1_master_mpllb_state      <= ubus_wdata   ;
                    9'h092:  we_tx1_mpll_en                 <= ubus_wdata   ;
                    9'h093:  we_tx1_mpllb_sel               <= ubus_wdata   ;
                    9'h094:  we_tx1_pstate                  <= ubus_wdata   ;
                    9'h095:  we_tx1_rate                    <= ubus_wdata   ;
                    9'h096:  we_tx1_req                     <= ubus_wdata   ;
                    9'h097:  we_tx1_reset_i                 <= ubus_wdata   ;
                                
                    9'h098:  we_l0_pcs_native_en            <= ubus_wdata   ;
                    9'h099:  we_l0_pcs_standard_en          <= ubus_wdata   ;
                    9'h09A:  we_l0_gate_stclk_en            <= ubus_wdata   ;
                    9'h09B:  we_l0_gate_drclk_en            <= ubus_wdata   ;
                 // 9'h001:  we_l0_tx_usrclk                <= ubus_wdata   ;
                 // 9'h001:  we_l0_tx_usr2clk               <= ubus_wdata   ;
                 // 9'h001:  we_l0_rx_usrclk                <= ubus_wdata   ;
                 // 9'h001:  we_l0_rx_usr2clk               <= ubus_wdata   ;
                    9'h09C:  we_l0_tx_outclk_sel            <= ubus_wdata   ;
                    9'h09D:  we_l0_tx_out2clk_sel           <= ubus_wdata   ;
                    9'h09E:  we_l0_tx_xclk_sel              <= ubus_wdata   ;
                    9'h09F:  we_l0_tx_iclk_sel              <= ubus_wdata   ;
                    9'h0A0:  we_l0_rx_outclk_sel            <= ubus_wdata   ;
                    9'h0A1:  we_l0_rx_out2clk_sel           <= ubus_wdata   ;
                    9'h0A2:  we_l0_rx_xclk_sel              <= ubus_wdata   ;
                    9'h0A3:  we_l0_rx_rmatclk_sel           <= ubus_wdata   ;
                    9'h0A4:  we_l0_rx_oclk_sel              <= ubus_wdata   ;
                    9'h0A5:  we_l0_tx_pcs_rst_n             <= ubus_wdata   ;
                    9'h0A6:  we_l0_tx_pcsfifo_rst_n         <= ubus_wdata   ;
                 // 9'h0A7:  we_l0_tx_usrdata_in            <= ubus_wdata   ;
                    9'h0A8:  we_l0_tx_usrdata_width         <= ubus_wdata   ;
                    9'h0A9:  we_l0_tx_usrdatak_in           <= ubus_wdata   ;
                    9'h0AA:  we_l0_tx_forcedisp_in          <= ubus_wdata   ;
                    9'h0AB:  we_l0_tx_forcedispval_in       <= ubus_wdata   ;
                    9'h0AC:  we_l0_tx_byteserial_en         <= ubus_wdata   ;
                    9'h0AD:  we_l0_tx_byteserial_mode       <= ubus_wdata   ;
                    9'h0AE:  we_l0_tx_pcsfifo_en            <= ubus_wdata   ;
                    9'h0AF:  we_l0_tx_enc_en                <= ubus_wdata   ;
                    9'h0B0:  we_l0_tx_bitslip_en            <= ubus_wdata   ;
                    9'h0B1:  we_l0_tx_bitslip_width         <= ubus_wdata   ;
                    9'h0B2:  we_l0_tx_prbs_en               <= ubus_wdata   ;
                    9'h0B3:  we_l0_tx_prbs_sel              <= ubus_wdata   ;
                    9'h0B4:  we_l0_tx_prbs_force_err        <= ubus_wdata   ;
                    9'h0B5:  we_l0_tx_pmadata_width         <= ubus_wdata   ;
                                  
                    9'h0B7:  we_l1_pcs_native_en            <= ubus_wdata   ;
                    9'h0B8:  we_l1_pcs_standard_en          <= ubus_wdata   ;
                    9'h0B9:  we_l1_gate_stclk_en            <= ubus_wdata   ;
                    9'h0BA:  we_l1_gate_drclk_en            <= ubus_wdata   ;
                 // 9'h001:  we_l1_tx_usrclk                <= ubus_wdata   ;
                 // 9'h001:  we_l1_tx_usr2clk               <= ubus_wdata   ;
                 // 9'h001:  we_l1_rx_usrclk                <= ubus_wdata   ;
                 // 9'h001:  we_l1_rx_usr2clk               <= ubus_wdata   ;
                    9'h0BB:  we_l1_tx_outclk_sel            <= ubus_wdata   ;
                    9'h0BC:  we_l1_tx_out2clk_sel           <= ubus_wdata   ;
                    9'h0BD:  we_l1_tx_xclk_sel              <= ubus_wdata   ;
                    9'h0BE:  we_l1_tx_iclk_sel              <= ubus_wdata   ;
                    9'h0BF:  we_l1_rx_outclk_sel            <= ubus_wdata   ;
                    9'h0C0:  we_l1_rx_out2clk_sel           <= ubus_wdata   ;
                    9'h0C1:  we_l1_rx_xclk_sel              <= ubus_wdata   ;
                    9'h0C2:  we_l1_rx_rmatclk_sel           <= ubus_wdata   ;
                    9'h0C3:  we_l1_rx_oclk_sel              <= ubus_wdata   ;
                    9'h0C4:  we_l1_tx_pcs_rst_n             <= ubus_wdata   ;
                    9'h0C5:  we_l1_tx_pcsfifo_rst_n         <= ubus_wdata   ;
                 // 9'h0C6:  we_l1_tx_usrdata_in            <= ubus_wdata   ;
                    9'h0C7:  we_l1_tx_usrdata_width         <= ubus_wdata   ;
                    9'h0C8:  we_l1_tx_usrdatak_in           <= ubus_wdata   ;
                    9'h0C9:  we_l1_tx_forcedisp_in          <= ubus_wdata   ;
                    9'h0CA:  we_l1_tx_forcedispval_in       <= ubus_wdata   ;
                    9'h0CB:  we_l1_tx_byteserial_en         <= ubus_wdata   ;
                    9'h0CC:  we_l1_tx_byteserial_mode       <= ubus_wdata   ;
                    9'h0CD:  we_l1_tx_pcsfifo_en            <= ubus_wdata   ;
                    9'h0CE:  we_l1_tx_enc_en                <= ubus_wdata   ;
                    9'h0CF:  we_l1_tx_bitslip_en            <= ubus_wdata   ;
                    9'h0D0:  we_l1_tx_bitslip_width         <= ubus_wdata   ;
                    9'h0D1:  we_l1_tx_prbs_en               <= ubus_wdata   ;
                    9'h0D2:  we_l1_tx_prbs_sel              <= ubus_wdata   ;
                    9'h0D3:  we_l1_tx_prbs_force_err        <= ubus_wdata   ;
                    9'h0D4:  we_l1_tx_pmadata_width         <= ubus_wdata   ;
                                    
                    9'h0D6:  we_l0_rx_pcs_rst_n             <= ubus_wdata   ;
                    9'h0D7:  we_l0_rx_pcsfifo_rst_n         <= ubus_wdata   ;
                    9'h0D8:  we_l0_rx_rmatchfifo_rst_n      <= ubus_wdata   ;
                    9'h0D9:  we_l0_rx_pmadata_width         <= ubus_wdata   ;
                    9'h0DA:  we_l0_rx_prbs_en               <= ubus_wdata   ;
                    9'h0DB:  we_l0_rx_prbs_sel              <= ubus_wdata   ;
                    9'h0DC:  we_l0_rx_wordalign_en          <= ubus_wdata   ;
                    9'h0DD:  we_l0_rx_pcommaalign_en        <= ubus_wdata   ;
                    9'h0DE:  we_l0_rx_mcommaalign_en        <= ubus_wdata   ;
                    9'h0DF:  we_l0_rx_slide                 <= ubus_wdata   ;
                    9'h0E0:  we_l0_rx_aligncomma_mask       <= ubus_wdata   ;
                    9'h0E1:  we_l0_rx_aligncomma_double     <= ubus_wdata   ;
                    9'h0E2:  we_l0_rx_alignmcomma_value     <= ubus_wdata   ;
                    9'h0E3:  we_l0_rx_alignmcomma_det       <= ubus_wdata   ;
                    9'h0E4:  we_l0_rx_alignpcomma_value     <= ubus_wdata   ;
                    9'h0E5:  we_l0_rx_alignpcomma_det       <= ubus_wdata   ;
                    9'h0E6:  we_l0_rx_show_realigncomma     <= ubus_wdata   ;
                    9'h0E7:  we_l0_rx_slidemode             <= ubus_wdata   ;
                    9'h0E8:  we_l0_rx_ratmatfifo_en         <= ubus_wdata   ;
                    9'h0E9:  we_l0_rx_rmfifo_skip_1byte     <= ubus_wdata   ;
                    9'h0EA:  we_l0_rx_rmfifo_skip_2byte     <= ubus_wdata   ;
                    9'h0EB:  we_l0_rx_rmfifo_skip_3byte     <= ubus_wdata   ;
                    9'h0EC:  we_l0_rx_rmfifo_skip_4byte     <= ubus_wdata   ;
                    9'h0ED:  we_l0_rx_rmfifo_skip4byte_en   <= ubus_wdata   ;
                    9'h0EE:  we_l0_rx_rmfifo_skip2byte_en   <= ubus_wdata   ;
                    9'h0EF:  we_l0_rx_rmfifo_min_ipg_cnt    <= ubus_wdata   ;
                    9'h0F0:  we_l0_rx_rmfifo_high_mark      <= ubus_wdata   ;
                    9'h0F1:  we_l0_rx_rmfifo_low_mark       <= ubus_wdata   ;
                    9'h0F2:  we_l0_rx_dec_en                <= ubus_wdata   ;
                    9'h0F3:  we_l0_rx_pcsfifo_en            <= ubus_wdata   ;
                    9'h0F4:  we_l0_rx_bytedeserial_en       <= ubus_wdata   ;
                    9'h0F5:  we_l0_rx_bytedeserial_mode     <= ubus_wdata   ;
                    9'h0F6:  we_l0_rx_usrdata_width         <= ubus_wdata   ;
                    9'h0F7:  we_l0_rx_prbs_err_clr          <= ubus_wdata   ;
                    9'h106:  we_l0_loopback_en              <= ubus_wdata   ;
                    9'h107:  we_l0_loopback_mode            <= ubus_wdata   ;
                                   
                    9'h108:  we_l1_rx_pcs_rst_n             <= ubus_wdata   ;
                    9'h109:  we_l1_rx_pcsfifo_rst_n         <= ubus_wdata   ;
                    9'h10A:  we_l1_rx_rmatchfifo_rst_n      <= ubus_wdata   ;
                    9'h10B:  we_l1_rx_pmadata_width         <= ubus_wdata   ;
                    9'h10C:  we_l1_rx_prbs_en               <= ubus_wdata   ;
                    9'h10D:  we_l1_rx_prbs_sel              <= ubus_wdata   ;
                    9'h10E:  we_l1_rx_wordalign_en          <= ubus_wdata   ;
                    9'h10F:  we_l1_rx_pcommaalign_en        <= ubus_wdata   ;
                    9'h110:  we_l1_rx_mcommaalign_en        <= ubus_wdata   ;
                    9'h111:  we_l1_rx_slide                 <= ubus_wdata   ;
                    9'h112:  we_l1_rx_aligncomma_mask       <= ubus_wdata   ;
                    9'h113:  we_l1_rx_aligncomma_double     <= ubus_wdata   ;
                    9'h114:  we_l1_rx_alignmcomma_value     <= ubus_wdata   ;
                    9'h115:  we_l1_rx_alignmcomma_det       <= ubus_wdata   ;
                    9'h116:  we_l1_rx_alignpcomma_value     <= ubus_wdata   ;
                    9'h117:  we_l1_rx_alignpcomma_det       <= ubus_wdata   ;
                    9'h118:  we_l1_rx_show_realigncomma     <= ubus_wdata   ;
                    9'h119:  we_l1_rx_slidemode             <= ubus_wdata   ;
                    9'h11A:  we_l1_rx_ratmatfifo_en         <= ubus_wdata   ;
                    9'h11B:  we_l1_rx_rmfifo_skip_1byte     <= ubus_wdata   ;
                    9'h11C:  we_l1_rx_rmfifo_skip_2byte     <= ubus_wdata   ;
                    9'h11D:  we_l1_rx_rmfifo_skip_3byte     <= ubus_wdata   ;
                    9'h11E:  we_l1_rx_rmfifo_skip_4byte     <= ubus_wdata   ;
                    9'h11F:  we_l1_rx_rmfifo_skip4byte_en   <= ubus_wdata   ;
                    9'h120:  we_l1_rx_rmfifo_skip2byte_en   <= ubus_wdata   ;
                    9'h121:  we_l1_rx_rmfifo_min_ipg_cnt    <= ubus_wdata   ;
                    9'h122:  we_l1_rx_rmfifo_high_mark      <= ubus_wdata   ;
                    9'h123:  we_l1_rx_rmfifo_low_mark       <= ubus_wdata   ;
                    9'h124:  we_l1_rx_dec_en                <= ubus_wdata   ;
                    9'h125:  we_l1_rx_pcsfifo_en            <= ubus_wdata   ;
                    9'h126:  we_l1_rx_bytedeserial_en       <= ubus_wdata   ;
                    9'h127:  we_l1_rx_bytedeserial_mode     <= ubus_wdata   ;
                    9'h128:  we_l1_rx_usrdata_width         <= ubus_wdata   ;
                    9'h129:  we_l1_rx_prbs_err_clr          <= ubus_wdata   ;
                    9'h138:  we_l1_loopback_en              <= ubus_wdata   ;
                    9'h139:  we_l1_loopback_mode            <= ubus_wdata   ;
                                     
                    9'h13A:  we_l0_debug_sel                <= ubus_wdata   ;
                    9'h13C:  we_l1_debug_sel                <= ubus_wdata   ;
                    9'h13E:  we_clk0_sel                    <= ubus_wdata   ;
                    9'h13F:  we_clk1_sel                    <= ubus_wdata   ;
                    9'h140:  we_clk2_sel                    <= ubus_wdata   ;
                    
                //  default: w_data_error                   <= ubus_wdata   ; 
                                                                                                     
                endcase
                
                end
                
end

//========================================================================
// 串口数据返回(根据实际数据返回时间,调节对应延时)
 
always@(posedge clk)
begin
	if(cri_state==cri_read&&cr_para_ack_o)
		begin
			ubus_bken <= 1;
//			ubus_rdata<={16'd0,cr_para_rd_data_o};
		end
	else
		begin
			ubus_bken <= ubus_ren;
//			ubus_rdata<=ubus_rdata;
		end
end


endmodule
