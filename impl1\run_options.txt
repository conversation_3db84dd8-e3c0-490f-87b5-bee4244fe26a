#--  Synopsys, Inc.
#--  Version R-2021.03L-SP1-1
#--  Project file D:\Project\INS350_5J_JZ _copy\impl1\run_options.txt
#--  Written on Mon Jun 23 15:40:49 2025


#project files
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/INS350_5J_JZ.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SignalProcessing.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/speed_select_Tx.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SquareWaveGenerator.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/UART_Control.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/uart_tx.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SignalGenerator.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Ctrl_Data.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/DS18B20.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Integration.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Modulation.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Rs422Output.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/Src_al/Demodulation.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v"
add_file -verilog -vlog_std v2001 "D:/Project/INS350_5J_JZ _copy/IP_al/global_clock1/global_clock/global_clock.v"


#implementation: "impl1"
impl -add impl1 -type fpga

#
#implementation attributes

set_option -vlog_std v2001
set_option -project_relative_includes 1
set_option -include_path {D:/Project/INS350_5J_JZ _copy}

#device options
set_option -technology ECP5U
set_option -part LFE5U_25F
set_option -package BG256C
set_option -speed_grade -7
set_option -part_companion ""

#compilation/mapping options
set_option -top_module "INS350_5J_JZ"

# hdl_compiler_options
set_option -distributed_compile 0
set_option -hdl_strict_syntax 0

# mapper_without_write_options
set_option -frequency 200
set_option -srs_instrumentation 1

# mapper_options
set_option -write_verilog 0
set_option -write_structural_verilog 0
set_option -write_vhdl 0

# Lattice XP
set_option -maxfan 1000
set_option -disable_io_insertion 0
set_option -retiming 0
set_option -pipe 1
set_option -forcegsr false
set_option -fix_gated_and_generated_clocks 1
set_option -rw_check_on_ram 1
set_option -update_models_cp 0
set_option -syn_edif_array_rename 1
set_option -Write_declared_clocks_only 1
set_option -seqshift_no_replicate 0

# NFilter
set_option -no_sequential_opt 0

# sequential_optimization_options
set_option -symbolic_fsm_compiler 1

# Compiler Options
set_option -compiler_compatible 0
set_option -resource_sharing 1
set_option -multi_file_compilation_unit 1

# Compiler Options
set_option -auto_infer_blackbox 0

#automatic place and route (vendor) options
set_option -write_apr_constraint 1

#set result format/file last
project -result_file "./INS350_5J_JZ_impl1.edi"

#set log file 
set_option log_file "D:/Project/INS350_5J_JZ _copy/impl1/INS350_5J_JZ_impl1.srf" 
impl -active "impl1"
