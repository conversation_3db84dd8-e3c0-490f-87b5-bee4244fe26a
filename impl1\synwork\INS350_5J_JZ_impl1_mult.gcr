i CLK120.CLKOP_i
m 0 0
u 77 855
n ckid0_0 {t:signal_process.trans.count[19:0].C} Clock source is invalid for GCC
p {t:CLK120.PLLInst_0.CLKOP}{t:CLK120.CLKOP_inferred_clock.I[0]}{t:CLK120.CLKOP_inferred_clock.OUT[0]}{p:CLK120.CLKOP}{t:CLK120.CLKOP}{t:wendu.clk}{p:wendu.clk}{t:wendu.cnt[7:0].C}
e ckid0_1 {t:wendu.cnt[7:0].C} dffr
d ckid0_2 {t:CLK120.PLLInst_0.CLKOP} EHXPLLL Clock source is invalid for GCC
i CLK120.CLKOS_i
m 0 0
u 38 178
n ckid0_3 {t:signal_process.demodu.fifo.pdp_ram_0_0_1.CLKW} Clock source is invalid for GCC
p {t:CLK120.PLLInst_0.CLKOS}{t:CLK120.CLKOS_inferred_clock.I[0]}{t:CLK120.CLKOS_inferred_clock.OUT[0]}{p:CLK120.CLKOS}{t:CLK120.CLKOS}{t:signal_process.clk_AD}{p:signal_process.clk_AD}{t:signal_process.demodu.clk_in}{p:signal_process.demodu.clk_in}{t:signal_process.demodu.AD_validcnt[7:0].C}
e ckid0_4 {t:signal_process.demodu.AD_validcnt[7:0].C} sdffpatr
d ckid0_5 {t:CLK120.PLLInst_0.CLKOS} EHXPLLL Clock source is invalid for GCC
i wendu.clk_us_i
m 0 0
u 12 64
n ckid0_6 {t:wendu.cur_state[5].C} Derived clock on input (not legal for GCC)
p {t:wendu.clk_us.Q[0]}{t:wendu.clk_us_derived_clock.I[0]}{t:wendu.clk_us_derived_clock.OUT[0]}{t:wendu.cur_state[5].C}
e ckid0_6 {t:wendu.cur_state[5].C} dffre
d ckid0_7 {t:wendu.clk_us.Q[0]} dffre Derived clock on input (not legal for GCC)
i CLK120.CLKOS2
m 0 0
u 0 0
i CLK120.LOCK
m 0 0
u 0 0
l 0 0 0 0 0 0
r 0 0 0 0 0 0 0 0
