@N|Running in 64-bit mode
@N|Running in 64-bit mode
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":757:7:757:9|Synthesizing module VHI in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":761:7:761:9|Synthesizing module VLO in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":1696:7:1696:13|Synthesizing module EHXPLLL in library work.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\global_clock\global_clock.v":8:7:8:18|Synthesizing module global_clock in library work.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":17:7:17:13|Synthesizing module DS18B20 in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":85:12:85:17|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":189:20:189:27|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":248:20:248:27|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":291:15:291:18|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":307:21:307:29|Removing redundant assignment.
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":157:0:157:5|Register bit rd_flag is always 1.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\speed_select_Tx.v":17:7:17:21|Synthesizing module speed_select_Tx in library work.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":16:7:16:13|Synthesizing module uart_tx in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":53:12:53:16|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":54:13:54:19|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":55:17:55:27|Removing redundant assignment.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":15:7:15:15|Synthesizing module Ctrl_Data in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":64:53:64:63|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":69:57:69:69|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":85:14:85:21|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":117:24:117:33|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":129:22:129:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":141:22:141:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":153:22:153:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":165:22:165:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":177:22:177:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":189:22:189:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":201:22:201:31|Removing redundant assignment.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\UART_Control.v":16:7:16:18|Synthesizing module UART_Control in library work.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalGenerator.v":42:7:42:21|Synthesizing module SignalGenerator in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalGenerator.v":80:15:80:25|Removing redundant assignment.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":25:7:25:10|Synthesizing module AND2 in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":367:7:367:9|Synthesizing module INV in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":810:7:810:10|Synthesizing module XOR2 in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":710:7:710:14|Synthesizing module ROM16X1A in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":959:7:959:14|Synthesizing module PDPW16KD in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":119:7:119:13|Synthesizing module FD1P3DX in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":160:7:160:13|Synthesizing module FD1S3BX in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":168:7:168:13|Synthesizing module FD1S3DX in library work.
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":76:7:76:11|Synthesizing module CCU2C in library work.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":8:7:8:20|Synthesizing module Asys_fifo56X16 in library work.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":42:7:42:18|Synthesizing module Demodulation in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":101:17:101:27|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":118:16:118:25|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":130:46:130:61|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":159:59:159:72|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":171:13:171:20|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":175:12:175:19|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":176:16:176:27|Removing redundant assignment.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Integration.v":42:7:42:17|Synthesizing module Integration in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Integration.v":71:11:71:17|Removing redundant assignment.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":42:7:42:16|Synthesizing module Modulation in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":103:10:103:15|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":117:9:117:13|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":131:11:131:17|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":138:13:138:21|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":150:11:150:17|Removing redundant assignment.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":43:7:43:17|Synthesizing module Rs422Output in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":180:14:180:21|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":190:9:190:11|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":195:38:195:43|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":204:11:204:15|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":221:13:221:19|Removing redundant assignment.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SquareWaveGenerator.v":16:7:16:25|Synthesizing module SquareWaveGenerator in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SquareWaveGenerator.v":46:11:46:17|Removing redundant assignment.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalProcessing.v":41:7:41:22|Synthesizing module SignalProcessing in library work.
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\INS350_5J_JZ.v":17:7:17:18|Synthesizing module INS350_5J_JZ in library work.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\INS350_5J_JZ.v":22:9:22:18|Input RxTransmit is unused.
@N: CL201 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":93:0:93:5|Trying to extract state machine for register trans_state.
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":96:0:96:5|Register bit square[12] is always 0.
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":96:0:96:5|Register bit square[13] is always 0.
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":107:0:107:5|Register bit square_dy[13] is always 0.
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":107:0:107:5|Register bit square_dy[12] is always 0.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":52:12:52:16|Input rst_n is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Integration.v":48:14:48:18|Input rst_n is unused.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\UART_Control.v":33:11:33:13|Input RXD is unused.
@N: CL201 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":89:0:89:5|Trying to extract state machine for register tx_state.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":22:15:22:21|Input rd_done is unused.
@N: CL201 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":93:0:93:5|Trying to extract state machine for register cur_state.
@N|Running in 64-bit mode

