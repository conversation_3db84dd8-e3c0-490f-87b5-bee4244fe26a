SCUBA, Version Diamond (64-bit) 3.12.1.454
Tue Mar 25 10:10:00 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

    Issued command   : D:/Software/lscc/diamond/3.12/ispfpga/bin/nt64/scuba -w -bus_exp 7 -bb -arch sa5p00 -type bram -wp 10 -rp 0011 -data_width 8 -num_rows 1024 -rdata_width 8 -read_reg1 outreg -gsr DISABLED -reset_rel async -memformat bin -cascade -1 -n pmi_ram_dpEbnonesadr81010248101024p1396442f -pmi 
    Circuit name     : pmi_ram_dpEbnonesadr81010248101024p1396442f
    Module type      : RAM_DP
    Module Version   : 6.5
    Ports            : 
	Inputs       : Wr<PERSON>dd<PERSON>[9:0], <PERSON><PERSON><PERSON><PERSON>[9:0], <PERSON>[7:0], <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>n, <PERSON><PERSON>, <PERSON>r<PERSON><PERSON>, WrClockEn
	Outputs      : Q[7:0]
    I/O buffer       : not inserted
    EDIF output      : pmi_ram_dpEbnonesadr81010248101024p1396442f.edn
    Bus notation     : big endian
    Report output    : pmi_ram_dpEbnonesadr81010248101024p1396442f.srp
    Element Usage    :
         DP16KD : 1
    Estimated Resource Usage:
            EBR : 1
