lappend auto_path "D:/Software/lscc/diamond/3.12/data/script"
package require symbol_generation

set ::bali::Para(MODNAME) Modulation
set ::bali::Para(PROJECT) INS350_5J_JZ
set ::bali::Para(PACKAGE) {"D:/Software/lscc/diamond/3.12/cae_library/vhdl_packages/vdbs"}
set ::bali::Para(PRIMITIVEFILE) {"D:/Software/lscc/diamond/3.12/cae_library/synthesis/verilog/ecp5u.v"}
set ::bali::Para(FILELIST) {"D:/Project/INS350_5J_JZ/Src_al/INS350_5J_JZ.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/Ctrl_Data.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/Demodulation.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/DS18B20.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/Integration.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/Modulation.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/Rs422Output.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/SignalGenerator.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/SignalProcessing.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/speed_select_Tx.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/SquareWaveGenerator.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/UART_Control.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/Src_al/uart_tx.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v=work,Verilog_2001" }
set ::bali::Para(INCLUDEPATH) {}
puts "set parameters done"
::bali::GenerateSymbol
