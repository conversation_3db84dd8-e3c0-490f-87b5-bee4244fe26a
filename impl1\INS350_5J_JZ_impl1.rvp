#-- Lattice Semiconductor Corporation Ltd.
#-- Bali Reveal project file

#device options
[Device]
part = LFE5U-25F-7BG256C
family = ECP5U
device = LFE5U-25F
speed = 7
package = CABGA256
operation = Commercial

#design options
[Design]
title = INS350_5J_JZ
path = D:/Project/INS350_5J_JZ _copy/impl1
core_generate = D:/Project/INS350_5J_JZ _copy/impl1
search_path = D:/Project/INS350_5J_JZ _copy
top = INS350_5J_JZ
lpf = D:/Project/INS350_5J_JZ _copy/INS350_5J_JZ.lpf
synthesis = synplify

#strategy options
VHDL2008 = false

#HDLs options
[HDLs]
D:/Project/INS350_5J_JZ _copy/Src_al/INS350_5J_JZ.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SignalProcessing.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/speed_select_Tx.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SquareWaveGenerator.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/UART_Control.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/uart_tx.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/SignalGenerator.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Ctrl_Data.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/DS18B20.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Integration.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Modulation.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Src_al/Rs422Output.v = Verilog
D:/Project/INS350_5J_JZ _copy/Src_al/Demodulation.v = Verilog
D:/Project/INS350_5J_JZ _copy/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v = Verilog
D:/Project/INS350_5J_JZ _copy/IP_al/global_clock1/global_clock/global_clock.v = Verilog
[HDL_Defines]
SBP_SYNTHESIS=
[HDL_Param]
[Generated]
D:/Project/INS350_5J_JZ _copy/impl1/reveal_workspace/tmpreveal/ins350_5j_jz_la0_trig_gen.v=Verilog
D:/Project/INS350_5J_JZ _copy/impl1/reveal_workspace/tmpreveal/ins350_5j_jz_la0_gen.v=Verilog
D:/Project/INS350_5J_JZ _copy/impl1/reveal_workspace/tmpreveal/INS350_5J_JZ_rvl.v=Verilog,work
