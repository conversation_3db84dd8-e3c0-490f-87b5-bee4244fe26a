# N9引脚硬件修改指导

## 问题说明
原理图设计将clk_AD_t信号连接到N9引脚，但在LFE5U-25F CABGA256封装中，**N9引脚不是用户可分配的I/O引脚**，而是特殊功能引脚。

## N9引脚的实际功能
根据Lattice ECP5系列数据手册，N9引脚可能是：
- 电源引脚（VCC/GND）
- 配置引脚（JTAG相关）
- 其他专用功能引脚

## 硬件修改方案

### 方案1：使用最接近的可用引脚
推荐使用与N9物理位置最接近的可用I/O引脚：

| 原引脚 | 替代引脚 | 物理距离 | Bank | 备注 |
|--------|----------|----------|------|------|
| N9     | **N8**   | 最近     | 8    | 推荐选择 |
| N9     | **N7**   | 较近     | 8    | 备选方案 |
| N9     | **M8**   | 较近     | 8    | 备选方案 |
| N9     | **M9**   | 较近     | 8    | 备选方案 |

### 方案2：使用其他Bank的引脚
如果上述引脚已被占用，可以考虑：

| 引脚 | Bank | 位置 | 备注 |
|------|------|------|------|
| L1   | 6    | PL26A | 当前约束文件中使用 |
| M15  | 3    | PR26D | 可用于时钟输出 |
| T6   | 8    | PB4A  | 与N8同Bank |

## 具体修改步骤

### 步骤1：确认PCB修改可行性
1. 检查N9引脚周围的布线情况
2. 确认替代引脚（如N8）是否可达
3. 评估信号完整性影响

### 步骤2：PCB修改方法

#### 方法A：飞线连接（临时方案）
```
1. 断开N9引脚的连接
2. 用细导线将信号连接到N8引脚
3. 确保连接牢固，避免短路
```

#### 方法B：PCB重新布线（永久方案）
```
1. 修改PCB设计文件
2. 将clk_AD_t信号从N9改为N8
3. 重新制作PCB
```

### 步骤3：更新约束文件
使用以下约束（已在INS350_5J_JZ.lpf中更新）：
```lpf
LOCATE COMP "clk_AD_t" SITE "N8";   # 使用N8替代N9
IOBUF PORT "clk_AD_t" IO_TYPE=LVCMOS33;
```

### 步骤4：验证修改
1. 重新编译FPGA项目
2. 检查引脚分配报告
3. 进行硬件测试

## 引脚特性对比

### N8引脚特性（推荐替代）
- **位置**: CABGA256封装中的N8
- **Bank**: 8
- **Site**: PB15A
- **功能**: HOLDN/DI/BUSY/CSSPIN/CEN（可配置为普通I/O）
- **I/O标准**: 支持LVCMOS33
- **驱动能力**: 标准I/O驱动

### 信号完整性考虑
1. **时钟信号质量**: N8引脚可以输出60MHz时钟信号
2. **负载匹配**: 检查目标器件的输入阻抗
3. **EMI考虑**: 时钟信号可能需要适当的滤波

## 测试验证

### 功能测试
1. 使用示波器测量N8引脚的时钟输出
2. 验证频率为60MHz
3. 检查信号质量（上升时间、抖动等）

### 系统测试
1. 验证AD转换器是否正常工作
2. 检查数据采集的时序
3. 确认整个系统功能正常

## 注意事项

### 硬件修改风险
- 飞线连接可能影响信号完整性
- 修改过程中避免损坏其他元件
- 确保修改后的连接牢固可靠

### 软件配置
- 更新约束文件后需要重新编译
- 可能需要调整时序约束
- 验证PLL配置是否正确

## 总结
N9引脚不可用是FPGA封装的固有限制，不是设计错误。通过硬件修改使用N8等替代引脚可以解决问题。建议优先使用N8引脚，因为它与N9物理位置最接近，修改相对简单。
