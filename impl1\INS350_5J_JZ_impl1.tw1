
Loading design for application trce from file ins350_5j_jz_impl1_map.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application trce from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.
Setup and Hold Report

--------------------------------------------------------------------------------
<PERSON><PERSON><PERSON> TRACE Report - Setup, Version Diamond (64-bit) 3.12.1.454
Tue Jun 24 17:15:09 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Command line:    trce -v 1 -gt -mapchkpnt 0 -sethld -o INS350_5J_JZ_impl1.tw1 -gui -msgset D:/Project/INS350_5J_JZ _V2/promote.xml INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1_map.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,7
Report level:    verbose report, limited to 1 item per preference
--------------------------------------------------------------------------------

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0, defined by PAR)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7, defined by PAR)



================================================================================
Preference: FREQUENCY NET "clk120mhz" 120.000000 MHz ;
            4096 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 1.178ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.393ns  (86.4% logic, 13.6% route), 12 logic levels.

 Constraint Details:

      7.393ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
     -0.238ns DIN_SET requirement (totaling 8.571ns) by 1.178ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *m[18:53].CLK0 to *um[18:53].P35 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1   e 0.497 *um[18:53].P35 to *dd[0:53].MB35 signal_process/rs422/un3_p_sum_1[35]
PD_DEL      ---     1.617 *dd[0:53].MB35 to *add[0:53].R36 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         2   e 0.497 *add[0:53].R36 to *2/SLICE_44.B1 signal_process/rs422/un3_p_sum_add[36]
C1TOFCO_DE  ---     0.401 *2/SLICE_44.B1 to */SLICE_44.FCO signal_process/rs422/SLICE_44
ROUTE         1   e 0.001 */SLICE_44.FCO to */SLICE_45.FCI signal_process/rs422/un3_p_sum_add_cry_0
FCITOFCO_D  ---     0.063 */SLICE_45.FCI to */SLICE_45.FCO signal_process/rs422/SLICE_45
ROUTE         1   e 0.001 */SLICE_45.FCO to */SLICE_46.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063 */SLICE_46.FCI to */SLICE_46.FCO signal_process/rs422/SLICE_46
ROUTE         1   e 0.001 */SLICE_46.FCO to */SLICE_47.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063 */SLICE_47.FCI to */SLICE_47.FCO signal_process/rs422/SLICE_47
ROUTE         1   e 0.001 */SLICE_47.FCO to */SLICE_48.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063 */SLICE_48.FCI to */SLICE_48.FCO signal_process/rs422/SLICE_48
ROUTE         1   e 0.001 */SLICE_48.FCO to */SLICE_49.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063 */SLICE_49.FCI to */SLICE_49.FCO signal_process/rs422/SLICE_49
ROUTE         1   e 0.001 */SLICE_49.FCO to */SLICE_50.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063 */SLICE_50.FCI to */SLICE_50.FCO signal_process/rs422/SLICE_50
ROUTE         1   e 0.001 */SLICE_50.FCO to */SLICE_51.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063 */SLICE_51.FCI to */SLICE_51.FCO signal_process/rs422/SLICE_51
ROUTE         1   e 0.001 */SLICE_51.FCO to */SLICE_52.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063 */SLICE_52.FCI to */SLICE_52.FCO signal_process/rs422/SLICE_52
ROUTE         1   e 0.001 */SLICE_52.FCO to */SLICE_53.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412 */SLICE_53.FCI to *2/SLICE_53.F1 signal_process/rs422/SLICE_53
ROUTE         1   e 0.001 *2/SLICE_53.F1 to */SLICE_53.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.393   (86.4% logic, 13.6% route), 12 logic levels.

Report:  139.762MHz is the maximum frequency for this preference.


================================================================================
Preference: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
            3495 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 992.907ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[13]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[19]  (to wendu.clk_us +)

   Delay:               7.331ns  (47.4% logic, 52.6% route), 20 logic levels.

 Constraint Details:

      7.331ns physical path delay wendu/SLICE_568 to wendu/SLICE_570 meets
    1000.000ns delay constraint less
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 992.907ns

 Physical Path Details:

      Data path wendu/SLICE_568 to wendu/SLICE_570:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460 *SLICE_568.CLK to */SLICE_568.Q0 wendu/SLICE_568 (from wendu.clk_us)
ROUTE         3   e 0.497 */SLICE_568.Q0 to */SLICE_616.B1 wendu/cnt_us[13]
CTOF_DEL    ---     0.208 */SLICE_616.B1 to */SLICE_616.F1 wendu/SLICE_616
ROUTE         1   e 0.182 */SLICE_616.F1 to */SLICE_616.D0 wendu/un1_cur_state_3_i_a2_3_o3_5_4
CTOF_DEL    ---     0.208 */SLICE_616.D0 to */SLICE_616.F0 wendu/SLICE_616
ROUTE         3   e 0.497 */SLICE_616.F0 to */SLICE_610.A0 wendu/un1_cur_state_3_i_a2_3_o3_5
CTOF_DEL    ---     0.208 */SLICE_610.A0 to */SLICE_610.F0 wendu/SLICE_610
ROUTE         3   e 0.497 */SLICE_610.F0 to */SLICE_607.B0 wendu/un1_cur_state_3_i_a2_3_o3_0
CTOF_DEL    ---     0.208 */SLICE_607.B0 to */SLICE_607.F0 wendu/SLICE_607
ROUTE         2   e 0.497 */SLICE_607.F0 to */SLICE_557.A1 wendu/data_temp_1_sqmuxa_0_o3_0
CTOF_DEL    ---     0.208 */SLICE_557.A1 to */SLICE_557.F1 wendu/SLICE_557
ROUTE         3   e 0.497 */SLICE_557.F1 to */SLICE_606.B1 wendu/data_temp_1_sqmuxa_0_o3_0_RNIT8UQ
CTOF_DEL    ---     0.208 */SLICE_606.B1 to */SLICE_606.F1 wendu/SLICE_606
ROUTE        19   e 0.182 */SLICE_606.F1 to */SLICE_606.A0 wendu/N_93_i
CTOF_DEL    ---     0.208 */SLICE_606.A0 to */SLICE_606.F0 wendu/SLICE_606
ROUTE         1   e 0.497 */SLICE_606.F0 to */SLICE_214.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401 */SLICE_214.B0 to *SLICE_214.FCO wendu/SLICE_214
ROUTE         1   e 0.001 *SLICE_214.FCO to *SLICE_215.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063 *SLICE_215.FCI to *SLICE_215.FCO wendu/SLICE_215
ROUTE         1   e 0.001 *SLICE_215.FCO to *SLICE_216.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063 *SLICE_216.FCI to *SLICE_216.FCO wendu/SLICE_216
ROUTE         1   e 0.001 *SLICE_216.FCO to *SLICE_217.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063 *SLICE_217.FCI to *SLICE_217.FCO wendu/SLICE_217
ROUTE         1   e 0.001 *SLICE_217.FCO to *SLICE_218.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063 *SLICE_218.FCI to *SLICE_218.FCO wendu/SLICE_218
ROUTE         1   e 0.001 *SLICE_218.FCO to *SLICE_219.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063 *SLICE_219.FCI to *SLICE_219.FCO wendu/SLICE_219
ROUTE         1   e 0.001 *SLICE_219.FCO to *SLICE_220.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063 *SLICE_220.FCI to *SLICE_220.FCO wendu/SLICE_220
ROUTE         1   e 0.001 *SLICE_220.FCO to *SLICE_221.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063 *SLICE_221.FCI to *SLICE_221.FCO wendu/SLICE_221
ROUTE         1   e 0.001 *SLICE_221.FCO to *SLICE_222.FCI wendu/un1_cnt_us_18_cry_14
FCITOFCO_D  ---     0.063 *SLICE_222.FCI to *SLICE_222.FCO wendu/SLICE_222
ROUTE         1   e 0.001 *SLICE_222.FCO to *SLICE_223.FCI wendu/un1_cnt_us_18_cry_16
FCITOFCO_D  ---     0.063 *SLICE_223.FCI to *SLICE_223.FCO wendu/SLICE_223
ROUTE         1   e 0.001 *SLICE_223.FCO to *SLICE_208.FCI wendu/un1_cnt_us_18_cry_18
FCITOF0_DE  ---     0.385 *SLICE_208.FCI to */SLICE_208.F0 wendu/SLICE_208
ROUTE         1   e 0.497 */SLICE_208.F0 to */SLICE_570.C1 wendu/un1_cnt_us_18_s_19_0_S0
CTOF_DEL    ---     0.208 */SLICE_570.C1 to */SLICE_570.F1 wendu/SLICE_570
ROUTE         1   e 0.001 */SLICE_570.F1 to *SLICE_570.DI1 wendu/cnt_us_12[19] (to wendu.clk_us)
                  --------
                    7.331   (47.4% logic, 52.6% route), 20 logic levels.

Report:  140.984MHz is the maximum frequency for this preference.


================================================================================
Preference: FREQUENCY NET "clk_in_c" 20.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
Preference: FREQUENCY NET "clk_AD" 60.000000 MHz ;
            3075 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 5.222ns (weighted slack = 10.444ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/demodu/fifo/FF_19  (to clk_AD +)

   Delay:               3.347ns  (55.3% logic, 44.7% route), 8 logic levels.

 Constraint Details:

      3.347ns physical path delay signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_296 meets
      8.334ns delay constraint less
     -0.235ns DIN_SET requirement (totaling 8.569ns) by 5.222ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_296:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460 *SLICE_286.CLK to */SLICE_286.Q0 signal_process/demodu/SLICE_286 (from clk120mhz)
ROUTE         4   e 0.497 */SLICE_286.Q0 to */SLICE_642.A0 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208 */SLICE_642.A0 to */SLICE_642.F0 signal_process/demodu/fifo/SLICE_642
ROUTE         9   e 0.497 */SLICE_642.F0 to */SLICE_109.A0 signal_process/demodu/fifo/rden_i
C0TOFCO_DE  ---     0.401 */SLICE_109.A0 to *SLICE_109.FCO signal_process/demodu/fifo/SLICE_109
ROUTE         1   e 0.001 *SLICE_109.FCO to *SLICE_110.FCI signal_process/demodu/fifo/co0_1
FCITOFCO_D  ---     0.063 *SLICE_110.FCI to *SLICE_110.FCO signal_process/demodu/fifo/SLICE_110
ROUTE         1   e 0.001 *SLICE_110.FCO to *SLICE_111.FCI signal_process/demodu/fifo/co1_1
FCITOFCO_D  ---     0.063 *SLICE_111.FCI to *SLICE_111.FCO signal_process/demodu/fifo/SLICE_111
ROUTE         1   e 0.001 *SLICE_111.FCO to *SLICE_112.FCI signal_process/demodu/fifo/co2_1
FCITOFCO_D  ---     0.063 *SLICE_112.FCI to *SLICE_112.FCO signal_process/demodu/fifo/SLICE_112
ROUTE         1   e 0.001 *SLICE_112.FCO to *SLICE_113.FCI signal_process/demodu/fifo/cmp_le_1_c
FCITOF0_DE  ---     0.385 *SLICE_113.FCI to */SLICE_113.F0 signal_process/demodu/fifo/SLICE_113
ROUTE         1   e 0.497 */SLICE_113.F0 to */SLICE_296.D0 signal_process/demodu/fifo/cmp_le_1
CTOF_DEL    ---     0.208 */SLICE_296.D0 to */SLICE_296.F0 signal_process/demodu/fifo/SLICE_296
ROUTE         1   e 0.001 */SLICE_296.F0 to *SLICE_296.DI0 signal_process/demodu/fifo/empty_d (to clk_AD)
                  --------
                    3.347   (55.3% logic, 44.7% route), 8 logic levels.

Report:  160.668MHz is the maximum frequency for this preference.


================================================================================
Preference: FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
Preference: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
            1596 items scored.
--------------------------------------------------------------------------------


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[54]  (to clk120mhz +)

   Delay:               7.955ns  (93.4% logic, 6.6% route), 29 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *am_0_0_1.CLKR to *am_0_0_1.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2   e 0.497 *am_0_0_1.DO18 to */SLICE_130.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401 */SLICE_130.A1 to *SLICE_130.FCO signal_process/demodu/SLICE_130
ROUTE         1   e 0.001 *SLICE_130.FCO to *SLICE_131.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063 *SLICE_131.FCI to *SLICE_131.FCO signal_process/demodu/SLICE_131
ROUTE         1   e 0.001 *SLICE_131.FCO to *SLICE_132.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063 *SLICE_132.FCI to *SLICE_132.FCO signal_process/demodu/SLICE_132
ROUTE         1   e 0.001 *SLICE_132.FCO to *SLICE_133.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063 *SLICE_133.FCI to *SLICE_133.FCO signal_process/demodu/SLICE_133
ROUTE         1   e 0.001 *SLICE_133.FCO to *SLICE_134.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063 *SLICE_134.FCI to *SLICE_134.FCO signal_process/demodu/SLICE_134
ROUTE         1   e 0.001 *SLICE_134.FCO to *SLICE_135.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063 *SLICE_135.FCI to *SLICE_135.FCO signal_process/demodu/SLICE_135
ROUTE         1   e 0.001 *SLICE_135.FCO to *SLICE_136.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063 *SLICE_136.FCI to *SLICE_136.FCO signal_process/demodu/SLICE_136
ROUTE         1   e 0.001 *SLICE_136.FCO to *SLICE_137.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063 *SLICE_137.FCI to *SLICE_137.FCO signal_process/demodu/SLICE_137
ROUTE         1   e 0.001 *SLICE_137.FCO to *SLICE_138.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063 *SLICE_138.FCI to *SLICE_138.FCO signal_process/demodu/SLICE_138
ROUTE         1   e 0.001 *SLICE_138.FCO to *SLICE_139.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063 *SLICE_139.FCI to *SLICE_139.FCO signal_process/demodu/SLICE_139
ROUTE         1   e 0.001 *SLICE_139.FCO to *SLICE_140.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063 *SLICE_140.FCI to *SLICE_140.FCO signal_process/demodu/SLICE_140
ROUTE         1   e 0.001 *SLICE_140.FCO to *SLICE_141.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063 *SLICE_141.FCI to *SLICE_141.FCO signal_process/demodu/SLICE_141
ROUTE         1   e 0.001 *SLICE_141.FCO to *SLICE_142.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063 *SLICE_142.FCI to *SLICE_142.FCO signal_process/demodu/SLICE_142
ROUTE         1   e 0.001 *SLICE_142.FCO to *SLICE_143.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063 *SLICE_143.FCI to *SLICE_143.FCO signal_process/demodu/SLICE_143
ROUTE         1   e 0.001 *SLICE_143.FCO to *SLICE_144.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063 *SLICE_144.FCI to *SLICE_144.FCO signal_process/demodu/SLICE_144
ROUTE         1   e 0.001 *SLICE_144.FCO to *SLICE_145.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063 *SLICE_145.FCI to *SLICE_145.FCO signal_process/demodu/SLICE_145
ROUTE         1   e 0.001 *SLICE_145.FCO to *SLICE_146.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063 *SLICE_146.FCI to *SLICE_146.FCO signal_process/demodu/SLICE_146
ROUTE         1   e 0.001 *SLICE_146.FCO to *SLICE_147.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063 *SLICE_147.FCI to *SLICE_147.FCO signal_process/demodu/SLICE_147
ROUTE         1   e 0.001 *SLICE_147.FCO to *SLICE_148.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063 *SLICE_148.FCI to *SLICE_148.FCO signal_process/demodu/SLICE_148
ROUTE         1   e 0.001 *SLICE_148.FCO to *SLICE_149.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063 *SLICE_149.FCI to *SLICE_149.FCO signal_process/demodu/SLICE_149
ROUTE         1   e 0.001 *SLICE_149.FCO to *SLICE_150.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063 *SLICE_150.FCI to *SLICE_150.FCO signal_process/demodu/SLICE_150
ROUTE         1   e 0.001 *SLICE_150.FCO to *SLICE_151.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063 *SLICE_151.FCI to *SLICE_151.FCO signal_process/demodu/SLICE_151
ROUTE         1   e 0.001 *SLICE_151.FCO to *SLICE_152.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063 *SLICE_152.FCI to *SLICE_152.FCO signal_process/demodu/SLICE_152
ROUTE         1   e 0.001 *SLICE_152.FCO to *SLICE_153.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063 *SLICE_153.FCI to *SLICE_153.FCO signal_process/demodu/SLICE_153
ROUTE         1   e 0.001 *SLICE_153.FCO to *SLICE_154.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063 *SLICE_154.FCI to *SLICE_154.FCO signal_process/demodu/SLICE_154
ROUTE         1   e 0.001 *SLICE_154.FCO to *SLICE_155.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063 *SLICE_155.FCI to *SLICE_155.FCO signal_process/demodu/SLICE_155
ROUTE         1   e 0.001 *SLICE_155.FCO to *SLICE_156.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOFCO_D  ---     0.063 *SLICE_156.FCI to *SLICE_156.FCO signal_process/demodu/SLICE_156
ROUTE         1   e 0.001 *SLICE_156.FCO to *SLICE_157.FCI signal_process/demodu/INS_dout_1_cry_52
FCITOF1_DE  ---     0.412 *SLICE_157.FCI to */SLICE_157.F1 signal_process/demodu/SLICE_157
ROUTE         1   e 0.001 */SLICE_157.F1 to *SLICE_157.DI1 signal_process/demodu/INS_dout_1[54] (to clk120mhz)
                  --------
                    7.955   (93.4% logic, 6.6% route), 29 logic levels.

Report Summary
--------------
----------------------------------------------------------------------------
Preference                              |   Constraint|       Actual|Levels
----------------------------------------------------------------------------
                                        |             |             |
FREQUENCY NET "clk120mhz" 120.000000    |             |             |
MHz ;                                   |  120.000 MHz|  139.762 MHz|  12  
                                        |             |             |
FREQUENCY NET "wendu.clk_us" 1.000000   |             |             |
MHz ;                                   |    1.000 MHz|  140.984 MHz|  20  
                                        |             |             |
FREQUENCY NET "clk_in_c" 20.000000 MHz  |             |             |
;                                       |            -|            -|   0  
                                        |             |             |
FREQUENCY NET "clk_AD" 60.000000 MHz ;  |   60.000 MHz|  160.668 MHz|   8  
                                        |             |             |
FREQUENCY NET "clk_AD_t_c" 60.000000    |             |             |
MHz ;                                   |            -|            -|   0  
                                        |             |             |
----------------------------------------------------------------------------


All preferences were met.


Clock Domains Analysis
------------------------

Found 4 clocks:

Clock Domain: wendu.clk_us   Source: wendu/SLICE_224.Q0   Loads: 37
   Covered under: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;

Clock Domain: clk_in_c   Source: clk_in.PAD   Loads: 1
   No transfer within this clock domain is found

Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS   Loads: 101
   Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;

   Data transfers from:
   Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP
      Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;   Transfers: 2

Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP   Loads: 444
   Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;

   Data transfers from:
   Clock Domain: wendu.clk_us   Source: wendu/SLICE_224.Q0
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 16

   Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;


Timing summary (Setup):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38135 paths, 6 nets, and 3745 connections (90.13% coverage)

--------------------------------------------------------------------------------
Lattice TRACE Report - Hold, Version Diamond (64-bit) 3.12.1.454
Tue Jun 24 17:15:10 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Command line:    trce -v 1 -gt -mapchkpnt 0 -sethld -o INS350_5J_JZ_impl1.tw1 -gui -msgset D:/Project/INS350_5J_JZ _V2/promote.xml INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1_map.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,M
Report level:    verbose report, limited to 1 item per preference
--------------------------------------------------------------------------------

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0, defined by PAR)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7, defined by PAR)



================================================================================
Preference: FREQUENCY NET "clk120mhz" 120.000000 MHz ;
            4096 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 0.104ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/modu/mudu_dy[2]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/modu/mudu_dy[3]  (to clk120mhz +)

   Delay:               0.222ns  (73.9% logic, 26.1% route), 1 logic levels.

 Constraint Details:

      0.222ns physical path delay signal_process/modu/SLICE_388 to signal_process/modu/SLICE_388 meets
      0.118ns M_HLD and
      0.000ns delay constraint requirement (totaling 0.118ns) by 0.104ns

 Physical Path Details:

      Data path signal_process/modu/SLICE_388 to signal_process/modu/SLICE_388:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164 *SLICE_388.CLK to */SLICE_388.Q0 signal_process/modu/SLICE_388 (from clk120mhz)
ROUTE         1   e 0.058 */SLICE_388.Q0 to */SLICE_388.M1 signal_process/modu/mudu_dy[2] (to clk120mhz)
                  --------
                    0.222   (73.9% logic, 26.1% route), 1 logic levels.


================================================================================
Preference: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
            3495 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 0.103ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[1]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[0]  (to wendu.clk_us +)

   Delay:               0.221ns  (73.8% logic, 26.2% route), 1 logic levels.

 Constraint Details:

      0.221ns physical path delay wendu/SLICE_574 to wendu/SLICE_574 meets
      0.118ns M_HLD and
      0.000ns delay constraint requirement (totaling 0.118ns) by 0.103ns

 Physical Path Details:

      Data path wendu/SLICE_574 to wendu/SLICE_574:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163 *SLICE_574.CLK to */SLICE_574.Q1 wendu/SLICE_574 (from wendu.clk_us)
ROUTE         2   e 0.058 */SLICE_574.Q1 to */SLICE_574.M0 wendu/data_temp[1] (to wendu.clk_us)
                  --------
                    0.221   (73.8% logic, 26.2% route), 1 logic levels.


================================================================================
Preference: FREQUENCY NET "clk_in_c" 20.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
Preference: FREQUENCY NET "clk_AD" 60.000000 MHz ;
            3075 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 0.104ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_valid_dy[0]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/AD_valid_dy[1]  (to clk_AD +)

   Delay:               0.222ns  (73.9% logic, 26.1% route), 1 logic levels.

 Constraint Details:

      0.222ns physical path delay signal_process/demodu/SLICE_282 to signal_process/demodu/SLICE_282 meets
      0.118ns M_HLD and
      0.000ns delay constraint requirement (totaling 0.118ns) by 0.104ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_282 to signal_process/demodu/SLICE_282:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164 *SLICE_282.CLK to */SLICE_282.Q0 signal_process/demodu/SLICE_282 (from clk_AD)
ROUTE         2   e 0.058 */SLICE_282.Q0 to */SLICE_282.M1 signal_process/demodu/AD_valid_dy[0] (to clk_AD)
                  --------
                    0.222   (73.9% logic, 26.1% route), 1 logic levels.


================================================================================
Preference: FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
Preference: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
            1596 items scored.
--------------------------------------------------------------------------------


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[2]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[2]  (to clk120mhz +)

   Delay:               0.362ns  (45.0% logic, 55.0% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193 *SLICE_159.CLK to */SLICE_159.Q1 signal_process/demodu/SLICE_159 (from clk_AD)
ROUTE         3   e 0.199 */SLICE_159.Q1 to */SLICE_326.M0 signal_process/demodu/sample_sum[2] (to clk120mhz)
                  --------
                    0.392   (49.2% logic, 50.8% route), 1 logic levels.

Report Summary
--------------
----------------------------------------------------------------------------
Preference(MIN Delays)                  |   Constraint|       Actual|Levels
----------------------------------------------------------------------------
                                        |             |             |
FREQUENCY NET "clk120mhz" 120.000000    |             |             |
MHz ;                                   |     0.000 ns|     0.104 ns|   1  
                                        |             |             |
FREQUENCY NET "wendu.clk_us" 1.000000   |             |             |
MHz ;                                   |     0.000 ns|     0.103 ns|   1  
                                        |             |             |
FREQUENCY NET "clk_in_c" 20.000000 MHz  |             |             |
;                                       |            -|            -|   0  
                                        |             |             |
FREQUENCY NET "clk_AD" 60.000000 MHz ;  |     0.000 ns|     0.104 ns|   1  
                                        |             |             |
FREQUENCY NET "clk_AD_t_c" 60.000000    |             |             |
MHz ;                                   |            -|            -|   0  
                                        |             |             |
----------------------------------------------------------------------------


All preferences were met.


Clock Domains Analysis
------------------------

Found 4 clocks:

Clock Domain: wendu.clk_us   Source: wendu/SLICE_224.Q0   Loads: 37
   Covered under: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;

Clock Domain: clk_in_c   Source: clk_in.PAD   Loads: 1
   No transfer within this clock domain is found

Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS   Loads: 101
   Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;

   Data transfers from:
   Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP
      Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;   Transfers: 2

Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP   Loads: 444
   Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;

   Data transfers from:
   Clock Domain: wendu.clk_us   Source: wendu/SLICE_224.Q0
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 16

   Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;


Timing summary (Hold):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38135 paths, 6 nets, and 4121 connections (99.18% coverage)



Timing summary (Setup and Hold):
---------------

Timing errors: 0 (setup), 0 (hold)
Score: 0 (setup), 0 (hold)
Cumulative negative slack: 0 (0+0)
--------------------------------------------------------------------------------

--------------------------------------------------------------------------------

