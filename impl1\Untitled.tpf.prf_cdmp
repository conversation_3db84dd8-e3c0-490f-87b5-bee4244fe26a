BANK 0 VCCIO 3.3 V;
BANK 1 VCCIO 3.3 V;
BANK 2 VCCIO 3.3 V;
BANK 3 VCCIO 3.3 V;
BANK 6 VCCIO 3.3 V;
BANK 7 VCCIO 3.3 V;
BANK 8 VCCIO 3.3 V;
<PERSON><PERSON><PERSON><PERSON> RESETPATHS ;
BLOCK ASYNCPATHS ;
BLOC<PERSON> JTAGPATHS ;
IOBUF PORT "dq" IO_TYPE=LVCMOS25 ;
IOBUF PORT "TXD" IO_TYPE=LVCMOS25 ;
IOBUF PORT "clk_in" IO_TYPE=LVCMOS25 ;
IOBUF PORT "clk_DA" IO_TYPE=LVCMOS25 ;
IOBUF PORT "clk_AD_t" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[13]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[12]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[11]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[10]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[9]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[8]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[7]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[6]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[5]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[4]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[3]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[2]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[1]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "DA_DATA[0]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[11]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[10]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[9]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[8]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[7]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[6]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[5]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[4]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[3]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[2]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[1]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "AD_DATA[0]" IO_TYPE=LVCMOS25 ;
IOBUF PORT "TxTransmit" IO_TYPE=LVCMOS25 ;
BLOCK PATH FROM CLKNET "clk_AD_t_c" TO CLKNET "clk120mhz" ;
FREQUENCY NET "clk120mhz" 120.000000 MHz ;
FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
FREQUENCY NET "clk_in_c" 20.000000 MHz ;
FREQUENCY NET "clk_AD" 60.000000 MHz ;
FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[11]" ;
USE DIN TRUE CELL "wendu_data_tempio[15]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[0]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[1]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[2]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[3]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[4]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[5]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[6]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[7]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[8]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[9]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[10]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[11]" ;
USE DIN TRUE CELL "wendu_data_tempio[15]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[0]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[1]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[2]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[3]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[4]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[5]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[6]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[7]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[8]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[9]" ;
USE DIN TRUE CELL "signal_process_demodu_din_reg0io[10]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[13]" ;
USE DOUT TRUE CELL "u_uart_U1_uart_txio" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[0]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[1]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[2]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[3]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[4]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[5]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[6]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[7]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[8]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[9]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[10]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[11]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[12]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[13]" ;
USE DOUT TRUE CELL "u_uart_U1_uart_txio" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[0]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[1]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[2]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[3]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[4]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[5]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[6]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[7]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[8]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[9]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[10]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[11]" ;
USE DOUT TRUE CELL "signal_process_modu_doutio[12]" ;
