<HTML>
<HEAD><TITLE>La<PERSON>ce TRACE Report</TITLE>
<STYLE TYPE="text/css">
<!--
 body,pre{
    font-family:'Courier New', monospace;
    color: #000000;
    font-size:88%;
    background-color: #ffffff;
}
h1 {
    font-weight: bold;
    margin-top: 24px;
    margin-bottom: 10px;
    border-bottom: 3px solid #000;    font-size: 1em;
}
h2 {
    font-weight: bold;
    margin-top: 18px;
    margin-bottom: 5px;
    font-size: 0.90em;
}
h3 {
    font-weight: bold;
    margin-top: 12px;
    margin-bottom: 5px;
    font-size: 0.80em;
}
p {
    font-size:78%;
}
P.Table {
    margin-top: 4px;
    margin-bottom: 4px;
    margin-right: 4px;
    margin-left: 4px;
}
table
{
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    border-collapse: collapse;
}
th {
    font-weight:bold;
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    text-align:left;
    font-size:78%;
}
td {
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    font-size:78%;
}
a {
    color:#013C9A;
    text-decoration:none;
}

a:visited {
    color:#013C9A;
}

a:hover, a:active {
    text-decoration:underline;
    color:#5BAFD4;
}
.pass
{
background-color: #00ff00;
}
.fail
{
background-color: #ff0000;
}
.comment
{
    font-size: 90%;
    font-style: italic;
}

-->
</STYLE>
</HEAD>
<PRE><A name="Par_Twr"></A><B><U><big>Place & Route TRACE Report</big></U></B>

Loading design for application trce from file ins350_5j_jz_impl1.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application trce from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.
Setup and Hold Report

--------------------------------------------------------------------------------
<A name="Par_Twr_setup"></A><B><U><big>Lattice TRACE Report - Setup, Version Diamond (64-bit) 3.12.1.454</big></U></B>
Tue Jun 24 11:16:10 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

<A name="ptwr_set_ri"></A><B><U><big>Report Information</big></U></B>
------------------
Command line:    trce -v 10 -gt -sethld -sp 7 -sphld m -o INS350_5J_JZ_impl1.twr -gui -msgset D:/Project/INS350_5J_JZ _V2/promote.xml INS350_5J_JZ_impl1.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,7
Report level:    verbose report, limited to 10 items per preference
--------------------------------------------------------------------------------

<A name="ptwr_set_ps"></A><B><U><big>Preference Summary</big></U></B>

<LI><A href='#par_twr_pref_0_0' Target='right'>FREQUENCY NET "clk120mhz" 120.000000 MHz (0 errors)</A></LI>            4096 items scored, 0 timing errors detected.
Report:  140.726MHz is the maximum frequency for this preference.

<LI><A href='#par_twr_pref_0_1' Target='right'>FREQUENCY NET "wendu.clk_us" 1.000000 MHz (0 errors)</A></LI>            3495 items scored, 0 timing errors detected.
Report:  109.051MHz is the maximum frequency for this preference.

<LI><A href='#par_twr_pref_0_2' Target='right'>FREQUENCY NET "clk_in_c" 20.000000 MHz (0 errors)</A></LI>            0 items scored, 0 timing errors detected.

<LI><A href='#par_twr_pref_0_3' Target='right'>FREQUENCY NET "clk_AD" 60.000000 MHz (0 errors)</A></LI>            3075 items scored, 0 timing errors detected.
Report:  155.666MHz is the maximum frequency for this preference.

<LI><A href='#par_twr_pref_0_4' Target='right'>FREQUENCY NET "clk_AD_t_c" 60.000000 MHz (0 errors)</A></LI>            0 items scored, 0 timing errors detected.

<LI><A href='#par_twr_pref_0_5' Target='right'>BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" (0 errors)</A></LI>            1596 items scored.

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2, defined by PAR)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7)



================================================================================
<A name="par_twr_pref_0_0"></A>Preference: FREQUENCY NET "clk120mhz" 120.000000 MHz ;
            4096 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P28 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P28 to *4_R13C54.MB28 signal_process/rs422/un3_p_sum_1[28]
PD_DEL      ---     1.617 *4_R13C54.MB28 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P34 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P34 to *4_R13C54.MB34 signal_process/rs422/un3_p_sum_1[34]
PD_DEL      ---     1.617 *4_R13C54.MB34 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P26 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P26 to *4_R13C54.MB26 signal_process/rs422/un3_p_sum_1[26]
PD_DEL      ---     1.617 *4_R13C54.MB26 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P30 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P30 to *4_R13C54.MB30 signal_process/rs422/un3_p_sum_1[30]
PD_DEL      ---     1.617 *4_R13C54.MB30 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P32 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P32 to *4_R13C54.MB32 signal_process/rs422/un3_p_sum_1[32]
PD_DEL      ---     1.617 *4_R13C54.MB32 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P33 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P33 to *4_R13C54.MB33 signal_process/rs422/un3_p_sum_1[33]
PD_DEL      ---     1.617 *4_R13C54.MB33 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P27 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P27 to *4_R13C54.MB27 signal_process/rs422/un3_p_sum_1[27]
PD_DEL      ---     1.617 *4_R13C54.MB27 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P29 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P29 to *4_R13C54.MB29 signal_process/rs422/un3_p_sum_1[29]
PD_DEL      ---     1.617 *4_R13C54.MB29 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P35 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P35 to *4_R13C54.MB35 signal_process/rs422/un3_p_sum_1[35]
PD_DEL      ---     1.617 *4_R13C54.MB35 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 1.227ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         MULT18X18D Port           signal_process/rs422/un3_p_sum[18:53](ASIC)  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum[55]  (to clk120mhz +)

   Delay:               7.255ns  (87.2% logic, 12.8% route), 11 logic levels.

 Constraint Details:

      7.255ns physical path delay signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53 meets
      8.333ns delay constraint less
      0.089ns skew and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 1.227ns

 Physical Path Details:

      Data path signal_process/rs422/un3_p_sum[18:53] to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
C2OUT_DEL   ---     3.455 *8_R13C52.CLK0 to *18_R13C52.P31 signal_process/rs422/un3_p_sum[18:53] (from clk120mhz)
ROUTE         1     0.000 *18_R13C52.P31 to *4_R13C54.MB31 signal_process/rs422/un3_p_sum_1[31]
PD_DEL      ---     1.617 *4_R13C54.MB31 to *54_R13C54.R37 signal_process/rs422/un3_p_sum_add[0:53]
ROUTE         1     0.929 *54_R13C54.R37 to     R14C54B.B0 signal_process/rs422/un3_p_sum_add[37]
C0TOFCO_DE  ---     0.401     R14C54B.B0 to    R14C54B.FCO signal_process/rs422/SLICE_45
ROUTE         1     0.000    R14C54B.FCO to    R14C54C.FCI signal_process/rs422/un3_p_sum_add_cry_2
FCITOFCO_D  ---     0.063    R14C54C.FCI to    R14C54C.FCO signal_process/rs422/SLICE_46
ROUTE         1     0.000    R14C54C.FCO to    R14C54D.FCI signal_process/rs422/un3_p_sum_add_cry_4
FCITOFCO_D  ---     0.063    R14C54D.FCI to    R14C54D.FCO signal_process/rs422/SLICE_47
ROUTE         1     0.000    R14C54D.FCO to    R14C55A.FCI signal_process/rs422/un3_p_sum_add_cry_6
FCITOFCO_D  ---     0.063    R14C55A.FCI to    R14C55A.FCO signal_process/rs422/SLICE_48
ROUTE         1     0.000    R14C55A.FCO to    R14C55B.FCI signal_process/rs422/un3_p_sum_add_cry_8
FCITOFCO_D  ---     0.063    R14C55B.FCI to    R14C55B.FCO signal_process/rs422/SLICE_49
ROUTE         1     0.000    R14C55B.FCO to    R14C55C.FCI signal_process/rs422/un3_p_sum_add_cry_10
FCITOFCO_D  ---     0.063    R14C55C.FCI to    R14C55C.FCO signal_process/rs422/SLICE_50
ROUTE         1     0.000    R14C55C.FCO to    R14C55D.FCI signal_process/rs422/un3_p_sum_add_cry_12
FCITOFCO_D  ---     0.063    R14C55D.FCI to    R14C55D.FCO signal_process/rs422/SLICE_51
ROUTE         1     0.000    R14C55D.FCO to    R14C56A.FCI signal_process/rs422/un3_p_sum_add_cry_14
FCITOFCO_D  ---     0.063    R14C56A.FCI to    R14C56A.FCO signal_process/rs422/SLICE_52
ROUTE         1     0.000    R14C56A.FCO to    R14C56B.FCI signal_process/rs422/un3_p_sum_add_cry_16
FCITOF1_DE  ---     0.412    R14C56B.FCI to     R14C56B.F1 signal_process/rs422/SLICE_53
ROUTE         1     0.000     R14C56B.F1 to    R14C56B.DI1 signal_process/rs422/un3_p_sum[54] (to clk120mhz)
                  --------
                    7.255   (87.2% logic, 12.8% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/un3_p_sum[18:53]:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.979  PLL_BR0.CLKOP to *8_R13C52.CLK0 clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_53:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     1.890  PLL_BR0.CLKOP to    R14C56B.CLK clk120mhz
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

Report:  140.726MHz is the maximum frequency for this preference.


================================================================================
<A name="par_twr_pref_0_1"></A>Preference: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
            3495 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 990.830ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[16]  (to wendu.clk_us +)

   Delay:               9.408ns  (31.5% logic, 68.5% route), 16 logic levels.

 Constraint Details:

      9.408ns physical path delay wendu/SLICE_563 to wendu/SLICE_569 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.830ns

 Physical Path Details:

      Data path wendu/SLICE_563 to wendu/SLICE_569:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29D.CLK to      R8C29D.Q1 wendu/SLICE_563 (from wendu.clk_us)
ROUTE         7     1.439      R8C29D.Q1 to     R10C30D.B0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R10C30D.B0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063     R7C29D.FCI to     R7C29D.FCO wendu/SLICE_217
ROUTE         1     0.000     R7C29D.FCO to     R7C30A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063     R7C30A.FCI to     R7C30A.FCO wendu/SLICE_218
ROUTE         1     0.000     R7C30A.FCO to     R7C30B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063     R7C30B.FCI to     R7C30B.FCO wendu/SLICE_219
ROUTE         1     0.000     R7C30B.FCO to     R7C30C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063     R7C30C.FCI to     R7C30C.FCO wendu/SLICE_220
ROUTE         1     0.000     R7C30C.FCO to     R7C30D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063     R7C30D.FCI to     R7C30D.FCO wendu/SLICE_221
ROUTE         1     0.000     R7C30D.FCO to     R7C31A.FCI wendu/un1_cnt_us_18_cry_14
FCITOF1_DE  ---     0.412     R7C31A.FCI to      R7C31A.F1 wendu/SLICE_222
ROUTE         1     0.682      R7C31A.F1 to      R8C31D.A1 wendu/un1_cnt_us_18_cry_15_0_S1
CTOF_DEL    ---     0.208      R8C31D.A1 to      R8C31D.F1 wendu/SLICE_569
ROUTE         1     0.000      R8C31D.F1 to     R8C31D.DI1 wendu/cnt_us_12[16] (to wendu.clk_us)
                  --------
                    9.408   (31.5% logic, 68.5% route), 16 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_563:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_569:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C31D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 990.883ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[19]  (to wendu.clk_us +)

   Delay:               9.355ns  (32.7% logic, 67.3% route), 18 logic levels.

 Constraint Details:

      9.355ns physical path delay wendu/SLICE_563 to wendu/SLICE_570 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.883ns

 Physical Path Details:

      Data path wendu/SLICE_563 to wendu/SLICE_570:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29D.CLK to      R8C29D.Q1 wendu/SLICE_563 (from wendu.clk_us)
ROUTE         7     1.439      R8C29D.Q1 to     R10C30D.B0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R10C30D.B0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063     R7C29D.FCI to     R7C29D.FCO wendu/SLICE_217
ROUTE         1     0.000     R7C29D.FCO to     R7C30A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063     R7C30A.FCI to     R7C30A.FCO wendu/SLICE_218
ROUTE         1     0.000     R7C30A.FCO to     R7C30B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063     R7C30B.FCI to     R7C30B.FCO wendu/SLICE_219
ROUTE         1     0.000     R7C30B.FCO to     R7C30C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063     R7C30C.FCI to     R7C30C.FCO wendu/SLICE_220
ROUTE         1     0.000     R7C30C.FCO to     R7C30D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063     R7C30D.FCI to     R7C30D.FCO wendu/SLICE_221
ROUTE         1     0.000     R7C30D.FCO to     R7C31A.FCI wendu/un1_cnt_us_18_cry_14
FCITOFCO_D  ---     0.063     R7C31A.FCI to     R7C31A.FCO wendu/SLICE_222
ROUTE         1     0.000     R7C31A.FCO to     R7C31B.FCI wendu/un1_cnt_us_18_cry_16
FCITOFCO_D  ---     0.063     R7C31B.FCI to     R7C31B.FCO wendu/SLICE_223
ROUTE         1     0.000     R7C31B.FCO to     R7C31C.FCI wendu/un1_cnt_us_18_cry_18
FCITOF0_DE  ---     0.385     R7C31C.FCI to      R7C31C.F0 wendu/SLICE_208
ROUTE         1     0.530      R7C31C.F0 to      R8C31B.C1 wendu/un1_cnt_us_18_s_19_0_S0
CTOF_DEL    ---     0.208      R8C31B.C1 to      R8C31B.F1 wendu/SLICE_570
ROUTE         1     0.000      R8C31B.F1 to     R8C31B.DI1 wendu/cnt_us_12[19] (to wendu.clk_us)
                  --------
                    9.355   (32.7% logic, 67.3% route), 18 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_563:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_570:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C31B.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 990.977ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[4]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[16]  (to wendu.clk_us +)

   Delay:               9.261ns  (32.0% logic, 68.0% route), 16 logic levels.

 Constraint Details:

      9.261ns physical path delay wendu/SLICE_564 to wendu/SLICE_569 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.977ns

 Physical Path Details:

      Data path wendu/SLICE_564 to wendu/SLICE_569:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29B.CLK to      R8C29B.Q1 wendu/SLICE_564 (from wendu.clk_us)
ROUTE         8     1.292      R8C29B.Q1 to     R10C30D.A0 wendu/cnt_us[4]
CTOF_DEL    ---     0.208     R10C30D.A0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063     R7C29D.FCI to     R7C29D.FCO wendu/SLICE_217
ROUTE         1     0.000     R7C29D.FCO to     R7C30A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063     R7C30A.FCI to     R7C30A.FCO wendu/SLICE_218
ROUTE         1     0.000     R7C30A.FCO to     R7C30B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063     R7C30B.FCI to     R7C30B.FCO wendu/SLICE_219
ROUTE         1     0.000     R7C30B.FCO to     R7C30C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063     R7C30C.FCI to     R7C30C.FCO wendu/SLICE_220
ROUTE         1     0.000     R7C30C.FCO to     R7C30D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063     R7C30D.FCI to     R7C30D.FCO wendu/SLICE_221
ROUTE         1     0.000     R7C30D.FCO to     R7C31A.FCI wendu/un1_cnt_us_18_cry_14
FCITOF1_DE  ---     0.412     R7C31A.FCI to      R7C31A.F1 wendu/SLICE_222
ROUTE         1     0.682      R7C31A.F1 to      R8C31D.A1 wendu/un1_cnt_us_18_cry_15_0_S1
CTOF_DEL    ---     0.208      R8C31D.A1 to      R8C31D.F1 wendu/SLICE_569
ROUTE         1     0.000      R8C31D.F1 to     R8C31D.DI1 wendu/cnt_us_12[16] (to wendu.clk_us)
                  --------
                    9.261   (32.0% logic, 68.0% route), 16 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_564:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29B.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_569:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C31D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.006ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[15]  (to wendu.clk_us +)

   Delay:               9.229ns  (31.8% logic, 68.2% route), 16 logic levels.

 Constraint Details:

      9.229ns physical path delay wendu/SLICE_563 to wendu/SLICE_569 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.235ns DIN_SET requirement (totaling 1000.235ns) by 991.006ns

 Physical Path Details:

      Data path wendu/SLICE_563 to wendu/SLICE_569:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29D.CLK to      R8C29D.Q1 wendu/SLICE_563 (from wendu.clk_us)
ROUTE         7     1.439      R8C29D.Q1 to     R10C30D.B0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R10C30D.B0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063     R7C29D.FCI to     R7C29D.FCO wendu/SLICE_217
ROUTE         1     0.000     R7C29D.FCO to     R7C30A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063     R7C30A.FCI to     R7C30A.FCO wendu/SLICE_218
ROUTE         1     0.000     R7C30A.FCO to     R7C30B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063     R7C30B.FCI to     R7C30B.FCO wendu/SLICE_219
ROUTE         1     0.000     R7C30B.FCO to     R7C30C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063     R7C30C.FCI to     R7C30C.FCO wendu/SLICE_220
ROUTE         1     0.000     R7C30C.FCO to     R7C30D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063     R7C30D.FCI to     R7C30D.FCO wendu/SLICE_221
ROUTE         1     0.000     R7C30D.FCO to     R7C31A.FCI wendu/un1_cnt_us_18_cry_14
FCITOF0_DE  ---     0.385     R7C31A.FCI to      R7C31A.F0 wendu/SLICE_222
ROUTE         1     0.530      R7C31A.F0 to      R8C31D.C0 wendu/un1_cnt_us_18_cry_15_0_S0
CTOF_DEL    ---     0.208      R8C31D.C0 to      R8C31D.F0 wendu/SLICE_569
ROUTE         1     0.000      R8C31D.F0 to     R8C31D.DI0 wendu/cnt_us_12[15] (to wendu.clk_us)
                  --------
                    9.229   (31.8% logic, 68.2% route), 16 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_563:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_569:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C31D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.006ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[14]  (to wendu.clk_us +)

   Delay:               9.232ns  (31.4% logic, 68.6% route), 15 logic levels.

 Constraint Details:

      9.232ns physical path delay wendu/SLICE_563 to wendu/SLICE_568 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.006ns

 Physical Path Details:

      Data path wendu/SLICE_563 to wendu/SLICE_568:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29D.CLK to      R8C29D.Q1 wendu/SLICE_563 (from wendu.clk_us)
ROUTE         7     1.439      R8C29D.Q1 to     R10C30D.B0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R10C30D.B0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063     R7C29D.FCI to     R7C29D.FCO wendu/SLICE_217
ROUTE         1     0.000     R7C29D.FCO to     R7C30A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063     R7C30A.FCI to     R7C30A.FCO wendu/SLICE_218
ROUTE         1     0.000     R7C30A.FCO to     R7C30B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063     R7C30B.FCI to     R7C30B.FCO wendu/SLICE_219
ROUTE         1     0.000     R7C30B.FCO to     R7C30C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063     R7C30C.FCI to     R7C30C.FCO wendu/SLICE_220
ROUTE         1     0.000     R7C30C.FCO to     R7C30D.FCI wendu/un1_cnt_us_18_cry_12
FCITOF1_DE  ---     0.412     R7C30D.FCI to      R7C30D.F1 wendu/SLICE_221
ROUTE         1     0.569      R7C30D.F1 to      R8C30C.D1 wendu/un1_cnt_us_18_cry_13_0_S1
CTOF_DEL    ---     0.208      R8C30C.D1 to      R8C30C.F1 wendu/SLICE_568
ROUTE         1     0.000      R8C30C.F1 to     R8C30C.DI1 wendu/cnt_us_12[14] (to wendu.clk_us)
                  --------
                    9.232   (31.4% logic, 68.6% route), 15 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_563:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_568:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C30C.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.019ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[10]  (to wendu.clk_us +)

   Delay:               9.219ns  (30.0% logic, 70.0% route), 13 logic levels.

 Constraint Details:

      9.219ns physical path delay wendu/SLICE_563 to wendu/SLICE_567 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.019ns

 Physical Path Details:

      Data path wendu/SLICE_563 to wendu/SLICE_567:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29D.CLK to      R8C29D.Q1 wendu/SLICE_563 (from wendu.clk_us)
ROUTE         7     1.439      R8C29D.Q1 to     R10C30D.B0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R10C30D.B0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063     R7C29D.FCI to     R7C29D.FCO wendu/SLICE_217
ROUTE         1     0.000     R7C29D.FCO to     R7C30A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063     R7C30A.FCI to     R7C30A.FCO wendu/SLICE_218
ROUTE         1     0.000     R7C30A.FCO to     R7C30B.FCI wendu/un1_cnt_us_18_cry_8
FCITOF1_DE  ---     0.412     R7C30B.FCI to      R7C30B.F1 wendu/SLICE_219
ROUTE         1     0.682      R7C30B.F1 to      R8C30D.A1 wendu/un1_cnt_us_18_cry_9_0_S1
CTOF_DEL    ---     0.208      R8C30D.A1 to      R8C30D.F1 wendu/SLICE_567
ROUTE         1     0.000      R8C30D.F1 to     R8C30D.DI1 wendu/cnt_us_12[10] (to wendu.clk_us)
                  --------
                    9.219   (30.0% logic, 70.0% route), 13 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_563:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_567:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C30D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.030ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[4]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[19]  (to wendu.clk_us +)

   Delay:               9.208ns  (33.2% logic, 66.8% route), 18 logic levels.

 Constraint Details:

      9.208ns physical path delay wendu/SLICE_564 to wendu/SLICE_570 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.030ns

 Physical Path Details:

      Data path wendu/SLICE_564 to wendu/SLICE_570:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29B.CLK to      R8C29B.Q1 wendu/SLICE_564 (from wendu.clk_us)
ROUTE         8     1.292      R8C29B.Q1 to     R10C30D.A0 wendu/cnt_us[4]
CTOF_DEL    ---     0.208     R10C30D.A0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063     R7C29D.FCI to     R7C29D.FCO wendu/SLICE_217
ROUTE         1     0.000     R7C29D.FCO to     R7C30A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063     R7C30A.FCI to     R7C30A.FCO wendu/SLICE_218
ROUTE         1     0.000     R7C30A.FCO to     R7C30B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063     R7C30B.FCI to     R7C30B.FCO wendu/SLICE_219
ROUTE         1     0.000     R7C30B.FCO to     R7C30C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063     R7C30C.FCI to     R7C30C.FCO wendu/SLICE_220
ROUTE         1     0.000     R7C30C.FCO to     R7C30D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063     R7C30D.FCI to     R7C30D.FCO wendu/SLICE_221
ROUTE         1     0.000     R7C30D.FCO to     R7C31A.FCI wendu/un1_cnt_us_18_cry_14
FCITOFCO_D  ---     0.063     R7C31A.FCI to     R7C31A.FCO wendu/SLICE_222
ROUTE         1     0.000     R7C31A.FCO to     R7C31B.FCI wendu/un1_cnt_us_18_cry_16
FCITOFCO_D  ---     0.063     R7C31B.FCI to     R7C31B.FCO wendu/SLICE_223
ROUTE         1     0.000     R7C31B.FCO to     R7C31C.FCI wendu/un1_cnt_us_18_cry_18
FCITOF0_DE  ---     0.385     R7C31C.FCI to      R7C31C.F0 wendu/SLICE_208
ROUTE         1     0.530      R7C31C.F0 to      R8C31B.C1 wendu/un1_cnt_us_18_s_19_0_S0
CTOF_DEL    ---     0.208      R8C31B.C1 to      R8C31B.F1 wendu/SLICE_570
ROUTE         1     0.000      R8C31B.F1 to     R8C31B.DI1 wendu/cnt_us_12[19] (to wendu.clk_us)
                  --------
                    9.208   (33.2% logic, 66.8% route), 18 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_564:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29B.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_570:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C31B.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.074ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[8]  (to wendu.clk_us +)

   Delay:               9.164ns  (29.5% logic, 70.5% route), 12 logic levels.

 Constraint Details:

      9.164ns physical path delay wendu/SLICE_563 to wendu/SLICE_566 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.074ns

 Physical Path Details:

      Data path wendu/SLICE_563 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29D.CLK to      R8C29D.Q1 wendu/SLICE_563 (from wendu.clk_us)
ROUTE         7     1.439      R8C29D.Q1 to     R10C30D.B0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R10C30D.B0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063     R7C29D.FCI to     R7C29D.FCO wendu/SLICE_217
ROUTE         1     0.000     R7C29D.FCO to     R7C30A.FCI wendu/un1_cnt_us_18_cry_6
FCITOF1_DE  ---     0.412     R7C30A.FCI to      R7C30A.F1 wendu/SLICE_218
ROUTE         1     0.690      R7C30A.F1 to      R8C30A.C1 wendu/un1_cnt_us_18_cry_7_0_S1
CTOF_DEL    ---     0.208      R8C30A.C1 to      R8C30A.F1 wendu/SLICE_566
ROUTE         1     0.000      R8C30A.F1 to     R8C30A.DI1 wendu/cnt_us_12[8] (to wendu.clk_us)
                  --------
                    9.164   (29.5% logic, 70.5% route), 12 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_563:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_566:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C30A.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.112ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[17]  (to wendu.clk_us +)

   Delay:               9.123ns  (32.8% logic, 67.2% route), 17 logic levels.

 Constraint Details:

      9.123ns physical path delay wendu/SLICE_563 to wendu/SLICE_570 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.235ns DIN_SET requirement (totaling 1000.235ns) by 991.112ns

 Physical Path Details:

      Data path wendu/SLICE_563 to wendu/SLICE_570:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29D.CLK to      R8C29D.Q1 wendu/SLICE_563 (from wendu.clk_us)
ROUTE         7     1.439      R8C29D.Q1 to     R10C30D.B0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R10C30D.B0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOFCO_D  ---     0.063     R7C29D.FCI to     R7C29D.FCO wendu/SLICE_217
ROUTE         1     0.000     R7C29D.FCO to     R7C30A.FCI wendu/un1_cnt_us_18_cry_6
FCITOFCO_D  ---     0.063     R7C30A.FCI to     R7C30A.FCO wendu/SLICE_218
ROUTE         1     0.000     R7C30A.FCO to     R7C30B.FCI wendu/un1_cnt_us_18_cry_8
FCITOFCO_D  ---     0.063     R7C30B.FCI to     R7C30B.FCO wendu/SLICE_219
ROUTE         1     0.000     R7C30B.FCO to     R7C30C.FCI wendu/un1_cnt_us_18_cry_10
FCITOFCO_D  ---     0.063     R7C30C.FCI to     R7C30C.FCO wendu/SLICE_220
ROUTE         1     0.000     R7C30C.FCO to     R7C30D.FCI wendu/un1_cnt_us_18_cry_12
FCITOFCO_D  ---     0.063     R7C30D.FCI to     R7C30D.FCO wendu/SLICE_221
ROUTE         1     0.000     R7C30D.FCO to     R7C31A.FCI wendu/un1_cnt_us_18_cry_14
FCITOFCO_D  ---     0.063     R7C31A.FCI to     R7C31A.FCO wendu/SLICE_222
ROUTE         1     0.000     R7C31A.FCO to     R7C31B.FCI wendu/un1_cnt_us_18_cry_16
FCITOF0_DE  ---     0.385     R7C31B.FCI to      R7C31B.F0 wendu/SLICE_223
ROUTE         1     0.361      R7C31B.F0 to      R8C31B.D0 wendu/un1_cnt_us_18_cry_17_0_S0
CTOF_DEL    ---     0.208      R8C31B.D0 to      R8C31B.F0 wendu/SLICE_570
ROUTE         1     0.000      R8C31B.F0 to     R8C31B.DI0 wendu/cnt_us_12[17] (to wendu.clk_us)
                  --------
                    9.123   (32.8% logic, 67.2% route), 17 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_563:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_570:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C31B.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 991.145ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/cnt_us[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/cnt_us[6]  (to wendu.clk_us +)

   Delay:               9.093ns  (29.1% logic, 70.9% route), 11 logic levels.

 Constraint Details:

      9.093ns physical path delay wendu/SLICE_563 to wendu/SLICE_565 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.145ns

 Physical Path Details:

      Data path wendu/SLICE_563 to wendu/SLICE_565:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457     R8C29D.CLK to      R8C29D.Q1 wendu/SLICE_563 (from wendu.clk_us)
ROUTE         7     1.439      R8C29D.Q1 to     R10C30D.B0 wendu/cnt_us[2]
CTOF_DEL    ---     0.208     R10C30D.B0 to     R10C30D.F0 wendu/SLICE_599
ROUTE         2     0.803     R10C30D.F0 to      R9C31D.C0 wendu/cnt_us_RNIM6RG[1]
CTOF_DEL    ---     0.208      R9C31D.C0 to      R9C31D.F0 wendu/SLICE_600
ROUTE         2     0.905      R9C31D.F0 to     R10C31A.B1 wendu/cnt_us_RNIRSM11[6]
CTOF_DEL    ---     0.208     R10C31A.B1 to     R10C31A.F1 wendu/SLICE_608
ROUTE         4     0.525     R10C31A.F1 to     R10C31D.C1 wendu/un1_cur_state_3_i_a2_3_o3_1
CTOF_DEL    ---     0.208     R10C31D.C1 to     R10C31D.F1 wendu/SLICE_586
ROUTE        18     1.223     R10C31D.F1 to     R10C29B.A0 wendu/un1_cur_state_3_i_a2_3_a2
CTOF_DEL    ---     0.208     R10C29B.A0 to     R10C29B.F0 wendu/SLICE_606
ROUTE         1     0.872     R10C29B.F0 to      R7C29A.B0 wendu/un1_cnt_us_0_sqmuxa_2_i
C0TOFCO_DE  ---     0.401      R7C29A.B0 to     R7C29A.FCO wendu/SLICE_214
ROUTE         1     0.000     R7C29A.FCO to     R7C29B.FCI wendu/un1_cnt_us_18_cry_0
FCITOFCO_D  ---     0.063     R7C29B.FCI to     R7C29B.FCO wendu/SLICE_215
ROUTE         1     0.000     R7C29B.FCO to     R7C29C.FCI wendu/un1_cnt_us_18_cry_2
FCITOFCO_D  ---     0.063     R7C29C.FCI to     R7C29C.FCO wendu/SLICE_216
ROUTE         1     0.000     R7C29C.FCO to     R7C29D.FCI wendu/un1_cnt_us_18_cry_4
FCITOF1_DE  ---     0.412     R7C29D.FCI to      R7C29D.F1 wendu/SLICE_217
ROUTE         1     0.682      R7C29D.F1 to      R8C29A.A1 wendu/un1_cnt_us_18_cry_5_0_S1
CTOF_DEL    ---     0.208      R8C29A.A1 to      R8C29A.F1 wendu/SLICE_565
ROUTE         1     0.000      R8C29A.F1 to     R8C29A.DI1 wendu/cnt_us_12[6] (to wendu.clk_us)
                  --------
                    9.093   (29.1% logic, 70.9% route), 11 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_563:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29D.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_565:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.394     R38C31A.Q0 to     R8C29A.CLK wendu.clk_us
                  --------
                    2.394   (0.0% logic, 100.0% route), 0 logic levels.

Report:  109.051MHz is the maximum frequency for this preference.


================================================================================
<A name="par_twr_pref_0_2"></A>Preference: FREQUENCY NET "clk_in_c" 20.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
<A name="par_twr_pref_0_3"></A>Preference: FREQUENCY NET "clk_AD" 60.000000 MHz ;
            3075 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 5.122ns (weighted slack = 10.244ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/demodu/fifo/FF_19  (to clk_AD +)

   Delay:               3.447ns  (53.7% logic, 46.3% route), 8 logic levels.

 Constraint Details:

      3.447ns physical path delay signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_296 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.569ns) by 5.122ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_296:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R32C43D.CLK to     R32C43D.Q0 signal_process/demodu/SLICE_286 (from clk120mhz)
ROUTE         4     0.371     R32C43D.Q0 to     R30C43D.D0 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R30C43D.D0 to     R30C43D.F0 signal_process/demodu/fifo/SLICE_642
ROUTE         9     0.695     R30C43D.F0 to     R30C42B.B0 signal_process/demodu/fifo/rden_i
C0TOFCO_DE  ---     0.401     R30C42B.B0 to    R30C42B.FCO signal_process/demodu/fifo/SLICE_109
ROUTE         1     0.000    R30C42B.FCO to    R30C42C.FCI signal_process/demodu/fifo/co0_1
FCITOFCO_D  ---     0.063    R30C42C.FCI to    R30C42C.FCO signal_process/demodu/fifo/SLICE_110
ROUTE         1     0.000    R30C42C.FCO to    R30C42D.FCI signal_process/demodu/fifo/co1_1
FCITOFCO_D  ---     0.063    R30C42D.FCI to    R30C42D.FCO signal_process/demodu/fifo/SLICE_111
ROUTE         1     0.000    R30C42D.FCO to    R30C43A.FCI signal_process/demodu/fifo/co2_1
FCITOFCO_D  ---     0.063    R30C43A.FCI to    R30C43A.FCO signal_process/demodu/fifo/SLICE_112
ROUTE         1     0.000    R30C43A.FCO to    R30C43B.FCI signal_process/demodu/fifo/cmp_le_1_c
FCITOF0_DE  ---     0.385    R30C43B.FCI to     R30C43B.F0 signal_process/demodu/fifo/SLICE_113
ROUTE         1     0.530     R30C43B.F0 to     R31C43C.C0 signal_process/demodu/fifo/cmp_le_1
CTOF_DEL    ---     0.208     R31C43C.C0 to     R31C43C.F0 signal_process/demodu/fifo/SLICE_296
ROUTE         1     0.000     R31C43C.F0 to    R31C43C.DI0 signal_process/demodu/fifo/empty_d (to clk_AD)
                  --------
                    3.447   (53.7% logic, 46.3% route), 8 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_286:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.890  PLL_BR0.CLKOP to    R32C43D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/SLICE_296:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.890  PLL_BR0.CLKOS to    R31C43C.CLK clk_AD
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 5.282ns (weighted slack = 10.564ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/demodu/fifo/FF_20  (to clk_AD +)

   Delay:               3.290ns  (50.8% logic, 49.2% route), 7 logic levels.

 Constraint Details:

      3.290ns physical path delay signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_107 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.572ns) by 5.282ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_107:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R32C43D.CLK to     R32C43D.Q0 signal_process/demodu/SLICE_286 (from clk120mhz)
ROUTE         4     0.681     R32C43D.Q0 to     R32C43B.A1 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R32C43B.A1 to     R32C43B.F1 signal_process/demodu/fifo/SLICE_619
ROUTE        10     0.939     R32C43B.F1 to     R32C42A.A1 signal_process/demodu/fifo/cnt_con
C1TOFCO_DE  ---     0.401     R32C42A.A1 to    R32C42A.FCO signal_process/demodu/fifo/SLICE_103
ROUTE         1     0.000    R32C42A.FCO to    R32C42B.FCI signal_process/demodu/fifo/bdcnt_bctr_ci
FCITOFCO_D  ---     0.063    R32C42B.FCI to    R32C42B.FCO signal_process/demodu/fifo/SLICE_104
ROUTE         1     0.000    R32C42B.FCO to    R32C42C.FCI signal_process/demodu/fifo/co0
FCITOFCO_D  ---     0.063    R32C42C.FCI to    R32C42C.FCO signal_process/demodu/fifo/SLICE_105
ROUTE         1     0.000    R32C42C.FCO to    R32C42D.FCI signal_process/demodu/fifo/co1
FCITOFCO_D  ---     0.063    R32C42D.FCI to    R32C42D.FCO signal_process/demodu/fifo/SLICE_106
ROUTE         1     0.000    R32C42D.FCO to    R32C43A.FCI signal_process/demodu/fifo/co2
FCITOF1_DE  ---     0.412    R32C43A.FCI to     R32C43A.F1 signal_process/demodu/fifo/SLICE_107
ROUTE         1     0.000     R32C43A.F1 to    R32C43A.DI1 signal_process/demodu/fifo/ifcount_7 (to clk_AD)
                  --------
                    3.290   (50.8% logic, 49.2% route), 7 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_286:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.890  PLL_BR0.CLKOP to    R32C43D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/SLICE_107:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.890  PLL_BR0.CLKOS to    R32C43A.CLK clk_AD
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 5.306ns (weighted slack = 10.612ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/demodu/fifo/FF_21  (to clk_AD +)

   Delay:               3.263ns  (50.4% logic, 49.6% route), 7 logic levels.

 Constraint Details:

      3.263ns physical path delay signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_107 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.569ns) by 5.306ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_107:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R32C43D.CLK to     R32C43D.Q0 signal_process/demodu/SLICE_286 (from clk120mhz)
ROUTE         4     0.681     R32C43D.Q0 to     R32C43B.A1 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R32C43B.A1 to     R32C43B.F1 signal_process/demodu/fifo/SLICE_619
ROUTE        10     0.939     R32C43B.F1 to     R32C42A.A1 signal_process/demodu/fifo/cnt_con
C1TOFCO_DE  ---     0.401     R32C42A.A1 to    R32C42A.FCO signal_process/demodu/fifo/SLICE_103
ROUTE         1     0.000    R32C42A.FCO to    R32C42B.FCI signal_process/demodu/fifo/bdcnt_bctr_ci
FCITOFCO_D  ---     0.063    R32C42B.FCI to    R32C42B.FCO signal_process/demodu/fifo/SLICE_104
ROUTE         1     0.000    R32C42B.FCO to    R32C42C.FCI signal_process/demodu/fifo/co0
FCITOFCO_D  ---     0.063    R32C42C.FCI to    R32C42C.FCO signal_process/demodu/fifo/SLICE_105
ROUTE         1     0.000    R32C42C.FCO to    R32C42D.FCI signal_process/demodu/fifo/co1
FCITOFCO_D  ---     0.063    R32C42D.FCI to    R32C42D.FCO signal_process/demodu/fifo/SLICE_106
ROUTE         1     0.000    R32C42D.FCO to    R32C43A.FCI signal_process/demodu/fifo/co2
FCITOF0_DE  ---     0.385    R32C43A.FCI to     R32C43A.F0 signal_process/demodu/fifo/SLICE_107
ROUTE         1     0.000     R32C43A.F0 to    R32C43A.DI0 signal_process/demodu/fifo/ifcount_6 (to clk_AD)
                  --------
                    3.263   (50.4% logic, 49.6% route), 7 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_286:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.890  PLL_BR0.CLKOP to    R32C43D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/SLICE_107:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.890  PLL_BR0.CLKOS to    R32C43A.CLK clk_AD
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 5.334ns (weighted slack = 10.668ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (to clk_AD +)

   Delay:               2.777ns  (24.1% logic, 75.9% route), 2 logic levels.

 Constraint Details:

      2.777ns physical path delay signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      8.334ns delay constraint less
     -0.089ns skew and
      0.000ns feedback compensation and
      0.312ns CE_SET requirement (totaling 8.111ns) by 5.334ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R32C43D.CLK to     R32C43D.Q0 signal_process/demodu/SLICE_286 (from clk120mhz)
ROUTE         4     0.371     R32C43D.Q0 to     R30C43D.D0 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R30C43D.D0 to     R30C43D.F0 signal_process/demodu/fifo/SLICE_642
ROUTE         9     1.738     R30C43D.F0 to EBR_R25C48.CER signal_process/demodu/fifo/rden_i (to clk_AD)
                  --------
                    2.777   (24.1% logic, 75.9% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_286:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.890  PLL_BR0.CLKOP to    R32C43D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.979  PLL_BR0.CLKOS to *R_R25C48.CLKR clk_AD
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 5.345ns (weighted slack = 10.690ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/demodu/fifo/FF_22  (to clk_AD +)

   Delay:               3.227ns  (49.8% logic, 50.2% route), 6 logic levels.

 Constraint Details:

      3.227ns physical path delay signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_106 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.572ns) by 5.345ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_106:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R32C43D.CLK to     R32C43D.Q0 signal_process/demodu/SLICE_286 (from clk120mhz)
ROUTE         4     0.681     R32C43D.Q0 to     R32C43B.A1 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R32C43B.A1 to     R32C43B.F1 signal_process/demodu/fifo/SLICE_619
ROUTE        10     0.939     R32C43B.F1 to     R32C42A.A1 signal_process/demodu/fifo/cnt_con
C1TOFCO_DE  ---     0.401     R32C42A.A1 to    R32C42A.FCO signal_process/demodu/fifo/SLICE_103
ROUTE         1     0.000    R32C42A.FCO to    R32C42B.FCI signal_process/demodu/fifo/bdcnt_bctr_ci
FCITOFCO_D  ---     0.063    R32C42B.FCI to    R32C42B.FCO signal_process/demodu/fifo/SLICE_104
ROUTE         1     0.000    R32C42B.FCO to    R32C42C.FCI signal_process/demodu/fifo/co0
FCITOFCO_D  ---     0.063    R32C42C.FCI to    R32C42C.FCO signal_process/demodu/fifo/SLICE_105
ROUTE         1     0.000    R32C42C.FCO to    R32C42D.FCI signal_process/demodu/fifo/co1
FCITOF1_DE  ---     0.412    R32C42D.FCI to     R32C42D.F1 signal_process/demodu/fifo/SLICE_106
ROUTE         1     0.000     R32C42D.F1 to    R32C42D.DI1 signal_process/demodu/fifo/ifcount_5 (to clk_AD)
                  --------
                    3.227   (49.8% logic, 50.2% route), 6 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_286:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.890  PLL_BR0.CLKOP to    R32C43D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/SLICE_106:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.890  PLL_BR0.CLKOS to    R32C42D.CLK clk_AD
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 5.369ns (weighted slack = 10.738ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/demodu/fifo/FF_23  (to clk_AD +)

   Delay:               3.200ns  (49.4% logic, 50.6% route), 6 logic levels.

 Constraint Details:

      3.200ns physical path delay signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_106 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.569ns) by 5.369ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_106:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R32C43D.CLK to     R32C43D.Q0 signal_process/demodu/SLICE_286 (from clk120mhz)
ROUTE         4     0.681     R32C43D.Q0 to     R32C43B.A1 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R32C43B.A1 to     R32C43B.F1 signal_process/demodu/fifo/SLICE_619
ROUTE        10     0.939     R32C43B.F1 to     R32C42A.A1 signal_process/demodu/fifo/cnt_con
C1TOFCO_DE  ---     0.401     R32C42A.A1 to    R32C42A.FCO signal_process/demodu/fifo/SLICE_103
ROUTE         1     0.000    R32C42A.FCO to    R32C42B.FCI signal_process/demodu/fifo/bdcnt_bctr_ci
FCITOFCO_D  ---     0.063    R32C42B.FCI to    R32C42B.FCO signal_process/demodu/fifo/SLICE_104
ROUTE         1     0.000    R32C42B.FCO to    R32C42C.FCI signal_process/demodu/fifo/co0
FCITOFCO_D  ---     0.063    R32C42C.FCI to    R32C42C.FCO signal_process/demodu/fifo/SLICE_105
ROUTE         1     0.000    R32C42C.FCO to    R32C42D.FCI signal_process/demodu/fifo/co1
FCITOF0_DE  ---     0.385    R32C42D.FCI to     R32C42D.F0 signal_process/demodu/fifo/SLICE_106
ROUTE         1     0.000     R32C42D.F0 to    R32C42D.DI0 signal_process/demodu/fifo/ifcount_4 (to clk_AD)
                  --------
                    3.200   (49.4% logic, 50.6% route), 6 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_286:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.890  PLL_BR0.CLKOP to    R32C43D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/SLICE_106:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.890  PLL_BR0.CLKOS to    R32C42D.CLK clk_AD
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 10.756ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/din_reg1[2]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[55]  (to clk_AD +)

   Delay:               6.146ns  (46.9% logic, 53.1% route), 29 logic levels.

 Constraint Details:

      6.146ns physical path delay signal_process/demodu/SLICE_290 to signal_process/demodu/SLICE_186 meets
     16.667ns delay constraint less
      0.000ns skew and
     -0.235ns DIN_SET requirement (totaling 16.902ns) by 10.756ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_290 to signal_process/demodu/SLICE_186:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R44C12D.CLK to     R44C12D.Q0 signal_process/demodu/SLICE_290 (from clk_AD)
ROUTE         1     3.262     R44C12D.Q0 to     R29C46B.A1 signal_process/demodu/din_reg1[2]
C1TOFCO_DE  ---     0.401     R29C46B.A1 to    R29C46B.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R29C46B.FCO to    R29C46C.FCI signal_process/demodu/un3_sample_sum_cry_2
FCITOFCO_D  ---     0.063    R29C46C.FCI to    R29C46C.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R29C46C.FCO to    R29C46D.FCI signal_process/demodu/un3_sample_sum_cry_4
FCITOFCO_D  ---     0.063    R29C46D.FCI to    R29C46D.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R29C46D.FCO to    R29C47A.FCI signal_process/demodu/un3_sample_sum_cry_6
FCITOFCO_D  ---     0.063    R29C47A.FCI to    R29C47A.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R29C47A.FCO to    R29C47B.FCI signal_process/demodu/un3_sample_sum_cry_8
FCITOFCO_D  ---     0.063    R29C47B.FCI to    R29C47B.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R29C47B.FCO to    R29C47C.FCI signal_process/demodu/un3_sample_sum_cry_10
FCITOFCO_D  ---     0.063    R29C47C.FCI to    R29C47C.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R29C47C.FCO to    R29C47D.FCI signal_process/demodu/un3_sample_sum_cry_12
FCITOFCO_D  ---     0.063    R29C47D.FCI to    R29C47D.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R29C47D.FCO to    R29C48A.FCI signal_process/demodu/un3_sample_sum_cry_14
FCITOFCO_D  ---     0.063    R29C48A.FCI to    R29C48A.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R29C48A.FCO to    R29C48B.FCI signal_process/demodu/un3_sample_sum_cry_16
FCITOFCO_D  ---     0.063    R29C48B.FCI to    R29C48B.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R29C48B.FCO to    R29C48C.FCI signal_process/demodu/un3_sample_sum_cry_18
FCITOFCO_D  ---     0.063    R29C48C.FCI to    R29C48C.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R29C48C.FCO to    R29C48D.FCI signal_process/demodu/un3_sample_sum_cry_20
FCITOFCO_D  ---     0.063    R29C48D.FCI to    R29C48D.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R29C48D.FCO to    R29C49A.FCI signal_process/demodu/un3_sample_sum_cry_22
FCITOFCO_D  ---     0.063    R29C49A.FCI to    R29C49A.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R29C49A.FCO to    R29C49B.FCI signal_process/demodu/un3_sample_sum_cry_24
FCITOFCO_D  ---     0.063    R29C49B.FCI to    R29C49B.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R29C49B.FCO to    R29C49C.FCI signal_process/demodu/un3_sample_sum_cry_26
FCITOFCO_D  ---     0.063    R29C49C.FCI to    R29C49C.FCO signal_process/demodu/SLICE_172
ROUTE         1     0.000    R29C49C.FCO to    R29C49D.FCI signal_process/demodu/un3_sample_sum_cry_28
FCITOFCO_D  ---     0.063    R29C49D.FCI to    R29C49D.FCO signal_process/demodu/SLICE_173
ROUTE         1     0.000    R29C49D.FCO to    R29C50A.FCI signal_process/demodu/un3_sample_sum_cry_30
FCITOFCO_D  ---     0.063    R29C50A.FCI to    R29C50A.FCO signal_process/demodu/SLICE_174
ROUTE         1     0.000    R29C50A.FCO to    R29C50B.FCI signal_process/demodu/un3_sample_sum_cry_32
FCITOFCO_D  ---     0.063    R29C50B.FCI to    R29C50B.FCO signal_process/demodu/SLICE_175
ROUTE         1     0.000    R29C50B.FCO to    R29C50C.FCI signal_process/demodu/un3_sample_sum_cry_34
FCITOFCO_D  ---     0.063    R29C50C.FCI to    R29C50C.FCO signal_process/demodu/SLICE_176
ROUTE         1     0.000    R29C50C.FCO to    R29C50D.FCI signal_process/demodu/un3_sample_sum_cry_36
FCITOFCO_D  ---     0.063    R29C50D.FCI to    R29C50D.FCO signal_process/demodu/SLICE_177
ROUTE         1     0.000    R29C50D.FCO to    R29C51A.FCI signal_process/demodu/un3_sample_sum_cry_38
FCITOFCO_D  ---     0.063    R29C51A.FCI to    R29C51A.FCO signal_process/demodu/SLICE_178
ROUTE         1     0.000    R29C51A.FCO to    R29C51B.FCI signal_process/demodu/un3_sample_sum_cry_40
FCITOFCO_D  ---     0.063    R29C51B.FCI to    R29C51B.FCO signal_process/demodu/SLICE_179
ROUTE         1     0.000    R29C51B.FCO to    R29C51C.FCI signal_process/demodu/un3_sample_sum_cry_42
FCITOFCO_D  ---     0.063    R29C51C.FCI to    R29C51C.FCO signal_process/demodu/SLICE_180
ROUTE         1     0.000    R29C51C.FCO to    R29C51D.FCI signal_process/demodu/un3_sample_sum_cry_44
FCITOFCO_D  ---     0.063    R29C51D.FCI to    R29C51D.FCO signal_process/demodu/SLICE_181
ROUTE         1     0.000    R29C51D.FCO to    R29C52A.FCI signal_process/demodu/un3_sample_sum_cry_46
FCITOFCO_D  ---     0.063    R29C52A.FCI to    R29C52A.FCO signal_process/demodu/SLICE_182
ROUTE         1     0.000    R29C52A.FCO to    R29C52B.FCI signal_process/demodu/un3_sample_sum_cry_48
FCITOFCO_D  ---     0.063    R29C52B.FCI to    R29C52B.FCO signal_process/demodu/SLICE_183
ROUTE         1     0.000    R29C52B.FCO to    R29C52C.FCI signal_process/demodu/un3_sample_sum_cry_50
FCITOFCO_D  ---     0.063    R29C52C.FCI to    R29C52C.FCO signal_process/demodu/SLICE_184
ROUTE         1     0.000    R29C52C.FCO to    R29C52D.FCI signal_process/demodu/un3_sample_sum_cry_52
FCITOFCO_D  ---     0.063    R29C52D.FCI to    R29C52D.FCO signal_process/demodu/SLICE_185
ROUTE         1     0.000    R29C52D.FCO to    R29C53A.FCI signal_process/demodu/un3_sample_sum_cry_54
FCITOF0_DE  ---     0.385    R29C53A.FCI to     R29C53A.F0 signal_process/demodu/SLICE_186
ROUTE         1     0.000     R29C53A.F0 to    R29C53A.DI0 signal_process/demodu/un3_sample_sum_s_55_0_S0 (to clk_AD)
                  --------
                    6.146   (46.9% logic, 53.1% route), 29 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_290:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R44C12D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_186:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R29C53A.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 10.795ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/din_reg1[2]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[54]  (to clk_AD +)

   Delay:               6.110ns  (46.6% logic, 53.4% route), 28 logic levels.

 Constraint Details:

      6.110ns physical path delay signal_process/demodu/SLICE_290 to signal_process/demodu/SLICE_185 meets
     16.667ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 16.905ns) by 10.795ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_290 to signal_process/demodu/SLICE_185:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R44C12D.CLK to     R44C12D.Q0 signal_process/demodu/SLICE_290 (from clk_AD)
ROUTE         1     3.262     R44C12D.Q0 to     R29C46B.A1 signal_process/demodu/din_reg1[2]
C1TOFCO_DE  ---     0.401     R29C46B.A1 to    R29C46B.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R29C46B.FCO to    R29C46C.FCI signal_process/demodu/un3_sample_sum_cry_2
FCITOFCO_D  ---     0.063    R29C46C.FCI to    R29C46C.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R29C46C.FCO to    R29C46D.FCI signal_process/demodu/un3_sample_sum_cry_4
FCITOFCO_D  ---     0.063    R29C46D.FCI to    R29C46D.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R29C46D.FCO to    R29C47A.FCI signal_process/demodu/un3_sample_sum_cry_6
FCITOFCO_D  ---     0.063    R29C47A.FCI to    R29C47A.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R29C47A.FCO to    R29C47B.FCI signal_process/demodu/un3_sample_sum_cry_8
FCITOFCO_D  ---     0.063    R29C47B.FCI to    R29C47B.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R29C47B.FCO to    R29C47C.FCI signal_process/demodu/un3_sample_sum_cry_10
FCITOFCO_D  ---     0.063    R29C47C.FCI to    R29C47C.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R29C47C.FCO to    R29C47D.FCI signal_process/demodu/un3_sample_sum_cry_12
FCITOFCO_D  ---     0.063    R29C47D.FCI to    R29C47D.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R29C47D.FCO to    R29C48A.FCI signal_process/demodu/un3_sample_sum_cry_14
FCITOFCO_D  ---     0.063    R29C48A.FCI to    R29C48A.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R29C48A.FCO to    R29C48B.FCI signal_process/demodu/un3_sample_sum_cry_16
FCITOFCO_D  ---     0.063    R29C48B.FCI to    R29C48B.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R29C48B.FCO to    R29C48C.FCI signal_process/demodu/un3_sample_sum_cry_18
FCITOFCO_D  ---     0.063    R29C48C.FCI to    R29C48C.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R29C48C.FCO to    R29C48D.FCI signal_process/demodu/un3_sample_sum_cry_20
FCITOFCO_D  ---     0.063    R29C48D.FCI to    R29C48D.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R29C48D.FCO to    R29C49A.FCI signal_process/demodu/un3_sample_sum_cry_22
FCITOFCO_D  ---     0.063    R29C49A.FCI to    R29C49A.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R29C49A.FCO to    R29C49B.FCI signal_process/demodu/un3_sample_sum_cry_24
FCITOFCO_D  ---     0.063    R29C49B.FCI to    R29C49B.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R29C49B.FCO to    R29C49C.FCI signal_process/demodu/un3_sample_sum_cry_26
FCITOFCO_D  ---     0.063    R29C49C.FCI to    R29C49C.FCO signal_process/demodu/SLICE_172
ROUTE         1     0.000    R29C49C.FCO to    R29C49D.FCI signal_process/demodu/un3_sample_sum_cry_28
FCITOFCO_D  ---     0.063    R29C49D.FCI to    R29C49D.FCO signal_process/demodu/SLICE_173
ROUTE         1     0.000    R29C49D.FCO to    R29C50A.FCI signal_process/demodu/un3_sample_sum_cry_30
FCITOFCO_D  ---     0.063    R29C50A.FCI to    R29C50A.FCO signal_process/demodu/SLICE_174
ROUTE         1     0.000    R29C50A.FCO to    R29C50B.FCI signal_process/demodu/un3_sample_sum_cry_32
FCITOFCO_D  ---     0.063    R29C50B.FCI to    R29C50B.FCO signal_process/demodu/SLICE_175
ROUTE         1     0.000    R29C50B.FCO to    R29C50C.FCI signal_process/demodu/un3_sample_sum_cry_34
FCITOFCO_D  ---     0.063    R29C50C.FCI to    R29C50C.FCO signal_process/demodu/SLICE_176
ROUTE         1     0.000    R29C50C.FCO to    R29C50D.FCI signal_process/demodu/un3_sample_sum_cry_36
FCITOFCO_D  ---     0.063    R29C50D.FCI to    R29C50D.FCO signal_process/demodu/SLICE_177
ROUTE         1     0.000    R29C50D.FCO to    R29C51A.FCI signal_process/demodu/un3_sample_sum_cry_38
FCITOFCO_D  ---     0.063    R29C51A.FCI to    R29C51A.FCO signal_process/demodu/SLICE_178
ROUTE         1     0.000    R29C51A.FCO to    R29C51B.FCI signal_process/demodu/un3_sample_sum_cry_40
FCITOFCO_D  ---     0.063    R29C51B.FCI to    R29C51B.FCO signal_process/demodu/SLICE_179
ROUTE         1     0.000    R29C51B.FCO to    R29C51C.FCI signal_process/demodu/un3_sample_sum_cry_42
FCITOFCO_D  ---     0.063    R29C51C.FCI to    R29C51C.FCO signal_process/demodu/SLICE_180
ROUTE         1     0.000    R29C51C.FCO to    R29C51D.FCI signal_process/demodu/un3_sample_sum_cry_44
FCITOFCO_D  ---     0.063    R29C51D.FCI to    R29C51D.FCO signal_process/demodu/SLICE_181
ROUTE         1     0.000    R29C51D.FCO to    R29C52A.FCI signal_process/demodu/un3_sample_sum_cry_46
FCITOFCO_D  ---     0.063    R29C52A.FCI to    R29C52A.FCO signal_process/demodu/SLICE_182
ROUTE         1     0.000    R29C52A.FCO to    R29C52B.FCI signal_process/demodu/un3_sample_sum_cry_48
FCITOFCO_D  ---     0.063    R29C52B.FCI to    R29C52B.FCO signal_process/demodu/SLICE_183
ROUTE         1     0.000    R29C52B.FCO to    R29C52C.FCI signal_process/demodu/un3_sample_sum_cry_50
FCITOFCO_D  ---     0.063    R29C52C.FCI to    R29C52C.FCO signal_process/demodu/SLICE_184
ROUTE         1     0.000    R29C52C.FCO to    R29C52D.FCI signal_process/demodu/un3_sample_sum_cry_52
FCITOF1_DE  ---     0.412    R29C52D.FCI to     R29C52D.F1 signal_process/demodu/SLICE_185
ROUTE         1     0.000     R29C52D.F1 to    R29C52D.DI1 signal_process/demodu/un3_sample_sum_cry_53_0_S1 (to clk_AD)
                  --------
                    6.110   (46.6% logic, 53.4% route), 28 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_290:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R44C12D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_185:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R29C52D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 5.408ns (weighted slack = 10.816ns)

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/Read_en  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/demodu/fifo/FF_24  (to clk_AD +)

   Delay:               3.164ns  (48.8% logic, 51.2% route), 5 logic levels.

 Constraint Details:

      3.164ns physical path delay signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_105 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.572ns) by 5.408ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_286 to signal_process/demodu/fifo/SLICE_105:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R32C43D.CLK to     R32C43D.Q0 signal_process/demodu/SLICE_286 (from clk120mhz)
ROUTE         4     0.681     R32C43D.Q0 to     R32C43B.A1 signal_process/demodu/Read_en
CTOF_DEL    ---     0.208     R32C43B.A1 to     R32C43B.F1 signal_process/demodu/fifo/SLICE_619
ROUTE        10     0.939     R32C43B.F1 to     R32C42A.A1 signal_process/demodu/fifo/cnt_con
C1TOFCO_DE  ---     0.401     R32C42A.A1 to    R32C42A.FCO signal_process/demodu/fifo/SLICE_103
ROUTE         1     0.000    R32C42A.FCO to    R32C42B.FCI signal_process/demodu/fifo/bdcnt_bctr_ci
FCITOFCO_D  ---     0.063    R32C42B.FCI to    R32C42B.FCO signal_process/demodu/fifo/SLICE_104
ROUTE         1     0.000    R32C42B.FCO to    R32C42C.FCI signal_process/demodu/fifo/co0
FCITOF1_DE  ---     0.412    R32C42C.FCI to     R32C42C.F1 signal_process/demodu/fifo/SLICE_105
ROUTE         1     0.000     R32C42C.F1 to    R32C42C.DI1 signal_process/demodu/fifo/ifcount_3 (to clk_AD)
                  --------
                    3.164   (48.8% logic, 51.2% route), 5 logic levels.

 Clock Skew Details: 

      Source Clock Path clk_in to signal_process/demodu/SLICE_286:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.890  PLL_BR0.CLKOP to    R32C43D.CLK clk120mhz
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      Destination Clock Path clk_in to signal_process/demodu/fifo/SLICE_105:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI clk_in
ROUTE         1     0.350      L16.PADDI to   PLL_BR0.CLKI clk_in_c
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS CLK120/PLLInst_0
ROUTE       101     1.890  PLL_BR0.CLKOS to    R32C42C.CLK clk_AD
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP CLK120/PLLInst_0
ROUTE       444     1.979  PLL_BR0.CLKOP to  PLL_BR0.CLKFB clk120mhz
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.


Passed: The following path meets requirements by 10.819ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/din_reg1[2]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[53]  (to clk_AD +)

   Delay:               6.083ns  (46.4% logic, 53.6% route), 28 logic levels.

 Constraint Details:

      6.083ns physical path delay signal_process/demodu/SLICE_290 to signal_process/demodu/SLICE_185 meets
     16.667ns delay constraint less
      0.000ns skew and
     -0.235ns DIN_SET requirement (totaling 16.902ns) by 10.819ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_290 to signal_process/demodu/SLICE_185:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R44C12D.CLK to     R44C12D.Q0 signal_process/demodu/SLICE_290 (from clk_AD)
ROUTE         1     3.262     R44C12D.Q0 to     R29C46B.A1 signal_process/demodu/din_reg1[2]
C1TOFCO_DE  ---     0.401     R29C46B.A1 to    R29C46B.FCO signal_process/demodu/SLICE_159
ROUTE         1     0.000    R29C46B.FCO to    R29C46C.FCI signal_process/demodu/un3_sample_sum_cry_2
FCITOFCO_D  ---     0.063    R29C46C.FCI to    R29C46C.FCO signal_process/demodu/SLICE_160
ROUTE         1     0.000    R29C46C.FCO to    R29C46D.FCI signal_process/demodu/un3_sample_sum_cry_4
FCITOFCO_D  ---     0.063    R29C46D.FCI to    R29C46D.FCO signal_process/demodu/SLICE_161
ROUTE         1     0.000    R29C46D.FCO to    R29C47A.FCI signal_process/demodu/un3_sample_sum_cry_6
FCITOFCO_D  ---     0.063    R29C47A.FCI to    R29C47A.FCO signal_process/demodu/SLICE_162
ROUTE         1     0.000    R29C47A.FCO to    R29C47B.FCI signal_process/demodu/un3_sample_sum_cry_8
FCITOFCO_D  ---     0.063    R29C47B.FCI to    R29C47B.FCO signal_process/demodu/SLICE_163
ROUTE         1     0.000    R29C47B.FCO to    R29C47C.FCI signal_process/demodu/un3_sample_sum_cry_10
FCITOFCO_D  ---     0.063    R29C47C.FCI to    R29C47C.FCO signal_process/demodu/SLICE_164
ROUTE         1     0.000    R29C47C.FCO to    R29C47D.FCI signal_process/demodu/un3_sample_sum_cry_12
FCITOFCO_D  ---     0.063    R29C47D.FCI to    R29C47D.FCO signal_process/demodu/SLICE_165
ROUTE         1     0.000    R29C47D.FCO to    R29C48A.FCI signal_process/demodu/un3_sample_sum_cry_14
FCITOFCO_D  ---     0.063    R29C48A.FCI to    R29C48A.FCO signal_process/demodu/SLICE_166
ROUTE         1     0.000    R29C48A.FCO to    R29C48B.FCI signal_process/demodu/un3_sample_sum_cry_16
FCITOFCO_D  ---     0.063    R29C48B.FCI to    R29C48B.FCO signal_process/demodu/SLICE_167
ROUTE         1     0.000    R29C48B.FCO to    R29C48C.FCI signal_process/demodu/un3_sample_sum_cry_18
FCITOFCO_D  ---     0.063    R29C48C.FCI to    R29C48C.FCO signal_process/demodu/SLICE_168
ROUTE         1     0.000    R29C48C.FCO to    R29C48D.FCI signal_process/demodu/un3_sample_sum_cry_20
FCITOFCO_D  ---     0.063    R29C48D.FCI to    R29C48D.FCO signal_process/demodu/SLICE_169
ROUTE         1     0.000    R29C48D.FCO to    R29C49A.FCI signal_process/demodu/un3_sample_sum_cry_22
FCITOFCO_D  ---     0.063    R29C49A.FCI to    R29C49A.FCO signal_process/demodu/SLICE_170
ROUTE         1     0.000    R29C49A.FCO to    R29C49B.FCI signal_process/demodu/un3_sample_sum_cry_24
FCITOFCO_D  ---     0.063    R29C49B.FCI to    R29C49B.FCO signal_process/demodu/SLICE_171
ROUTE         1     0.000    R29C49B.FCO to    R29C49C.FCI signal_process/demodu/un3_sample_sum_cry_26
FCITOFCO_D  ---     0.063    R29C49C.FCI to    R29C49C.FCO signal_process/demodu/SLICE_172
ROUTE         1     0.000    R29C49C.FCO to    R29C49D.FCI signal_process/demodu/un3_sample_sum_cry_28
FCITOFCO_D  ---     0.063    R29C49D.FCI to    R29C49D.FCO signal_process/demodu/SLICE_173
ROUTE         1     0.000    R29C49D.FCO to    R29C50A.FCI signal_process/demodu/un3_sample_sum_cry_30
FCITOFCO_D  ---     0.063    R29C50A.FCI to    R29C50A.FCO signal_process/demodu/SLICE_174
ROUTE         1     0.000    R29C50A.FCO to    R29C50B.FCI signal_process/demodu/un3_sample_sum_cry_32
FCITOFCO_D  ---     0.063    R29C50B.FCI to    R29C50B.FCO signal_process/demodu/SLICE_175
ROUTE         1     0.000    R29C50B.FCO to    R29C50C.FCI signal_process/demodu/un3_sample_sum_cry_34
FCITOFCO_D  ---     0.063    R29C50C.FCI to    R29C50C.FCO signal_process/demodu/SLICE_176
ROUTE         1     0.000    R29C50C.FCO to    R29C50D.FCI signal_process/demodu/un3_sample_sum_cry_36
FCITOFCO_D  ---     0.063    R29C50D.FCI to    R29C50D.FCO signal_process/demodu/SLICE_177
ROUTE         1     0.000    R29C50D.FCO to    R29C51A.FCI signal_process/demodu/un3_sample_sum_cry_38
FCITOFCO_D  ---     0.063    R29C51A.FCI to    R29C51A.FCO signal_process/demodu/SLICE_178
ROUTE         1     0.000    R29C51A.FCO to    R29C51B.FCI signal_process/demodu/un3_sample_sum_cry_40
FCITOFCO_D  ---     0.063    R29C51B.FCI to    R29C51B.FCO signal_process/demodu/SLICE_179
ROUTE         1     0.000    R29C51B.FCO to    R29C51C.FCI signal_process/demodu/un3_sample_sum_cry_42
FCITOFCO_D  ---     0.063    R29C51C.FCI to    R29C51C.FCO signal_process/demodu/SLICE_180
ROUTE         1     0.000    R29C51C.FCO to    R29C51D.FCI signal_process/demodu/un3_sample_sum_cry_44
FCITOFCO_D  ---     0.063    R29C51D.FCI to    R29C51D.FCO signal_process/demodu/SLICE_181
ROUTE         1     0.000    R29C51D.FCO to    R29C52A.FCI signal_process/demodu/un3_sample_sum_cry_46
FCITOFCO_D  ---     0.063    R29C52A.FCI to    R29C52A.FCO signal_process/demodu/SLICE_182
ROUTE         1     0.000    R29C52A.FCO to    R29C52B.FCI signal_process/demodu/un3_sample_sum_cry_48
FCITOFCO_D  ---     0.063    R29C52B.FCI to    R29C52B.FCO signal_process/demodu/SLICE_183
ROUTE         1     0.000    R29C52B.FCO to    R29C52C.FCI signal_process/demodu/un3_sample_sum_cry_50
FCITOFCO_D  ---     0.063    R29C52C.FCI to    R29C52C.FCO signal_process/demodu/SLICE_184
ROUTE         1     0.000    R29C52C.FCO to    R29C52D.FCI signal_process/demodu/un3_sample_sum_cry_52
FCITOF0_DE  ---     0.385    R29C52D.FCI to     R29C52D.F0 signal_process/demodu/SLICE_185
ROUTE         1     0.000     R29C52D.F0 to    R29C52D.DI0 signal_process/demodu/un3_sample_sum_cry_53_0_S0 (to clk_AD)
                  --------
                    6.083   (46.4% logic, 53.6% route), 28 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_290:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R44C12D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_185:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     1.890  PLL_BR0.CLKOS to    R29C52D.CLK clk_AD
                  --------
                    1.890   (0.0% logic, 100.0% route), 0 logic levels.

Report:  155.666MHz is the maximum frequency for this preference.


================================================================================
<A name="par_twr_pref_0_4"></A>Preference: FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
<A name="par_twr_pref_0_5"></A>Preference: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
            1596 items scored.
--------------------------------------------------------------------------------


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[54]  (to clk120mhz +)

   Delay:               8.571ns  (86.7% logic, 13.3% route), 29 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2     1.141 *R_R25C48.DO18 to     R23C49A.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401     R23C49A.A1 to    R23C49A.FCO signal_process/demodu/SLICE_130
ROUTE         1     0.000    R23C49A.FCO to    R23C49B.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063    R23C49B.FCI to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R23C54D.FCI to    R23C54D.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R23C54D.FCO to    R23C55A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R23C55A.FCI to    R23C55A.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R23C55A.FCO to    R23C55B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R23C55B.FCI to    R23C55B.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R23C55B.FCO to    R23C55C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOFCO_D  ---     0.063    R23C55C.FCI to    R23C55C.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R23C55C.FCO to    R23C55D.FCI signal_process/demodu/INS_dout_1_cry_52
FCITOF1_DE  ---     0.412    R23C55D.FCI to     R23C55D.F1 signal_process/demodu/SLICE_157
ROUTE         1     0.000     R23C55D.F1 to    R23C55D.DI1 signal_process/demodu/INS_dout_1[54] (to clk120mhz)
                  --------
                    8.571   (86.7% logic, 13.3% route), 29 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[53]  (to clk120mhz +)

   Delay:               8.544ns  (86.6% logic, 13.4% route), 29 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2     1.141 *R_R25C48.DO18 to     R23C49A.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401     R23C49A.A1 to    R23C49A.FCO signal_process/demodu/SLICE_130
ROUTE         1     0.000    R23C49A.FCO to    R23C49B.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063    R23C49B.FCI to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R23C54D.FCI to    R23C54D.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R23C54D.FCO to    R23C55A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R23C55A.FCI to    R23C55A.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R23C55A.FCO to    R23C55B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R23C55B.FCI to    R23C55B.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R23C55B.FCO to    R23C55C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOFCO_D  ---     0.063    R23C55C.FCI to    R23C55C.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R23C55C.FCO to    R23C55D.FCI signal_process/demodu/INS_dout_1_cry_52
FCITOF0_DE  ---     0.385    R23C55D.FCI to     R23C55D.F0 signal_process/demodu/SLICE_157
ROUTE         1     0.000     R23C55D.F0 to    R23C55D.DI0 signal_process/demodu/INS_dout_1[53] (to clk120mhz)
                  --------
                    8.544   (86.6% logic, 13.4% route), 29 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[52]  (to clk120mhz +)

   Delay:               8.508ns  (86.6% logic, 13.4% route), 28 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2     1.141 *R_R25C48.DO18 to     R23C49A.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401     R23C49A.A1 to    R23C49A.FCO signal_process/demodu/SLICE_130
ROUTE         1     0.000    R23C49A.FCO to    R23C49B.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063    R23C49B.FCI to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R23C54D.FCI to    R23C54D.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R23C54D.FCO to    R23C55A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R23C55A.FCI to    R23C55A.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R23C55A.FCO to    R23C55B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R23C55B.FCI to    R23C55B.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R23C55B.FCO to    R23C55C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOF1_DE  ---     0.412    R23C55C.FCI to     R23C55C.F1 signal_process/demodu/SLICE_156
ROUTE         1     0.000     R23C55C.F1 to    R23C55C.DI1 signal_process/demodu/INS_dout_1[52] (to clk120mhz)
                  --------
                    8.508   (86.6% logic, 13.4% route), 28 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[51]  (to clk120mhz +)

   Delay:               8.481ns  (86.5% logic, 13.5% route), 28 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2     1.141 *R_R25C48.DO18 to     R23C49A.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401     R23C49A.A1 to    R23C49A.FCO signal_process/demodu/SLICE_130
ROUTE         1     0.000    R23C49A.FCO to    R23C49B.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063    R23C49B.FCI to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R23C54D.FCI to    R23C54D.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R23C54D.FCO to    R23C55A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R23C55A.FCI to    R23C55A.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R23C55A.FCO to    R23C55B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R23C55B.FCI to    R23C55B.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R23C55B.FCO to    R23C55C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOF0_DE  ---     0.385    R23C55C.FCI to     R23C55C.F0 signal_process/demodu/SLICE_156
ROUTE         1     0.000     R23C55C.F0 to    R23C55C.DI0 signal_process/demodu/INS_dout_1[51] (to clk120mhz)
                  --------
                    8.481   (86.5% logic, 13.5% route), 28 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[50]  (to clk120mhz +)

   Delay:               8.445ns  (86.5% logic, 13.5% route), 27 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2     1.141 *R_R25C48.DO18 to     R23C49A.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401     R23C49A.A1 to    R23C49A.FCO signal_process/demodu/SLICE_130
ROUTE         1     0.000    R23C49A.FCO to    R23C49B.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063    R23C49B.FCI to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R23C54D.FCI to    R23C54D.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R23C54D.FCO to    R23C55A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R23C55A.FCI to    R23C55A.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R23C55A.FCO to    R23C55B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOF1_DE  ---     0.412    R23C55B.FCI to     R23C55B.F1 signal_process/demodu/SLICE_155
ROUTE         1     0.000     R23C55B.F1 to    R23C55B.DI1 signal_process/demodu/INS_dout_1[50] (to clk120mhz)
                  --------
                    8.445   (86.5% logic, 13.5% route), 27 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[49]  (to clk120mhz +)

   Delay:               8.418ns  (86.4% logic, 13.6% route), 27 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2     1.141 *R_R25C48.DO18 to     R23C49A.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401     R23C49A.A1 to    R23C49A.FCO signal_process/demodu/SLICE_130
ROUTE         1     0.000    R23C49A.FCO to    R23C49B.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063    R23C49B.FCI to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R23C54D.FCI to    R23C54D.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R23C54D.FCO to    R23C55A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R23C55A.FCI to    R23C55A.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R23C55A.FCO to    R23C55B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOF0_DE  ---     0.385    R23C55B.FCI to     R23C55B.F0 signal_process/demodu/SLICE_155
ROUTE         1     0.000     R23C55B.F0 to    R23C55B.DI0 signal_process/demodu/INS_dout_1[49] (to clk120mhz)
                  --------
                    8.418   (86.4% logic, 13.6% route), 27 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[48]  (to clk120mhz +)

   Delay:               8.382ns  (86.4% logic, 13.6% route), 26 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2     1.141 *R_R25C48.DO18 to     R23C49A.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401     R23C49A.A1 to    R23C49A.FCO signal_process/demodu/SLICE_130
ROUTE         1     0.000    R23C49A.FCO to    R23C49B.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063    R23C49B.FCI to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R23C54D.FCI to    R23C54D.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R23C54D.FCO to    R23C55A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOF1_DE  ---     0.412    R23C55A.FCI to     R23C55A.F1 signal_process/demodu/SLICE_154
ROUTE         1     0.000     R23C55A.F1 to    R23C55A.DI1 signal_process/demodu/INS_dout_1[48] (to clk120mhz)
                  --------
                    8.382   (86.4% logic, 13.6% route), 26 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[47]  (to clk120mhz +)

   Delay:               8.355ns  (86.3% logic, 13.7% route), 26 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2     1.141 *R_R25C48.DO18 to     R23C49A.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401     R23C49A.A1 to    R23C49A.FCO signal_process/demodu/SLICE_130
ROUTE         1     0.000    R23C49A.FCO to    R23C49B.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063    R23C49B.FCI to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R23C54D.FCI to    R23C54D.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R23C54D.FCO to    R23C55A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOF0_DE  ---     0.385    R23C55A.FCI to     R23C55A.F0 signal_process/demodu/SLICE_154
ROUTE         1     0.000     R23C55A.F0 to    R23C55A.DI0 signal_process/demodu/INS_dout_1[47] (to clk120mhz)
                  --------
                    8.355   (86.3% logic, 13.7% route), 26 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[54]  (to clk120mhz +)

   Delay:               8.336ns  (88.4% logic, 11.6% route), 28 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO19 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         1     0.969 *R_R25C48.DO19 to     R23C49B.A0 signal_process/demodu/sample_sum_DY[1]
C0TOFCO_DE  ---     0.401     R23C49B.A0 to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOFCO_D  ---     0.063    R23C54D.FCI to    R23C54D.FCO signal_process/demodu/SLICE_153
ROUTE         1     0.000    R23C54D.FCO to    R23C55A.FCI signal_process/demodu/INS_dout_1_cry_46
FCITOFCO_D  ---     0.063    R23C55A.FCI to    R23C55A.FCO signal_process/demodu/SLICE_154
ROUTE         1     0.000    R23C55A.FCO to    R23C55B.FCI signal_process/demodu/INS_dout_1_cry_48
FCITOFCO_D  ---     0.063    R23C55B.FCI to    R23C55B.FCO signal_process/demodu/SLICE_155
ROUTE         1     0.000    R23C55B.FCO to    R23C55C.FCI signal_process/demodu/INS_dout_1_cry_50
FCITOFCO_D  ---     0.063    R23C55C.FCI to    R23C55C.FCO signal_process/demodu/SLICE_156
ROUTE         1     0.000    R23C55C.FCO to    R23C55D.FCI signal_process/demodu/INS_dout_1_cry_52
FCITOF1_DE  ---     0.412    R23C55D.FCI to     R23C55D.F1 signal_process/demodu/SLICE_157
ROUTE         1     0.000     R23C55D.F1 to    R23C55D.DI1 signal_process/demodu/INS_dout_1[54] (to clk120mhz)
                  --------
                    8.336   (88.4% logic, 11.6% route), 28 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/INS_dout[46]  (to clk120mhz +)

   Delay:               8.319ns  (86.3% logic, 13.7% route), 25 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 *R_R25C48.CLKR to *R_R25C48.DO18 signal_process/demodu/fifo/pdp_ram_0_0_1 (from clk_AD)
ROUTE         2     1.141 *R_R25C48.DO18 to     R23C49A.A1 signal_process/demodu/INS_dout_1
C1TOFCO_DE  ---     0.401     R23C49A.A1 to    R23C49A.FCO signal_process/demodu/SLICE_130
ROUTE         1     0.000    R23C49A.FCO to    R23C49B.FCI signal_process/demodu/INS_dout_1_cry_0
FCITOFCO_D  ---     0.063    R23C49B.FCI to    R23C49B.FCO signal_process/demodu/SLICE_131
ROUTE         1     0.000    R23C49B.FCO to    R23C49C.FCI signal_process/demodu/INS_dout_1_cry_2
FCITOFCO_D  ---     0.063    R23C49C.FCI to    R23C49C.FCO signal_process/demodu/SLICE_132
ROUTE         1     0.000    R23C49C.FCO to    R23C49D.FCI signal_process/demodu/INS_dout_1_cry_4
FCITOFCO_D  ---     0.063    R23C49D.FCI to    R23C49D.FCO signal_process/demodu/SLICE_133
ROUTE         1     0.000    R23C49D.FCO to    R23C50A.FCI signal_process/demodu/INS_dout_1_cry_6
FCITOFCO_D  ---     0.063    R23C50A.FCI to    R23C50A.FCO signal_process/demodu/SLICE_134
ROUTE         1     0.000    R23C50A.FCO to    R23C50B.FCI signal_process/demodu/INS_dout_1_cry_8
FCITOFCO_D  ---     0.063    R23C50B.FCI to    R23C50B.FCO signal_process/demodu/SLICE_135
ROUTE         1     0.000    R23C50B.FCO to    R23C50C.FCI signal_process/demodu/INS_dout_1_cry_10
FCITOFCO_D  ---     0.063    R23C50C.FCI to    R23C50C.FCO signal_process/demodu/SLICE_136
ROUTE         1     0.000    R23C50C.FCO to    R23C50D.FCI signal_process/demodu/INS_dout_1_cry_12
FCITOFCO_D  ---     0.063    R23C50D.FCI to    R23C50D.FCO signal_process/demodu/SLICE_137
ROUTE         1     0.000    R23C50D.FCO to    R23C51A.FCI signal_process/demodu/INS_dout_1_cry_14
FCITOFCO_D  ---     0.063    R23C51A.FCI to    R23C51A.FCO signal_process/demodu/SLICE_138
ROUTE         1     0.000    R23C51A.FCO to    R23C51B.FCI signal_process/demodu/INS_dout_1_cry_16
FCITOFCO_D  ---     0.063    R23C51B.FCI to    R23C51B.FCO signal_process/demodu/SLICE_139
ROUTE         1     0.000    R23C51B.FCO to    R23C51C.FCI signal_process/demodu/INS_dout_1_cry_18
FCITOFCO_D  ---     0.063    R23C51C.FCI to    R23C51C.FCO signal_process/demodu/SLICE_140
ROUTE         1     0.000    R23C51C.FCO to    R23C51D.FCI signal_process/demodu/INS_dout_1_cry_20
FCITOFCO_D  ---     0.063    R23C51D.FCI to    R23C51D.FCO signal_process/demodu/SLICE_141
ROUTE         1     0.000    R23C51D.FCO to    R23C52A.FCI signal_process/demodu/INS_dout_1_cry_22
FCITOFCO_D  ---     0.063    R23C52A.FCI to    R23C52A.FCO signal_process/demodu/SLICE_142
ROUTE         1     0.000    R23C52A.FCO to    R23C52B.FCI signal_process/demodu/INS_dout_1_cry_24
FCITOFCO_D  ---     0.063    R23C52B.FCI to    R23C52B.FCO signal_process/demodu/SLICE_143
ROUTE         1     0.000    R23C52B.FCO to    R23C52C.FCI signal_process/demodu/INS_dout_1_cry_26
FCITOFCO_D  ---     0.063    R23C52C.FCI to    R23C52C.FCO signal_process/demodu/SLICE_144
ROUTE         1     0.000    R23C52C.FCO to    R23C52D.FCI signal_process/demodu/INS_dout_1_cry_28
FCITOFCO_D  ---     0.063    R23C52D.FCI to    R23C52D.FCO signal_process/demodu/SLICE_145
ROUTE         1     0.000    R23C52D.FCO to    R23C53A.FCI signal_process/demodu/INS_dout_1_cry_30
FCITOFCO_D  ---     0.063    R23C53A.FCI to    R23C53A.FCO signal_process/demodu/SLICE_146
ROUTE         1     0.000    R23C53A.FCO to    R23C53B.FCI signal_process/demodu/INS_dout_1_cry_32
FCITOFCO_D  ---     0.063    R23C53B.FCI to    R23C53B.FCO signal_process/demodu/SLICE_147
ROUTE         1     0.000    R23C53B.FCO to    R23C53C.FCI signal_process/demodu/INS_dout_1_cry_34
FCITOFCO_D  ---     0.063    R23C53C.FCI to    R23C53C.FCO signal_process/demodu/SLICE_148
ROUTE         1     0.000    R23C53C.FCO to    R23C53D.FCI signal_process/demodu/INS_dout_1_cry_36
FCITOFCO_D  ---     0.063    R23C53D.FCI to    R23C53D.FCO signal_process/demodu/SLICE_149
ROUTE         1     0.000    R23C53D.FCO to    R23C54A.FCI signal_process/demodu/INS_dout_1_cry_38
FCITOFCO_D  ---     0.063    R23C54A.FCI to    R23C54A.FCO signal_process/demodu/SLICE_150
ROUTE         1     0.000    R23C54A.FCO to    R23C54B.FCI signal_process/demodu/INS_dout_1_cry_40
FCITOFCO_D  ---     0.063    R23C54B.FCI to    R23C54B.FCO signal_process/demodu/SLICE_151
ROUTE         1     0.000    R23C54B.FCO to    R23C54C.FCI signal_process/demodu/INS_dout_1_cry_42
FCITOFCO_D  ---     0.063    R23C54C.FCI to    R23C54C.FCO signal_process/demodu/SLICE_152
ROUTE         1     0.000    R23C54C.FCO to    R23C54D.FCI signal_process/demodu/INS_dout_1_cry_44
FCITOF1_DE  ---     0.412    R23C54D.FCI to     R23C54D.F1 signal_process/demodu/SLICE_153
ROUTE         1     0.000     R23C54D.F1 to    R23C54D.DI1 signal_process/demodu/INS_dout_1[46] (to clk120mhz)
                  --------
                    8.319   (86.3% logic, 13.7% route), 25 logic levels.

<A name="ptwr_set_rs"></A><B><U><big>Report Summary</big></U></B>
--------------
----------------------------------------------------------------------------
Preference                              |   Constraint|       Actual|Levels
----------------------------------------------------------------------------
                                        |             |             |
FREQUENCY NET "clk120mhz" 120.000000    |             |             |
MHz ;                                   |  120.000 MHz|  140.726 MHz|  11  
                                        |             |             |
FREQUENCY NET "wendu.clk_us" 1.000000   |             |             |
MHz ;                                   |    1.000 MHz|  109.051 MHz|  16  
                                        |             |             |
FREQUENCY NET "clk_in_c" 20.000000 MHz  |             |             |
;                                       |            -|            -|   0  
                                        |             |             |
FREQUENCY NET "clk_AD" 60.000000 MHz ;  |   60.000 MHz|  155.666 MHz|   8  
                                        |             |             |
FREQUENCY NET "clk_AD_t_c" 60.000000    |             |             |
MHz ;                                   |            -|            -|   0  
                                        |             |             |
----------------------------------------------------------------------------


All preferences were met.


<A name="ptwr_set_clkda"></A><B><U><big>Clock Domains Analysis</big></U></B>
------------------------

Found 4 clocks:

Clock Domain: wendu.clk_us   Source: wendu/SLICE_224.Q0   Loads: 37
   Covered under: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;

Clock Domain: clk_in_c   Source: clk_in.PAD   Loads: 1
   No transfer within this clock domain is found

Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS   Loads: 101
   Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;

   Data transfers from:
   Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP
      Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;   Transfers: 2

Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP   Loads: 444
   Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;

   Data transfers from:
   Clock Domain: wendu.clk_us   Source: wendu/SLICE_224.Q0
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 16

   Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;


<A name="ptwr_set_ts"></A><B><U><big>Timing summary (Setup):</big></U></B>
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38135 paths, 6 nets, and 4122 connections (99.21% coverage)

--------------------------------------------------------------------------------
<A name="Par_Twr_hold"></A><B><U><big>Lattice TRACE Report - Hold, Version Diamond (64-bit) 3.12.1.454</big></U></B>
Tue Jun 24 11:16:11 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

<A name="ptwr_hold_ri"></A><B><U><big>Report Information</big></U></B>
------------------
Command line:    trce -v 10 -gt -sethld -sp 7 -sphld m -o INS350_5J_JZ_impl1.twr -gui -msgset D:/Project/INS350_5J_JZ _V2/promote.xml INS350_5J_JZ_impl1.ncd INS350_5J_JZ_impl1.prf 
Design file:     ins350_5j_jz_impl1.ncd
Preference file: ins350_5j_jz_impl1.prf
Device,speed:    LFE5U-25F,m
Report level:    verbose report, limited to 10 items per preference
--------------------------------------------------------------------------------

<A name="ptwr_hold_ps"></A><B><U><big>Preference Summary</big></U></B>

<LI><A href='#par_twr_pref_1_0' Target='right'>FREQUENCY NET "clk120mhz" 120.000000 MHz (0 errors)</A></LI>            4096 items scored, 0 timing errors detected.

<LI><A href='#par_twr_pref_1_1' Target='right'>FREQUENCY NET "wendu.clk_us" 1.000000 MHz (0 errors)</A></LI>            3495 items scored, 0 timing errors detected.

<LI><A href='#par_twr_pref_1_2' Target='right'>FREQUENCY NET "clk_in_c" 20.000000 MHz (0 errors)</A></LI>            0 items scored, 0 timing errors detected.

<LI><A href='#par_twr_pref_1_3' Target='right'>FREQUENCY NET "clk_AD" 60.000000 MHz (0 errors)</A></LI>            3075 items scored, 0 timing errors detected.

<LI><A href='#par_twr_pref_1_4' Target='right'>FREQUENCY NET "clk_AD_t_c" 60.000000 MHz (0 errors)</A></LI>            0 items scored, 0 timing errors detected.

<LI><A href='#par_twr_pref_1_5' Target='right'>BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" (0 errors)</A></LI>            1596 items scored.

BLOCK ASYNCPATHS
BLOCK RESETPATHS
BLOCK JTAG PATHS
--------------------------------------------------------------------------------


Derating parameters
-------------------
VCCIO Voltage:
                   3.300 V (Bank 0)
                   3.300 V (Bank 1, defined by PAR)
                   3.300 V (Bank 2, defined by PAR)
                   3.300 V (Bank 3, defined by PAR)
                   3.300 V (Bank 6, defined by PAR)
                   3.300 V (Bank 7)



================================================================================
<A name="par_twr_pref_1_0"></A>Preference: FREQUENCY NET "clk120mhz" 120.000000 MHz ;
            4096 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 0.175ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/rs422/p_sum[36]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/rs422/p_sum_dy[36]  (to clk120mhz +)

   Delay:               0.293ns  (56.0% logic, 44.0% route), 1 logic levels.

 Constraint Details:

      0.293ns physical path delay signal_process/rs422/SLICE_458 to signal_process/rs422/SLICE_466 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.175ns

 Physical Path Details:

      Data path signal_process/rs422/SLICE_458 to signal_process/rs422/SLICE_466:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R15C52A.CLK to     R15C52A.Q0 signal_process/rs422/SLICE_458 (from clk120mhz)
ROUTE         1     0.129     R15C52A.Q0 to     R15C52C.M0 signal_process/rs422/p_sum[36] (to clk120mhz)
                  --------
                    0.293   (56.0% logic, 44.0% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_458:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R15C52A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/rs422/SLICE_466:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R15C52C.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.175ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/modu/mudu_dy[2]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/modu/mudu_dy[3]  (to clk120mhz +)

   Delay:               0.293ns  (56.0% logic, 44.0% route), 1 logic levels.

 Constraint Details:

      0.293ns physical path delay signal_process/modu/SLICE_388 to signal_process/modu/SLICE_388 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.175ns

 Physical Path Details:

      Data path signal_process/modu/SLICE_388 to signal_process/modu/SLICE_388:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R21C49A.CLK to     R21C49A.Q0 signal_process/modu/SLICE_388 (from clk120mhz)
ROUTE         1     0.129     R21C49A.Q0 to     R21C49A.M1 signal_process/modu/mudu_dy[2] (to clk120mhz)
                  --------
                    0.293   (56.0% logic, 44.0% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_388:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R21C49A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_388:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R21C49A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/modu/mudu_dy[5]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/modu/mudu_dy[6]  (to clk120mhz +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay signal_process/modu/SLICE_389 to signal_process/modu/SLICE_638 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path signal_process/modu/SLICE_389 to signal_process/modu/SLICE_638:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R21C60B.CLK to     R21C60B.Q1 signal_process/modu/SLICE_389 (from clk120mhz)
ROUTE         2     0.131     R21C60B.Q1 to     R21C60C.M0 signal_process/modu/mudu_dy[5] (to clk120mhz)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_389:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R21C60B.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_638:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R21C60C.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.177ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/integ/inte_dy[0]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/integ/inte_dy[1]  (to clk120mhz +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay signal_process/integ/SLICE_384 to signal_process/integ/SLICE_384 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      Data path signal_process/integ/SLICE_384 to signal_process/integ/SLICE_384:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R24C46D.CLK to     R24C46D.Q0 signal_process/integ/SLICE_384 (from clk120mhz)
ROUTE         2     0.131     R24C46D.Q0 to     R24C46D.M1 signal_process/integ/inte_dy[0] (to clk120mhz)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/integ/SLICE_384:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R24C46D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/integ/SLICE_384:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R24C46D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.177ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/transmitdy[0]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U2/transmitdy[1]  (to clk120mhz +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay u_uart/U2/SLICE_536 to u_uart/U2/SLICE_536 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_536 to u_uart/U2/SLICE_536:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R21C44B.CLK to     R21C44B.Q0 u_uart/U2/SLICE_536 (from clk120mhz)
ROUTE         2     0.131     R21C44B.Q0 to     R21C44B.M1 u_uart/U2/transmitdy[0] (to clk120mhz)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_536:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R21C44B.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_536:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R21C44B.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.178ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/integ/DA_dout[0]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/integ/DA_dout[0]  (to clk120mhz +)

   Delay:               0.297ns  (80.8% logic, 19.2% route), 2 logic levels.

 Constraint Details:

      0.297ns physical path delay signal_process/integ/SLICE_383 to signal_process/integ/SLICE_383 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.178ns

 Physical Path Details:

      Data path signal_process/integ/SLICE_383 to signal_process/integ/SLICE_383:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R20C46A.CLK to     R20C46A.Q0 signal_process/integ/SLICE_383 (from clk120mhz)
ROUTE         3     0.057     R20C46A.Q0 to     R20C46A.D0 signal_process/integ/DA_dout[0]
CTOF_DEL    ---     0.076     R20C46A.D0 to     R20C46A.F0 signal_process/integ/SLICE_383
ROUTE         1     0.000     R20C46A.F0 to    R20C46A.DI0 signal_process/integ/DA_dout_1[0] (to clk120mhz)
                  --------
                    0.297   (80.8% logic, 19.2% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/integ/SLICE_383:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R20C46A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/integ/SLICE_383:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R20C46A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.178ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/tx_state[8]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U2/tx_state[9]  (to clk120mhz +)

   Delay:               0.296ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.296ns physical path delay u_uart/U2/SLICE_548 to u_uart/U2/SLICE_548 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.178ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_548 to u_uart/U2/SLICE_548:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R21C39D.CLK to     R21C39D.Q0 u_uart/U2/SLICE_548 (from clk120mhz)
ROUTE        10     0.132     R21C39D.Q0 to     R21C39D.M1 u_uart/U2/tx_state[8] (to clk120mhz)
                  --------
                    0.296   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_548:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R21C39D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_548:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R21C39D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.178ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/tx_state[0]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U2/tx_state[0]  (to clk120mhz +)

   Delay:               0.297ns  (80.8% logic, 19.2% route), 2 logic levels.

 Constraint Details:

      0.297ns physical path delay u_uart/U2/SLICE_544 to u_uart/U2/SLICE_544 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.178ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_544 to u_uart/U2/SLICE_544:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R23C39A.CLK to     R23C39A.Q0 u_uart/U2/SLICE_544 (from clk120mhz)
ROUTE         2     0.057     R23C39A.Q0 to     R23C39A.D0 u_uart/U2/tx_state[0]
CTOF_DEL    ---     0.076     R23C39A.D0 to     R23C39A.F0 u_uart/U2/SLICE_544
ROUTE         1     0.000     R23C39A.F0 to    R23C39A.DI0 u_uart/U2/tx_state_ns[0] (to clk120mhz)
                  --------
                    0.297   (80.8% logic, 19.2% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_544:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R23C39A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_544:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R23C39A.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.178ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/modu/mudu_dy[0]  (from clk120mhz +)
   Destination:    FF         Data in        signal_process/modu/mudu_dy[1]  (to clk120mhz +)

   Delay:               0.296ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.296ns physical path delay signal_process/modu/SLICE_402 to signal_process/modu/SLICE_402 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.178ns

 Physical Path Details:

      Data path signal_process/modu/SLICE_402 to signal_process/modu/SLICE_402:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R22C49D.CLK to     R22C49D.Q0 signal_process/modu/SLICE_402 (from clk120mhz)
ROUTE         4     0.132     R22C49D.Q0 to     R22C49D.M1 signal_process/mudu_dy[0] (to clk120mhz)
                  --------
                    0.296   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_402:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R22C49D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/modu/SLICE_402:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R22C49D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.178ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              u_uart/U2/tx_state[5]  (from clk120mhz +)
   Destination:    FF         Data in        u_uart/U2/tx_state[6]  (to clk120mhz +)

   Delay:               0.296ns  (55.1% logic, 44.9% route), 1 logic levels.

 Constraint Details:

      0.296ns physical path delay u_uart/U2/SLICE_546 to u_uart/U2/SLICE_547 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.178ns

 Physical Path Details:

      Data path u_uart/U2/SLICE_546 to u_uart/U2/SLICE_547:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R22C40C.CLK to     R22C40C.Q1 u_uart/U2/SLICE_546 (from clk120mhz)
ROUTE        10     0.133     R22C40C.Q1 to     R22C40D.M0 u_uart/U2/tx_state[5] (to clk120mhz)
                  --------
                    0.296   (55.1% logic, 44.9% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_546:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R22C40C.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to u_uart/U2/SLICE_547:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       444     0.653  PLL_BR0.CLKOP to    R22C40D.CLK clk120mhz
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


================================================================================
<A name="par_twr_pref_1_1"></A>Preference: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
            3495 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 0.175ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[0]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data[0]  (to wendu.clk_us +)

   Delay:               0.293ns  (56.0% logic, 44.0% route), 1 logic levels.

 Constraint Details:

      0.293ns physical path delay wendu/SLICE_574 to wendu/SLICE_486 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.175ns

 Physical Path Details:

      Data path wendu/SLICE_574 to wendu/SLICE_486:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R18C38A.CLK to     R18C38A.Q0 wendu/SLICE_574 (from wendu.clk_us)
ROUTE         1     0.129     R18C38A.Q0 to     R18C38C.M0 wendu/data_temp[0] (to wendu.clk_us)
                  --------
                    0.293   (56.0% logic, 44.0% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_574:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C38A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_486:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C38C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[13]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[12]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_580 to wendu/SLICE_580 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_580 to wendu/SLICE_580:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R17C37C.CLK to     R17C37C.Q1 wendu/SLICE_580 (from wendu.clk_us)
ROUTE         2     0.131     R17C37C.Q1 to     R17C37C.M0 wendu/data_temp[13] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_580:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R17C37C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_580:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R17C37C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[7]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data[7]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_577 to wendu/SLICE_489 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_577 to wendu/SLICE_489:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R19C37A.CLK to     R19C37A.Q1 wendu/SLICE_577 (from wendu.clk_us)
ROUTE         2     0.131     R19C37A.Q1 to     R19C37C.M1 wendu/data_temp[7] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_577:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R19C37A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_489:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R19C37C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[3]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[2]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_575 to wendu/SLICE_575 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_575 to wendu/SLICE_575:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R18C37D.CLK to     R18C37D.Q1 wendu/SLICE_575 (from wendu.clk_us)
ROUTE         2     0.131     R18C37D.Q1 to     R18C37D.M0 wendu/data_temp[3] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_575:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C37D.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_575:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C37D.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[9]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[8]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_578 to wendu/SLICE_578 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_578 to wendu/SLICE_578:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R19C36A.CLK to     R19C36A.Q1 wendu/SLICE_578 (from wendu.clk_us)
ROUTE         2     0.131     R19C36A.Q1 to     R19C36A.M0 wendu/data_temp[9] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_578:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R19C36A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_578:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R19C36A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[9]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data[9]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_578 to wendu/SLICE_490 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_578 to wendu/SLICE_490:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R19C36A.CLK to     R19C36A.Q1 wendu/SLICE_578 (from wendu.clk_us)
ROUTE         2     0.131     R19C36A.Q1 to     R19C36B.M1 wendu/data_temp[9] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_578:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R19C36A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_490:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R19C36B.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[7]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[6]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_577 to wendu/SLICE_577 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_577 to wendu/SLICE_577:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R19C37A.CLK to     R19C37A.Q1 wendu/SLICE_577 (from wendu.clk_us)
ROUTE         2     0.131     R19C37A.Q1 to     R19C37A.M0 wendu/data_temp[7] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_577:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R19C37A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_577:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R19C37A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[1]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[0]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_574 to wendu/SLICE_574 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_574 to wendu/SLICE_574:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R18C38A.CLK to     R18C38A.Q1 wendu/SLICE_574 (from wendu.clk_us)
ROUTE         2     0.131     R18C38A.Q1 to     R18C38A.M0 wendu/data_temp[1] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_574:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C38A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_574:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C38A.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.176ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[11]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data_temp[10]  (to wendu.clk_us +)

   Delay:               0.294ns  (55.4% logic, 44.6% route), 1 logic levels.

 Constraint Details:

      0.294ns physical path delay wendu/SLICE_579 to wendu/SLICE_579 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.176ns

 Physical Path Details:

      Data path wendu/SLICE_579 to wendu/SLICE_579:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R18C36B.CLK to     R18C36B.Q1 wendu/SLICE_579 (from wendu.clk_us)
ROUTE         2     0.131     R18C36B.Q1 to     R18C36B.M0 wendu/data_temp[11] (to wendu.clk_us)
                  --------
                    0.294   (55.4% logic, 44.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_579:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C36B.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_579:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C36B.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.177ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              wendu/data_temp[2]  (from wendu.clk_us +)
   Destination:    FF         Data in        wendu/data[2]  (to wendu.clk_us +)

   Delay:               0.295ns  (55.6% logic, 44.4% route), 1 logic levels.

 Constraint Details:

      0.295ns physical path delay wendu/SLICE_575 to wendu/SLICE_487 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.177ns

 Physical Path Details:

      Data path wendu/SLICE_575 to wendu/SLICE_487:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R18C37D.CLK to     R18C37D.Q0 wendu/SLICE_575 (from wendu.clk_us)
ROUTE         2     0.131     R18C37D.Q0 to     R18C37C.M0 wendu/data_temp[2] (to wendu.clk_us)
                  --------
                    0.295   (55.6% logic, 44.4% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path wendu/SLICE_224 to wendu/SLICE_575:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C37D.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path wendu/SLICE_224 to wendu/SLICE_487:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     0.897     R38C31A.Q0 to    R18C37C.CLK wendu.clk_us
                  --------
                    0.897   (0.0% logic, 100.0% route), 0 logic levels.


================================================================================
<A name="par_twr_pref_1_2"></A>Preference: FREQUENCY NET "clk_in_c" 20.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
<A name="par_twr_pref_1_3"></A>Preference: FREQUENCY NET "clk_AD" 60.000000 MHz ;
            3075 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


Passed: The following path meets requirements by 0.178ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[0]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/sample_sum[0]  (to clk_AD +)

   Delay:               0.297ns  (80.8% logic, 19.2% route), 2 logic levels.

 Constraint Details:

      0.297ns physical path delay signal_process/demodu/SLICE_353 to signal_process/demodu/SLICE_353 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.178ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_353 to signal_process/demodu/SLICE_353:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R30C46A.CLK to     R30C46A.Q0 signal_process/demodu/SLICE_353 (from clk_AD)
ROUTE         4     0.057     R30C46A.Q0 to     R30C46A.D0 signal_process/demodu/sample_sum[0]
CTOF_DEL    ---     0.076     R30C46A.D0 to     R30C46A.F0 signal_process/demodu/SLICE_353
ROUTE         1     0.000     R30C46A.F0 to    R30C46A.DI0 signal_process/demodu/un3_sample_sum_axb_0 (to clk_AD)
                  --------
                    0.297   (80.8% logic, 19.2% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_353:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R30C46A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_353:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R30C46A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.179ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_valid_dy[0]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/AD_valid_dy[1]  (to clk_AD +)

   Delay:               0.297ns  (55.2% logic, 44.8% route), 1 logic levels.

 Constraint Details:

      0.297ns physical path delay signal_process/demodu/SLICE_282 to signal_process/demodu/SLICE_282 meets
      0.118ns M_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.118ns) by 0.179ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_282 to signal_process/demodu/SLICE_282:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R28C44C.CLK to     R28C44C.Q0 signal_process/demodu/SLICE_282 (from clk_AD)
ROUTE         2     0.133     R28C44C.Q0 to     R28C44C.M1 signal_process/demodu/AD_valid_dy[0] (to clk_AD)
                  --------
                    0.297   (55.2% logic, 44.8% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_282:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R28C44C.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_282:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R28C44C.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.243ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/latch_sample_sum[31]  (from clk_AD +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (to clk_AD +)

   Delay:               0.345ns  (47.2% logic, 52.8% route), 1 logic levels.

 Constraint Details:

      0.345ns physical path delay signal_process/demodu/SLICE_312 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.059ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.102ns) by 0.243ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_312 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R26C50A.CLK to     R26C50A.Q1 signal_process/demodu/SLICE_312 (from clk_AD)
ROUTE         1     0.182     R26C50A.Q1 to *R_R25C48.DI31 signal_process/demodu/latch_sample_sum[31] (to clk_AD)
                  --------
                    0.345   (47.2% logic, 52.8% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_312:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R26C50A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696  PLL_BR0.CLKOS to *R_R25C48.CLKW clk_AD
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.243ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_valid_dy[1]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/AD_validcnt[0]  (to clk_AD +)

   Delay:               0.362ns  (66.0% logic, 34.0% route), 2 logic levels.

 Constraint Details:

      0.362ns physical path delay signal_process/demodu/SLICE_282 to signal_process/demodu/SLICE_283 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.243ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_282 to signal_process/demodu/SLICE_283:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R28C44C.CLK to     R28C44C.Q1 signal_process/demodu/SLICE_282 (from clk_AD)
ROUTE         1     0.121     R28C44C.Q1 to     R28C44D.D0 signal_process/demodu/AD_valid_dy[1]
CTOF_DEL    ---     0.076     R28C44D.D0 to     R28C44D.F0 signal_process/demodu/SLICE_283
ROUTE        34     0.002     R28C44D.F0 to    R28C44D.DI0 signal_process/demodu/AD_validcnt7 (to clk_AD)
                  --------
                    0.362   (66.0% logic, 34.0% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_282:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R28C44C.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_283:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R28C44D.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.243ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/latch_sample_sum[35]  (from clk_AD +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (to clk_AD +)

   Delay:               0.345ns  (47.2% logic, 52.8% route), 1 logic levels.

 Constraint Details:

      0.345ns physical path delay signal_process/demodu/SLICE_314 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.059ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.102ns) by 0.243ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_314 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.163    R27C50D.CLK to     R27C50D.Q1 signal_process/demodu/SLICE_314 (from clk_AD)
ROUTE         1     0.182     R27C50D.Q1 to *R_R25C48.DI35 signal_process/demodu/latch_sample_sum[35] (to clk_AD)
                  --------
                    0.345   (47.2% logic, 52.8% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_314:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R27C50D.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696  PLL_BR0.CLKOS to *R_R25C48.CLKW clk_AD
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.244ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/latch_sample_sum[34]  (from clk_AD +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (to clk_AD +)

   Delay:               0.346ns  (47.4% logic, 52.6% route), 1 logic levels.

 Constraint Details:

      0.346ns physical path delay signal_process/demodu/SLICE_314 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.059ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.102ns) by 0.244ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_314 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R27C50D.CLK to     R27C50D.Q0 signal_process/demodu/SLICE_314 (from clk_AD)
ROUTE         1     0.182     R27C50D.Q0 to *R_R25C48.DI34 signal_process/demodu/latch_sample_sum[34] (to clk_AD)
                  --------
                    0.346   (47.4% logic, 52.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_314:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R27C50D.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696  PLL_BR0.CLKOS to *R_R25C48.CLKW clk_AD
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.244ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/latch_sample_sum[30]  (from clk_AD +)
   Destination:    PDPW16KD   Port           signal_process/demodu/fifo/pdp_ram_0_0_1(ASIC)  (to clk_AD +)

   Delay:               0.346ns  (47.4% logic, 52.6% route), 1 logic levels.

 Constraint Details:

      0.346ns physical path delay signal_process/demodu/SLICE_312 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      0.059ns DATA_HLD and
      0.000ns delay constraint less
     -0.043ns skew requirement (totaling 0.102ns) by 0.244ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_312 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R26C50A.CLK to     R26C50A.Q0 signal_process/demodu/SLICE_312 (from clk_AD)
ROUTE         1     0.182     R26C50A.Q0 to *R_R25C48.DI30 signal_process/demodu/latch_sample_sum[30] (to clk_AD)
                  --------
                    0.346   (47.4% logic, 52.6% route), 1 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_312:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R26C50A.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.696  PLL_BR0.CLKOS to *R_R25C48.CLKW clk_AD
                  --------
                    0.696   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.257ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_validcnt[0]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/Write_en  (to clk_AD +)

   Delay:               0.376ns  (63.8% logic, 36.2% route), 2 logic levels.

 Constraint Details:

      0.376ns physical path delay signal_process/demodu/SLICE_283 to signal_process/demodu/SLICE_287 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.257ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_283 to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R28C44D.CLK to     R28C44D.Q0 signal_process/demodu/SLICE_283 (from clk_AD)
ROUTE         3     0.136     R28C44D.Q0 to     R30C44C.D0 signal_process/demodu/AD_validcnt[0]
CTOF_DEL    ---     0.076     R30C44C.D0 to     R30C44C.F0 signal_process/demodu/SLICE_287
ROUTE         1     0.000     R30C44C.F0 to    R30C44C.DI0 signal_process/demodu/Write_enc (to clk_AD)
                  --------
                    0.376   (63.8% logic, 36.2% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_283:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R28C44D.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R30C44C.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.257ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/AD_validcnt[0]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/Latch_sum  (to clk_AD +)

   Delay:               0.376ns  (63.8% logic, 36.2% route), 2 logic levels.

 Constraint Details:

      0.376ns physical path delay signal_process/demodu/SLICE_283 to signal_process/demodu/SLICE_285 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.257ns

 Physical Path Details:

      Data path signal_process/demodu/SLICE_283 to signal_process/demodu/SLICE_285:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R28C44D.CLK to     R28C44D.Q0 signal_process/demodu/SLICE_283 (from clk_AD)
ROUTE         3     0.136     R28C44D.Q0 to     R30C44D.D0 signal_process/demodu/AD_validcnt[0]
CTOF_DEL    ---     0.076     R30C44D.D0 to     R30C44D.F0 signal_process/demodu/SLICE_285
ROUTE         1     0.000     R30C44D.F0 to    R30C44D.DI0 signal_process/demodu/Latch_sumc (to clk_AD)
                  --------
                    0.376   (63.8% logic, 36.2% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_283:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R28C44D.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/SLICE_285:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R30C44D.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


Passed: The following path meets requirements by 0.261ns

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/fifo/FF_18  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/fifo/FF_19  (to clk_AD +)

   Delay:               0.380ns  (63.2% logic, 36.8% route), 2 logic levels.

 Constraint Details:

      0.380ns physical path delay signal_process/demodu/fifo/SLICE_295 to signal_process/demodu/fifo/SLICE_296 meets
      0.119ns DIN_HLD and
      0.000ns delay constraint less
      0.000ns skew requirement (totaling 0.119ns) by 0.261ns

 Physical Path Details:

      Data path signal_process/demodu/fifo/SLICE_295 to signal_process/demodu/fifo/SLICE_296:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.164    R32C43C.CLK to     R32C43C.Q0 signal_process/demodu/fifo/SLICE_295 (from clk_AD)
ROUTE         6     0.140     R32C43C.Q0 to     R31C43C.D0 signal_process/demodu/fifo/Full
CTOF_DEL    ---     0.076     R31C43C.D0 to     R31C43C.F0 signal_process/demodu/fifo/SLICE_296
ROUTE         1     0.000     R31C43C.F0 to    R31C43C.DI0 signal_process/demodu/fifo/empty_d (to clk_AD)
                  --------
                    0.380   (63.2% logic, 36.8% route), 2 logic levels.

 Clock Skew Details: 

      Source Clock Path CLK120/PLLInst_0 to signal_process/demodu/fifo/SLICE_295:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R32C43C.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.

      Destination Clock Path CLK120/PLLInst_0 to signal_process/demodu/fifo/SLICE_296:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE       101     0.653  PLL_BR0.CLKOS to    R31C43C.CLK clk_AD
                  --------
                    0.653   (0.0% logic, 100.0% route), 0 logic levels.


================================================================================
<A name="par_twr_pref_1_4"></A>Preference: FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


================================================================================
<A name="par_twr_pref_1_5"></A>Preference: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
            1596 items scored.
--------------------------------------------------------------------------------


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[16]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[16]  (to clk120mhz +)

   Delay:               0.381ns  (42.8% logic, 57.2% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R29C48A.CLK to     R29C48A.Q1 signal_process/demodu/SLICE_166 (from clk_AD)
ROUTE         3     0.266     R29C48A.Q1 to     R27C48A.M0 signal_process/demodu/sample_sum[16] (to clk120mhz)
                  --------
                    0.459   (42.0% logic, 58.0% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/fifo/FF_19  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/Read_en  (to clk120mhz +)

   Delay:               0.382ns  (62.8% logic, 37.2% route), 2 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.194    R31C43C.CLK to     R31C43C.Q0 signal_process/demodu/fifo/SLICE_296 (from clk_AD)
ROUTE         6     0.202     R31C43C.Q0 to     R32C43D.D0 signal_process/demodu/fifo/empty_flag
CTOF_DEL    ---     0.089     R32C43D.D0 to     R32C43D.F0 signal_process/demodu/SLICE_286
ROUTE         1     0.000     R32C43D.F0 to    R32C43D.DI0 signal_process/demodu/empty_flag_i (to clk120mhz)
                  --------
                    0.485   (58.4% logic, 41.6% route), 2 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[20]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[20]  (to clk120mhz +)

   Delay:               0.381ns  (42.8% logic, 57.2% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R29C48C.CLK to     R29C48C.Q1 signal_process/demodu/SLICE_168 (from clk_AD)
ROUTE         3     0.266     R29C48C.Q1 to     R27C48C.M0 signal_process/demodu/sample_sum[20] (to clk120mhz)
                  --------
                    0.459   (42.0% logic, 58.0% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[17]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[17]  (to clk120mhz +)

   Delay:               0.382ns  (42.9% logic, 57.1% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.194    R29C48B.CLK to     R29C48B.Q0 signal_process/demodu/SLICE_167 (from clk_AD)
ROUTE         3     0.265     R29C48B.Q0 to     R27C48A.M1 signal_process/demodu/sample_sum[17] (to clk120mhz)
                  --------
                    0.459   (42.3% logic, 57.7% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[21]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[21]  (to clk120mhz +)

   Delay:               0.382ns  (42.9% logic, 57.1% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.194    R29C48D.CLK to     R29C48D.Q0 signal_process/demodu/SLICE_169 (from clk_AD)
ROUTE         3     0.265     R29C48D.Q0 to     R27C48C.M1 signal_process/demodu/sample_sum[21] (to clk120mhz)
                  --------
                    0.459   (42.3% logic, 57.7% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[6]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[6]  (to clk120mhz +)

   Delay:               0.432ns  (37.7% logic, 62.3% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R29C46D.CLK to     R29C46D.Q1 signal_process/demodu/SLICE_161 (from clk_AD)
ROUTE         3     0.371     R29C46D.Q1 to     R26C47D.M0 signal_process/demodu/sample_sum[6] (to clk120mhz)
                  --------
                    0.564   (34.2% logic, 65.8% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[14]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[14]  (to clk120mhz +)

   Delay:               0.432ns  (37.7% logic, 62.3% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R29C47D.CLK to     R29C47D.Q1 signal_process/demodu/SLICE_165 (from clk_AD)
ROUTE         3     0.371     R29C47D.Q1 to     R26C48D.M0 signal_process/demodu/sample_sum[14] (to clk120mhz)
                  --------
                    0.564   (34.2% logic, 65.8% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[4]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[4]  (to clk120mhz +)

   Delay:               0.432ns  (37.7% logic, 62.3% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R29C46C.CLK to     R29C46C.Q1 signal_process/demodu/SLICE_160 (from clk_AD)
ROUTE         3     0.371     R29C46C.Q1 to     R26C48A.M0 signal_process/demodu/sample_sum[4] (to clk120mhz)
                  --------
                    0.564   (34.2% logic, 65.8% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[8]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[8]  (to clk120mhz +)

   Delay:               0.463ns  (35.2% logic, 64.8% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R29C47A.CLK to     R29C47A.Q1 signal_process/demodu/SLICE_162 (from clk_AD)
ROUTE         3     0.361     R29C47A.Q1 to     R27C49C.M0 signal_process/demodu/sample_sum[8] (to clk120mhz)
                  --------
                    0.554   (34.8% logic, 65.2% route), 1 logic levels.


Blocked: 

 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              signal_process/demodu/sample_sum[22]  (from clk_AD +)
   Destination:    FF         Data in        signal_process/demodu/median_sum_n[22]  (to clk120mhz +)

   Delay:               0.463ns  (35.2% logic, 64.8% route), 1 logic levels.

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.193    R29C48D.CLK to     R29C48D.Q1 signal_process/demodu/SLICE_169 (from clk_AD)
ROUTE         3     0.361     R29C48D.Q1 to     R28C50A.M0 signal_process/demodu/sample_sum[22] (to clk120mhz)
                  --------
                    0.554   (34.8% logic, 65.2% route), 1 logic levels.

<A name="ptwr_hold_rs"></A><B><U><big>Report Summary</big></U></B>
--------------
----------------------------------------------------------------------------
Preference(MIN Delays)                  |   Constraint|       Actual|Levels
----------------------------------------------------------------------------
                                        |             |             |
FREQUENCY NET "clk120mhz" 120.000000    |             |             |
MHz ;                                   |     0.000 ns|     0.175 ns|   1  
                                        |             |             |
FREQUENCY NET "wendu.clk_us" 1.000000   |             |             |
MHz ;                                   |     0.000 ns|     0.175 ns|   1  
                                        |             |             |
FREQUENCY NET "clk_in_c" 20.000000 MHz  |             |             |
;                                       |            -|            -|   0  
                                        |             |             |
FREQUENCY NET "clk_AD" 60.000000 MHz ;  |     0.000 ns|     0.178 ns|   2  
                                        |             |             |
FREQUENCY NET "clk_AD_t_c" 60.000000    |             |             |
MHz ;                                   |            -|            -|   0  
                                        |             |             |
----------------------------------------------------------------------------


All preferences were met.


<A name="ptwr_hold_clkda"></A><B><U><big>Clock Domains Analysis</big></U></B>
------------------------

Found 4 clocks:

Clock Domain: wendu.clk_us   Source: wendu/SLICE_224.Q0   Loads: 37
   Covered under: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;

Clock Domain: clk_in_c   Source: clk_in.PAD   Loads: 1
   No transfer within this clock domain is found

Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS   Loads: 101
   Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;

   Data transfers from:
   Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP
      Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;   Transfers: 2

Clock Domain: clk120mhz   Source: CLK120/PLLInst_0.CLKOP   Loads: 444
   Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;

   Data transfers from:
   Clock Domain: wendu.clk_us   Source: wendu/SLICE_224.Q0
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 16

   Clock Domain: clk_AD   Source: CLK120/PLLInst_0.CLKOS
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD" TO CLKNET "clk120mhz" ;


<A name="ptwr_hold_ts"></A><B><U><big>Timing summary (Hold):</big></U></B>
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38135 paths, 6 nets, and 4122 connections (99.21% coverage)



<A name="ptwr_ts"></A><B><U><big>Timing summary (Setup and Hold):</big></U></B>
---------------

Timing errors: 0 (setup), 0 (hold)
Score: 0 (setup), 0 (hold)
Cumulative negative slack: 0 (0+0)
--------------------------------------------------------------------------------

--------------------------------------------------------------------------------




<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
<BR>
</PRE></FONT>
</BODY>
</HTML>
