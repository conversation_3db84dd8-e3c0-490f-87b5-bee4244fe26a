

// TOOL:     vlog2tf
// DATE:     Thu Mar 13 17:01:43 2025
 
// TITLE:    Lattice Semiconductor Corporation
// MODULE:   global_clock
// DESIGN:   global_clock
// FILENAME: global_clock.tfi
// PROJECT:  INS350_5J_JZ
// VERSION:  2.0
// NOTE: DO NOT EDIT THIS FILE
//
// This file is generated by the Verilog Test Fixture Declarations process and 
// contains an I/O and instance declarations of the Verilog source file
// you selected from the Sources in Project list.
// Notes:
// 1) This include file (.tfi) should be referenced by your text fixture using
// the `include compile directive using the syntax:  `include "<file_name>.tfi"
// 2) If your design I/O changes, rerun the process to obtain new I/O and 
// instance declarations.
// 3) Verilog simulations will produce errors if there are <PERSON><PERSON><PERSON> FPGA library 
// elements in your design that require the instantiation of GSR, PUR, and TSALL
// and they are not present in the test fixture. For more information see the 
// How To section of online help. 



// Inputs
	reg CLKI;
	reg RST;


// Outputs
	wire CLKOP;
	wire CLKOS;
	wire CLKOS2;
	wire LOCK;


// Bidirs


// Instantiate the UUT
	global_clock UUT (
		.CLKI(CLKI), 
		.RST(RST), 
		.CLKOP(CLKOP), 
		.CLKOS(CLKOS), 
		.CLKOS2(CLKOS2), 
		.LOCK(LOCK)
	);


// Initialize Inputs
`ifdef auto_init

	initial begin
		CLKI = 0;
		RST = 0;
	end

`endif

