BITGEN: Bitstream Generator Diamond (64-bit) 3.12.1.454
Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
Tue Jun 24 11:16:13 2025


Command: bitgen -w -g CfgMode:Disable -g RamCfg:Reset -g DisableUES:FALSE -g ES:No -e -s D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.sec -k D:/Project/INS350_5J_JZ _V2/INS350_5J_JZ.bek -gui -msgset D:/Project/INS350_5J_JZ _V2/promote.xml INS350_5J_JZ_impl1.ncd INS350_5J_JZ_impl1.prf 

Loading design for application Bitgen from file INS350_5J_JZ_impl1.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application Bitgen from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.

Running DRC.
DRC detected 0 errors and 0 warnings.
Reading Preference File from INS350_5J_JZ_impl1.prf.

Preference Summary:
+---------------------------------+---------------------------------+
|  Preference                     |  Current Setting                |
+---------------------------------+---------------------------------+
|                         RamCfg  |                        Reset**  |
+---------------------------------+---------------------------------+
|                        CfgMode  |                      Disable**  |
+---------------------------------+---------------------------------+
|                        DONE_EX  |                          OFF**  |
+---------------------------------+---------------------------------+
|                        DONE_OD  |                           ON**  |
+---------------------------------+---------------------------------+
|                     MCCLK_FREQ  |                          2.4**  |
+---------------------------------+---------------------------------+
|                  CONFIG_SECURE  |                          OFF**  |
+---------------------------------+---------------------------------+
|                    CONFIG_MODE  |                         JTAG**  |
+---------------------------------+---------------------------------+
|                        WAKE_UP  |                           21**  |
+---------------------------------+---------------------------------+
|                          INBUF  |                          OFF**  |
+---------------------------------+---------------------------------+
|                             ES  |                           No**  |
+---------------------------------+---------------------------------+
|                 SLAVE_SPI_PORT  |                      DISABLE**  |
+---------------------------------+---------------------------------+
|                MASTER_SPI_PORT  |                      DISABLE**  |
+---------------------------------+---------------------------------+
|                COMPRESS_CONFIG  |                          OFF**  |
+---------------------------------+---------------------------------+
|            BACKGROUND_RECONFIG  |                          OFF**  |
+---------------------------------+---------------------------------+
|                     DisableUES  |                        FALSE**  |
+---------------------------------+---------------------------------+
|            SLAVE_PARALLEL_PORT  |                      DISABLE**  |
+---------------------------------+---------------------------------+
|                      DONE_PULL  |                           ON**  |
+---------------------------------+---------------------------------+
|               CONFIG_IOVOLTAGE  |                            3.3  |
+---------------------------------+---------------------------------+
|                        TRANSFR  |                          OFF**  |
+---------------------------------+---------------------------------+
 *  Default setting.
 ** The specified setting matches the default setting.


Creating bit map...
 
Bitstream Status: Final           Version 10.27.
 
Saving bit stream in "INS350_5J_JZ_impl1.bit".
Total CPU Time: 5 secs 
Total REAL Time: 5 secs 
Peak Memory Usage: 290 MB
