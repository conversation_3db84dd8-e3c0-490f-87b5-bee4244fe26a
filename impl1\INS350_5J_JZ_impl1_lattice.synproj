-a "ECP5U"
-d LFE5U-25F
-t CABGA256
-s 7
-frequency 200
-optimization_goal Timing
-bram_utilization 100
-ramstyle Auto
-romstyle auto
-dsp_utilization 100
-use_dsp 1
-use_carry_chain 1
-carry_chain_length 0
-force_gsr No
-resource_sharing 1
-propagate_constants 1
-remove_duplicate_regs 1
-mux_style Auto
-max_fanout 1000
-fsm_encoding_style Auto

-fix_gated_clocks 1
-loop_limit 1950



-use_io_insertion 1
-resolve_mixed_drivers 0
-use_io_reg auto


-lpf 1
-p "D:/Project/INS350_5J_JZ"
-ver "D:/Project/INS350_5J_JZ/Src_al/INS350_5J_JZ.v"
"D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.v"
"D:/Project/INS350_5J_JZ/Src_al/Ctrl_Data.v"
"D:/Project/INS350_5J_JZ/Src_al/Demodulation.v"
"D:/Project/INS350_5J_JZ/Src_al/DS18B20.v"
"D:/Project/INS350_5J_JZ/Src_al/Integration.v"
"D:/Project/INS350_5J_JZ/Src_al/Modulation.v"
"D:/Project/INS350_5J_JZ/Src_al/Rs422Output.v"
"D:/Project/INS350_5J_JZ/Src_al/SignalGenerator.v"
"D:/Project/INS350_5J_JZ/Src_al/SignalProcessing.v"
"D:/Project/INS350_5J_JZ/Src_al/speed_select_Tx.v"
"D:/Project/INS350_5J_JZ/Src_al/SquareWaveGenerator.v"
"D:/Project/INS350_5J_JZ/Src_al/UART_Control.v"
"D:/Project/INS350_5J_JZ/Src_al/uart_tx.v"
"D:/Project/INS350_5J_JZ/IP_al/global_clock1/Asys_fifo56X16/Asys_fifo56X16.v"
-top INS350_5J_JZ


-p "D:/Software/lscc/diamond/3.12/ispfpga/sa5p00/data" "D:/Project/INS350_5J_JZ/impl1" "D:/Project/INS350_5J_JZ"

-ngd "INS350_5J_JZ_impl1.ngd"

