 <html> 
  <head>
    <script type="text/javascript" src="file:///D:\Software\lscc\diamond\3.12\synpbase\lib\report\reportlog_tree.js"></script>
    <link rel="stylesheet" type="text/css" href="file:///D:\Software\lscc\diamond\3.12\synpbase\lib\report\reportlog_tree.css" />  
  </head> 

  <body style="background-color:#e0e0ff;">
   <script type="text/javascript"> reportLogObj.loadImage("closed.png", "open.png")</script>
    <ul id="impl1-menu" class="treeview" style="padding-left:12;">
        <li style="font-size:12; font-style:normal"> <b style="background-color:#a2bff0; font-weight:bold">proj_1 (impl1)</b> 
         <ul rel="open" style="font-size:small;">

<li style="font-size:12; font-style:normal"><b style="background-color:#a2bff0; font-weight:bold">Synthesis -  </b> 
<ul rel="open">
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#compilerReport1" target="srrFrame" title="">Compiler Report</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#compilerReport3" target="srrFrame" title="">Compiler Constraint Applicator</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#mapperReport5" target="srrFrame" title="">Pre-mapping Report</a>  
<ul rel="open" >
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#mapperReport6" target="srrFrame" title="">Clock Summary</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#clockReport7" target="srrFrame" title="">Clock Conversion</a>  </li></ul></li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#mapperReport8" target="srrFrame" title="">Mapper Report</a>  
<ul rel="open" >
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#timingReport9" target="srrFrame" title="">Timing Report</a>  
<ul rel="open" >
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#performanceSummary10" target="srrFrame" title="">Performance Summary</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#clockRelationships11" target="srrFrame" title="">Clock Relationships</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#interfaceInfo12" target="srrFrame" title="">Interface Information</a>  </li>
<li><a href="file:///#" target="srrFrame" title="">Detailed Report for Clocks</a>  
<ul  >
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#clockReport13" target="srrFrame" title="">Clock: DS18B20|clk_us_derived_clock</a>  
<ul  >
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#startingSlack14" target="srrFrame" title="">Starting Points with Worst Slack</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#endingSlack15" target="srrFrame" title="">Ending Points with Worst Slack</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#worstPaths16" target="srrFrame" title="">Worst Path Information</a>  </li></ul></li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#clockReport17" target="srrFrame" title="">Clock: global_clock|CLKOP_inferred_clock</a>  
<ul  >
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#startingSlack18" target="srrFrame" title="">Starting Points with Worst Slack</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#endingSlack19" target="srrFrame" title="">Ending Points with Worst Slack</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#worstPaths20" target="srrFrame" title="">Worst Path Information</a>  </li></ul></li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#clockReport21" target="srrFrame" title="">Clock: global_clock|CLKOS_inferred_clock</a>  
<ul  >
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#startingSlack22" target="srrFrame" title="">Starting Points with Worst Slack</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#endingSlack23" target="srrFrame" title="">Ending Points with Worst Slack</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#worstPaths24" target="srrFrame" title="">Worst Path Information</a>  </li></ul></li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#clockReport25" target="srrFrame" title="">Clock: System</a>  
<ul  >
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#startingSlack26" target="srrFrame" title="">Starting Points with Worst Slack</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#endingSlack27" target="srrFrame" title="">Ending Points with Worst Slack</a>  </li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#worstPaths28" target="srrFrame" title="">Worst Path Information</a>  </li></ul></li></ul></li></ul></li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\syntmp\INS350_5J_JZ_impl1_srr.htm#resourceUsage29" target="srrFrame" title="">Resource Utilization</a>  </li></ul></li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1_cck.rpt" target="srrFrame" title="">Constraint Checker Report (15:40 23-Jun)</a>  </li></ul></li>
<li style="font-size:12; font-style:normal"><b style="background-color:#a2bff0; font-weight:bold">Place and Route -  </b> 
<ul rel="open">
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1.twr" target="srrFrame" title="">Timing Report (15:03 23-Jun)</a>  </li></ul></li>
<li><a href="file:///D:\Project\INS350_5J_JZ _copy\impl1\stdout.log" target="srrFrame" title="">Session Log (15:40 23-Jun)</a>  
<ul  ></ul></li>         </ul>
        </li>
   </ul>

   <script type="text/javascript"> reportLogObj.generateLog("impl1-menu")</script>

  </body>
 </html>