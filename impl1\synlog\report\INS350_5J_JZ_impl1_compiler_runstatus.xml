<?xml version="1.0" encoding="UTF-8" ?>
<!-- *************************************************************************************
FILE DESCRIPTION
The file contains the job information from compiler to be displayed as part of the summary report.
*******************************************************************************************-->

<job_run_status name="compiler"> 
  <report_link name="Detailed report">
    <data>D:\Project\INS350_5J_JZ _copy\impl1\synlog\INS350_5J_JZ_impl1_compiler.srr</data>
    <title>Synopsys HDL Compiler</title>
  </report_link> 
  <job_status>
    <data>Completed    </data>
  </job_status>
<job_info> 
			<info name="Notes">
  <data>82</data>
  <report_link name="more"><data>D:\Project\INS350_5J_JZ _copy\impl1\synlog\report\INS350_5J_JZ_impl1_compiler_notes.txt</data></report_link>
  </info>
			<info name="Warnings">
  <data>33</data>
  <report_link name="more"><data>D:\Project\INS350_5J_JZ _copy\impl1\synlog\report\INS350_5J_JZ_impl1_compiler_warnings.txt</data></report_link>
  </info>
			<info name="Errors">
  <data>0</data>
  <report_link name="more"><data>D:\Project\INS350_5J_JZ _copy\impl1\synlog\report\INS350_5J_JZ_impl1_compiler_errors.txt</data></report_link>
  </info>
			<info name="CPU Time">
  <data>-</data>
  </info>
			<info name="Real Time">
  <data>00h:00m:05s</data>
  </info>
			<info name="Peak Memory">
  <data>-</data>
  </info>
			<info name="Date &amp;Time">
  <data type="timestamp">1750664454</data>
  </info>
			</job_info>
</job_run_status>