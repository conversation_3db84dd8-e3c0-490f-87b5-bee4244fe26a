<HTML>
<HEAD><TITLE>Project Summary</TITLE>
<STYLE TYPE="text/css">
<!--
 body,pre{
    font-family:'Courier New', monospace;
    color: #000000;
    font-size:88%;
    background-color: #ffffff;
}
h1 {
    font-weight: bold;
    margin-top: 24px;
    margin-bottom: 10px;
    border-bottom: 3px solid #000;    font-size: 1em;
}
h2 {
    font-weight: bold;
    margin-top: 18px;
    margin-bottom: 5px;
    font-size: 0.90em;
}
h3 {
    font-weight: bold;
    margin-top: 12px;
    margin-bottom: 5px;
    font-size: 0.80em;
}
p {
    font-size:78%;
}
P.Table {
    margin-top: 4px;
    margin-bottom: 4px;
    margin-right: 4px;
    margin-left: 4px;
}
table
{
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    border-collapse: collapse;
}
th {
    font-weight:bold;
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    text-align:left;
    font-size:78%;
}
td {
    padding: 4px;
    border-width: 1px 1px 1px 1px;
    border-style: solid solid solid solid;
    border-color: black black black black;
    vertical-align:top;
    font-size:78%;
}
a {
    color:#013C9A;
    text-decoration:none;
}

a:visited {
    color:#013C9A;
}

a:hover, a:active {
    text-decoration:underline;
    color:#5BAFD4;
}
.pass
{
background-color: #00ff00;
}
.fail
{
background-color: #ff0000;
}
.comment
{
    font-size: 90%;
    font-style: italic;
}

-->
</STYLE>
</HEAD>
<PRE><TABLE border=1 width=100% cellspacing=0 cellpadding=2><small>
<TR>
<TD align='center' BGCOLOR='#000099' COLSPAN='4'><SPAN style="COLOR: #FFFFFF"><B>INS350_5J_JZ project summary</B></SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Module Name:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">INS350_5J_JZ</SPAN></TD>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Synthesis:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">SynplifyPro</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Implementation Name:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">impl1</SPAN></TD>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Strategy Name:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">Strategy1</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Last Process:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">Map Design</SPAN></TD>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">State:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">Failed</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Target Device:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">LFE5U-25F-7BG256C</SPAN></TD>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Device Family:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">ECP5U</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Device Type:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">LFE5U-25F</SPAN></TD>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Package Type:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">CABGA256</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Performance grade:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">7</SPAN></TD>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Operating conditions:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">COM</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Logic preference file:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='3'><SPAN style="COLOR: #000000">INS350_5J_JZ.lpf</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Physical Preference file:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='3'><SPAN style="COLOR: #000000">impl1/INS350_5J_JZ_impl1.prf</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Product Version:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000">3.12.1.454</SPAN></TD>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Patch Version:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='1'><SPAN style="COLOR: #000000"></SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Updated:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='3'><SPAN style="COLOR: #000000">2025/06/25 09:46:49</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Implementation Location:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='3'><SPAN style="COLOR: #000000">D:/Project/INS350_5J_JZ _V2_copy/impl1</SPAN></TD>
</TR>
<TR>
<TD align='left' BGCOLOR='#DEE8F4' COLSPAN='1'><SPAN style="COLOR: #000000">Project File:</SPAN></TD>
<TD align='left' BGCOLOR='#FFFFFF' COLSPAN='3'><SPAN style="COLOR: #000000">D:/Project/INS350_5J_JZ _V2_copy/INS350_5J_JZ.ldf</SPAN></TD>
</TR>
</small></TABLE>
<BR>
<BR>
<BR>
<BR>
<BR>
</PRE></FONT>
</BODY>
</HTML>
