<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML><HEAD>
<META http-equiv=Content-Type content="text/html; charset=iso-8859-1">
<STYLE type=text/css>
<!--
.blink {text-decoration:blink}
.ms  {font-size: 9pt; font-family: monospace; font-weight: normal}
.msb {font-size: 9pt; font-family: monospace; font-weight: bold  }
-->
</STYLE>
<META content="MSHTML 6.00.2900.2180" name=GENERATOR></HEAD>
<BODY><B>
</B>
<BR><PRE><A name="Report Header"></A>
--------------------------------------------------------------------------------
<PERSON><PERSON><PERSON> TRACE Report - Setup, Version Diamond (64-bit) 3.12.1.454
Tue Mar 25 11:21:54 2025

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.

Report Information
------------------
Design file:     INS350_5J_JZ
Device,speed:    LFE5U-25F,7
Report level:    verbose report, limited to 10 items per preference
--------------------------------------------------------------------------------



</A><A name="FREQUENCY NET 'clk120mhz' 120.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "clk120mhz" 120.000000 MHz ;
            10 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------
<font color=#000000> 

Passed: The following path meets requirements by 0.181ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/INS_dout[54]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.301ns  (88.7% logic, 11.3% route), 28 logic levels.

 Constraint Details:

      8.301ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 0.181ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO20,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.934,EBR_R25C46.DO20,R24C46B.A1,signal_process/demodu/sample_sum_DY[2]:C1TOFCO_DEL, 0.401,R24C46B.A1,R24C46B.FCO,signal_process/demodu/SLICE_148:ROUTE, 0.000,R24C46B.FCO,R24C46C.FCI,signal_process/demodu/INS_dout_1_cry_2:FCITOFCO_DEL, 0.063,R24C46C.FCI,R24C46C.FCO,signal_process/demodu/SLICE_149:ROUTE, 0.000,R24C46C.FCO,R24C46D.FCI,signal_process/demodu/INS_dout_1_cry_4:FCITOFCO_DEL, 0.063,R24C46D.FCI,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOFCO_DEL, 0.063,R24C52C.FCI,R24C52C.FCO,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.FCO,R24C52D.FCI,signal_process/demodu/INS_dout_1_cry_52:FCITOF1_DEL, 0.412,R24C52D.FCI,R24C52D.F1,signal_process/demodu/SLICE_174:ROUTE, 0.000,R24C52D.F1,R24C52D.DI1,signal_process/demodu/INS_dout_1[54]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO20 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.934<A href="#@net:signal_process/demodu/sample_sum_DY[2]:EBR_R25C46.DO20:R24C46B.A1:0.934"> EBR_R25C46.DO20 to R24C46B.A1    </A> <A href="#@net:signal_process/demodu/sample_sum_DY[2]">signal_process/demodu/sample_sum_DY[2]</A>
C1TOFCO_DE  ---     0.401     R24C46B.A1 to    R24C46B.FCO <A href="#@comp:signal_process/demodu/SLICE_148">signal_process/demodu/SLICE_148</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_2:R24C46B.FCO:R24C46C.FCI:0.000">    R24C46B.FCO to R24C46C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_2">signal_process/demodu/INS_dout_1_cry_2</A>
FCITOFCO_D  ---     0.063    R24C46C.FCI to    R24C46C.FCO <A href="#@comp:signal_process/demodu/SLICE_149">signal_process/demodu/SLICE_149</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_4:R24C46C.FCO:R24C46D.FCI:0.000">    R24C46C.FCO to R24C46D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_4">signal_process/demodu/INS_dout_1_cry_4</A>
FCITOFCO_D  ---     0.063    R24C46D.FCI to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOFCO_D  ---     0.063    R24C52C.FCI to    R24C52C.FCO <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_52:R24C52C.FCO:R24C52D.FCI:0.000">    R24C52C.FCO to R24C52D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_52">signal_process/demodu/INS_dout_1_cry_52</A>
FCITOF1_DE  ---     0.412    R24C52D.FCI to     R24C52D.F1 <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/SLICE_174</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[54]:R24C52D.F1:R24C52D.DI1:0.000">     R24C52D.F1 to R24C52D.DI1   </A> <A href="#@net:signal_process/demodu/INS_dout_1[54]">signal_process/demodu/INS_dout_1[54]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.301   (88.7% logic, 11.3% route), 28 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52D.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52D.CLK:1.890">  PLL_BR0.CLKOP to R24C52D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.196ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/INS_dout[54]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.286ns  (88.1% logic, 11.9% route), 27 logic levels.

 Constraint Details:

      8.286ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 0.196ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO22,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.982,EBR_R25C46.DO22,R24C46C.A1,signal_process/demodu/sample_sum_DY[4]:C1TOFCO_DEL, 0.401,R24C46C.A1,R24C46C.FCO,signal_process/demodu/SLICE_149:ROUTE, 0.000,R24C46C.FCO,R24C46D.FCI,signal_process/demodu/INS_dout_1_cry_4:FCITOFCO_DEL, 0.063,R24C46D.FCI,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOFCO_DEL, 0.063,R24C52C.FCI,R24C52C.FCO,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.FCO,R24C52D.FCI,signal_process/demodu/INS_dout_1_cry_52:FCITOF1_DEL, 0.412,R24C52D.FCI,R24C52D.F1,signal_process/demodu/SLICE_174:ROUTE, 0.000,R24C52D.F1,R24C52D.DI1,signal_process/demodu/INS_dout_1[54]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO22 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.982<A href="#@net:signal_process/demodu/sample_sum_DY[4]:EBR_R25C46.DO22:R24C46C.A1:0.982"> EBR_R25C46.DO22 to R24C46C.A1    </A> <A href="#@net:signal_process/demodu/sample_sum_DY[4]">signal_process/demodu/sample_sum_DY[4]</A>
C1TOFCO_DE  ---     0.401     R24C46C.A1 to    R24C46C.FCO <A href="#@comp:signal_process/demodu/SLICE_149">signal_process/demodu/SLICE_149</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_4:R24C46C.FCO:R24C46D.FCI:0.000">    R24C46C.FCO to R24C46D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_4">signal_process/demodu/INS_dout_1_cry_4</A>
FCITOFCO_D  ---     0.063    R24C46D.FCI to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOFCO_D  ---     0.063    R24C52C.FCI to    R24C52C.FCO <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_52:R24C52C.FCO:R24C52D.FCI:0.000">    R24C52C.FCO to R24C52D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_52">signal_process/demodu/INS_dout_1_cry_52</A>
FCITOF1_DE  ---     0.412    R24C52D.FCI to     R24C52D.F1 <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/SLICE_174</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[54]:R24C52D.F1:R24C52D.DI1:0.000">     R24C52D.F1 to R24C52D.DI1   </A> <A href="#@net:signal_process/demodu/INS_dout_1[54]">signal_process/demodu/INS_dout_1[54]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.286   (88.1% logic, 11.9% route), 27 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52D.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52D.CLK:1.890">  PLL_BR0.CLKOP to R24C52D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.205ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/INS_dout[53]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.274ns  (88.7% logic, 11.3% route), 28 logic levels.

 Constraint Details:

      8.274ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.479ns) by 0.205ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO20,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.934,EBR_R25C46.DO20,R24C46B.A1,signal_process/demodu/sample_sum_DY[2]:C1TOFCO_DEL, 0.401,R24C46B.A1,R24C46B.FCO,signal_process/demodu/SLICE_148:ROUTE, 0.000,R24C46B.FCO,R24C46C.FCI,signal_process/demodu/INS_dout_1_cry_2:FCITOFCO_DEL, 0.063,R24C46C.FCI,R24C46C.FCO,signal_process/demodu/SLICE_149:ROUTE, 0.000,R24C46C.FCO,R24C46D.FCI,signal_process/demodu/INS_dout_1_cry_4:FCITOFCO_DEL, 0.063,R24C46D.FCI,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOFCO_DEL, 0.063,R24C52C.FCI,R24C52C.FCO,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.FCO,R24C52D.FCI,signal_process/demodu/INS_dout_1_cry_52:FCITOF0_DEL, 0.385,R24C52D.FCI,R24C52D.F0,signal_process/demodu/SLICE_174:ROUTE, 0.000,R24C52D.F0,R24C52D.DI0,signal_process/demodu/INS_dout_1[53]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO20 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.934<A href="#@net:signal_process/demodu/sample_sum_DY[2]:EBR_R25C46.DO20:R24C46B.A1:0.934"> EBR_R25C46.DO20 to R24C46B.A1    </A> <A href="#@net:signal_process/demodu/sample_sum_DY[2]">signal_process/demodu/sample_sum_DY[2]</A>
C1TOFCO_DE  ---     0.401     R24C46B.A1 to    R24C46B.FCO <A href="#@comp:signal_process/demodu/SLICE_148">signal_process/demodu/SLICE_148</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_2:R24C46B.FCO:R24C46C.FCI:0.000">    R24C46B.FCO to R24C46C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_2">signal_process/demodu/INS_dout_1_cry_2</A>
FCITOFCO_D  ---     0.063    R24C46C.FCI to    R24C46C.FCO <A href="#@comp:signal_process/demodu/SLICE_149">signal_process/demodu/SLICE_149</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_4:R24C46C.FCO:R24C46D.FCI:0.000">    R24C46C.FCO to R24C46D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_4">signal_process/demodu/INS_dout_1_cry_4</A>
FCITOFCO_D  ---     0.063    R24C46D.FCI to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOFCO_D  ---     0.063    R24C52C.FCI to    R24C52C.FCO <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_52:R24C52C.FCO:R24C52D.FCI:0.000">    R24C52C.FCO to R24C52D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_52">signal_process/demodu/INS_dout_1_cry_52</A>
FCITOF0_DE  ---     0.385    R24C52D.FCI to     R24C52D.F0 <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/SLICE_174</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[53]:R24C52D.F0:R24C52D.DI0:0.000">     R24C52D.F0 to R24C52D.DI0   </A> <A href="#@net:signal_process/demodu/INS_dout_1[53]">signal_process/demodu/INS_dout_1[53]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.274   (88.7% logic, 11.3% route), 28 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52D.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52D.CLK:1.890">  PLL_BR0.CLKOP to R24C52D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.220ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/INS_dout[53]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.259ns  (88.1% logic, 11.9% route), 27 logic levels.

 Constraint Details:

      8.259ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.479ns) by 0.220ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO22,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.982,EBR_R25C46.DO22,R24C46C.A1,signal_process/demodu/sample_sum_DY[4]:C1TOFCO_DEL, 0.401,R24C46C.A1,R24C46C.FCO,signal_process/demodu/SLICE_149:ROUTE, 0.000,R24C46C.FCO,R24C46D.FCI,signal_process/demodu/INS_dout_1_cry_4:FCITOFCO_DEL, 0.063,R24C46D.FCI,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOFCO_DEL, 0.063,R24C52C.FCI,R24C52C.FCO,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.FCO,R24C52D.FCI,signal_process/demodu/INS_dout_1_cry_52:FCITOF0_DEL, 0.385,R24C52D.FCI,R24C52D.F0,signal_process/demodu/SLICE_174:ROUTE, 0.000,R24C52D.F0,R24C52D.DI0,signal_process/demodu/INS_dout_1[53]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO22 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.982<A href="#@net:signal_process/demodu/sample_sum_DY[4]:EBR_R25C46.DO22:R24C46C.A1:0.982"> EBR_R25C46.DO22 to R24C46C.A1    </A> <A href="#@net:signal_process/demodu/sample_sum_DY[4]">signal_process/demodu/sample_sum_DY[4]</A>
C1TOFCO_DE  ---     0.401     R24C46C.A1 to    R24C46C.FCO <A href="#@comp:signal_process/demodu/SLICE_149">signal_process/demodu/SLICE_149</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_4:R24C46C.FCO:R24C46D.FCI:0.000">    R24C46C.FCO to R24C46D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_4">signal_process/demodu/INS_dout_1_cry_4</A>
FCITOFCO_D  ---     0.063    R24C46D.FCI to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOFCO_D  ---     0.063    R24C52C.FCI to    R24C52C.FCO <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_52:R24C52C.FCO:R24C52D.FCI:0.000">    R24C52C.FCO to R24C52D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_52">signal_process/demodu/INS_dout_1_cry_52</A>
FCITOF0_DE  ---     0.385    R24C52D.FCI to     R24C52D.F0 <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/SLICE_174</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[53]:R24C52D.F0:R24C52D.DI0:0.000">     R24C52D.F0 to R24C52D.DI0   </A> <A href="#@net:signal_process/demodu/INS_dout_1[53]">signal_process/demodu/INS_dout_1[53]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.259   (88.1% logic, 11.9% route), 27 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52D.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52D.CLK:1.890">  PLL_BR0.CLKOP to R24C52D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.244ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/INS_dout[52]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.238ns  (88.7% logic, 11.3% route), 27 logic levels.

 Constraint Details:

      8.238ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_173 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 0.244ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO20,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.934,EBR_R25C46.DO20,R24C46B.A1,signal_process/demodu/sample_sum_DY[2]:C1TOFCO_DEL, 0.401,R24C46B.A1,R24C46B.FCO,signal_process/demodu/SLICE_148:ROUTE, 0.000,R24C46B.FCO,R24C46C.FCI,signal_process/demodu/INS_dout_1_cry_2:FCITOFCO_DEL, 0.063,R24C46C.FCI,R24C46C.FCO,signal_process/demodu/SLICE_149:ROUTE, 0.000,R24C46C.FCO,R24C46D.FCI,signal_process/demodu/INS_dout_1_cry_4:FCITOFCO_DEL, 0.063,R24C46D.FCI,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOF1_DEL, 0.412,R24C52C.FCI,R24C52C.F1,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.F1,R24C52C.DI1,signal_process/demodu/INS_dout_1[52]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_173:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO20 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.934<A href="#@net:signal_process/demodu/sample_sum_DY[2]:EBR_R25C46.DO20:R24C46B.A1:0.934"> EBR_R25C46.DO20 to R24C46B.A1    </A> <A href="#@net:signal_process/demodu/sample_sum_DY[2]">signal_process/demodu/sample_sum_DY[2]</A>
C1TOFCO_DE  ---     0.401     R24C46B.A1 to    R24C46B.FCO <A href="#@comp:signal_process/demodu/SLICE_148">signal_process/demodu/SLICE_148</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_2:R24C46B.FCO:R24C46C.FCI:0.000">    R24C46B.FCO to R24C46C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_2">signal_process/demodu/INS_dout_1_cry_2</A>
FCITOFCO_D  ---     0.063    R24C46C.FCI to    R24C46C.FCO <A href="#@comp:signal_process/demodu/SLICE_149">signal_process/demodu/SLICE_149</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_4:R24C46C.FCO:R24C46D.FCI:0.000">    R24C46C.FCO to R24C46D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_4">signal_process/demodu/INS_dout_1_cry_4</A>
FCITOFCO_D  ---     0.063    R24C46D.FCI to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOF1_DE  ---     0.412    R24C52C.FCI to     R24C52C.F1 <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[52]:R24C52C.F1:R24C52C.DI1:0.000">     R24C52C.F1 to R24C52C.DI1   </A> <A href="#@net:signal_process/demodu/INS_dout_1[52]">signal_process/demodu/INS_dout_1[52]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.238   (88.7% logic, 11.3% route), 27 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52C.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_173:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52C.CLK:1.890">  PLL_BR0.CLKOP to R24C52C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.258ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/INS_dout[54]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.224ns  (90.3% logic, 9.7% route), 29 logic levels.

 Constraint Details:

      8.224ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 0.258ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO18,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.794,EBR_R25C46.DO18,R24C46A.B1,signal_process/demodu/INS_dout_1:C1TOFCO_DEL, 0.401,R24C46A.B1,R24C46A.FCO,signal_process/demodu/SLICE_147:ROUTE, 0.000,R24C46A.FCO,R24C46B.FCI,signal_process/demodu/INS_dout_1_cry_0:FCITOFCO_DEL, 0.063,R24C46B.FCI,R24C46B.FCO,signal_process/demodu/SLICE_148:ROUTE, 0.000,R24C46B.FCO,R24C46C.FCI,signal_process/demodu/INS_dout_1_cry_2:FCITOFCO_DEL, 0.063,R24C46C.FCI,R24C46C.FCO,signal_process/demodu/SLICE_149:ROUTE, 0.000,R24C46C.FCO,R24C46D.FCI,signal_process/demodu/INS_dout_1_cry_4:FCITOFCO_DEL, 0.063,R24C46D.FCI,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOFCO_DEL, 0.063,R24C52C.FCI,R24C52C.FCO,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.FCO,R24C52D.FCI,signal_process/demodu/INS_dout_1_cry_52:FCITOF1_DEL, 0.412,R24C52D.FCI,R24C52D.F1,signal_process/demodu/SLICE_174:ROUTE, 0.000,R24C52D.F1,R24C52D.DI1,signal_process/demodu/INS_dout_1[54]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO18 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         2     0.794<A href="#@net:signal_process/demodu/INS_dout_1:EBR_R25C46.DO18:R24C46A.B1:0.794"> EBR_R25C46.DO18 to R24C46A.B1    </A> <A href="#@net:signal_process/demodu/INS_dout_1">signal_process/demodu/INS_dout_1</A>
C1TOFCO_DE  ---     0.401     R24C46A.B1 to    R24C46A.FCO <A href="#@comp:signal_process/demodu/SLICE_147">signal_process/demodu/SLICE_147</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_0:R24C46A.FCO:R24C46B.FCI:0.000">    R24C46A.FCO to R24C46B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_0">signal_process/demodu/INS_dout_1_cry_0</A>
FCITOFCO_D  ---     0.063    R24C46B.FCI to    R24C46B.FCO <A href="#@comp:signal_process/demodu/SLICE_148">signal_process/demodu/SLICE_148</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_2:R24C46B.FCO:R24C46C.FCI:0.000">    R24C46B.FCO to R24C46C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_2">signal_process/demodu/INS_dout_1_cry_2</A>
FCITOFCO_D  ---     0.063    R24C46C.FCI to    R24C46C.FCO <A href="#@comp:signal_process/demodu/SLICE_149">signal_process/demodu/SLICE_149</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_4:R24C46C.FCO:R24C46D.FCI:0.000">    R24C46C.FCO to R24C46D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_4">signal_process/demodu/INS_dout_1_cry_4</A>
FCITOFCO_D  ---     0.063    R24C46D.FCI to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOFCO_D  ---     0.063    R24C52C.FCI to    R24C52C.FCO <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_52:R24C52C.FCO:R24C52D.FCI:0.000">    R24C52C.FCO to R24C52D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_52">signal_process/demodu/INS_dout_1_cry_52</A>
FCITOF1_DE  ---     0.412    R24C52D.FCI to     R24C52D.F1 <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/SLICE_174</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[54]:R24C52D.F1:R24C52D.DI1:0.000">     R24C52D.F1 to R24C52D.DI1   </A> <A href="#@net:signal_process/demodu/INS_dout_1[54]">signal_process/demodu/INS_dout_1[54]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.224   (90.3% logic, 9.7% route), 29 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52D.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52D.CLK:1.890">  PLL_BR0.CLKOP to R24C52D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.259ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/INS_dout[54]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.223ns  (88.1% logic, 11.9% route), 26 logic levels.

 Constraint Details:

      8.223ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 0.259ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO24,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.982,EBR_R25C46.DO24,R24C46D.A1,signal_process/demodu/sample_sum_DY[6]:C1TOFCO_DEL, 0.401,R24C46D.A1,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOFCO_DEL, 0.063,R24C52C.FCI,R24C52C.FCO,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.FCO,R24C52D.FCI,signal_process/demodu/INS_dout_1_cry_52:FCITOF1_DEL, 0.412,R24C52D.FCI,R24C52D.F1,signal_process/demodu/SLICE_174:ROUTE, 0.000,R24C52D.F1,R24C52D.DI1,signal_process/demodu/INS_dout_1[54]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO24 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.982<A href="#@net:signal_process/demodu/sample_sum_DY[6]:EBR_R25C46.DO24:R24C46D.A1:0.982"> EBR_R25C46.DO24 to R24C46D.A1    </A> <A href="#@net:signal_process/demodu/sample_sum_DY[6]">signal_process/demodu/sample_sum_DY[6]</A>
C1TOFCO_DE  ---     0.401     R24C46D.A1 to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOFCO_D  ---     0.063    R24C52C.FCI to    R24C52C.FCO <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_52:R24C52C.FCO:R24C52D.FCI:0.000">    R24C52C.FCO to R24C52D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_52">signal_process/demodu/INS_dout_1_cry_52</A>
FCITOF1_DE  ---     0.412    R24C52D.FCI to     R24C52D.F1 <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/SLICE_174</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[54]:R24C52D.F1:R24C52D.DI1:0.000">     R24C52D.F1 to R24C52D.DI1   </A> <A href="#@net:signal_process/demodu/INS_dout_1[54]">signal_process/demodu/INS_dout_1[54]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.223   (88.1% logic, 11.9% route), 26 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52D.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52D.CLK:1.890">  PLL_BR0.CLKOP to R24C52D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.259ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/INS_dout[52]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.223ns  (88.1% logic, 11.9% route), 26 logic levels.

 Constraint Details:

      8.223ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_173 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 0.259ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO22,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.982,EBR_R25C46.DO22,R24C46C.A1,signal_process/demodu/sample_sum_DY[4]:C1TOFCO_DEL, 0.401,R24C46C.A1,R24C46C.FCO,signal_process/demodu/SLICE_149:ROUTE, 0.000,R24C46C.FCO,R24C46D.FCI,signal_process/demodu/INS_dout_1_cry_4:FCITOFCO_DEL, 0.063,R24C46D.FCI,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOF1_DEL, 0.412,R24C52C.FCI,R24C52C.F1,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.F1,R24C52C.DI1,signal_process/demodu/INS_dout_1[52]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_173:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO22 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.982<A href="#@net:signal_process/demodu/sample_sum_DY[4]:EBR_R25C46.DO22:R24C46C.A1:0.982"> EBR_R25C46.DO22 to R24C46C.A1    </A> <A href="#@net:signal_process/demodu/sample_sum_DY[4]">signal_process/demodu/sample_sum_DY[4]</A>
C1TOFCO_DE  ---     0.401     R24C46C.A1 to    R24C46C.FCO <A href="#@comp:signal_process/demodu/SLICE_149">signal_process/demodu/SLICE_149</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_4:R24C46C.FCO:R24C46D.FCI:0.000">    R24C46C.FCO to R24C46D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_4">signal_process/demodu/INS_dout_1_cry_4</A>
FCITOFCO_D  ---     0.063    R24C46D.FCI to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOF1_DE  ---     0.412    R24C52C.FCI to     R24C52C.F1 <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[52]:R24C52C.F1:R24C52C.DI1:0.000">     R24C52C.F1 to R24C52C.DI1   </A> <A href="#@net:signal_process/demodu/INS_dout_1[52]">signal_process/demodu/INS_dout_1[52]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.223   (88.1% logic, 11.9% route), 26 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52C.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_173:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52C.CLK:1.890">  PLL_BR0.CLKOP to R24C52C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.268ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/INS_dout[51]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.211ns  (88.6% logic, 11.4% route), 27 logic levels.

 Constraint Details:

      8.211ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_173 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.479ns) by 0.268ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO20,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.934,EBR_R25C46.DO20,R24C46B.A1,signal_process/demodu/sample_sum_DY[2]:C1TOFCO_DEL, 0.401,R24C46B.A1,R24C46B.FCO,signal_process/demodu/SLICE_148:ROUTE, 0.000,R24C46B.FCO,R24C46C.FCI,signal_process/demodu/INS_dout_1_cry_2:FCITOFCO_DEL, 0.063,R24C46C.FCI,R24C46C.FCO,signal_process/demodu/SLICE_149:ROUTE, 0.000,R24C46C.FCO,R24C46D.FCI,signal_process/demodu/INS_dout_1_cry_4:FCITOFCO_DEL, 0.063,R24C46D.FCI,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOF0_DEL, 0.385,R24C52C.FCI,R24C52C.F0,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.F0,R24C52C.DI0,signal_process/demodu/INS_dout_1[51]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_173:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO20 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.934<A href="#@net:signal_process/demodu/sample_sum_DY[2]:EBR_R25C46.DO20:R24C46B.A1:0.934"> EBR_R25C46.DO20 to R24C46B.A1    </A> <A href="#@net:signal_process/demodu/sample_sum_DY[2]">signal_process/demodu/sample_sum_DY[2]</A>
C1TOFCO_DE  ---     0.401     R24C46B.A1 to    R24C46B.FCO <A href="#@comp:signal_process/demodu/SLICE_148">signal_process/demodu/SLICE_148</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_2:R24C46B.FCO:R24C46C.FCI:0.000">    R24C46B.FCO to R24C46C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_2">signal_process/demodu/INS_dout_1_cry_2</A>
FCITOFCO_D  ---     0.063    R24C46C.FCI to    R24C46C.FCO <A href="#@comp:signal_process/demodu/SLICE_149">signal_process/demodu/SLICE_149</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_4:R24C46C.FCO:R24C46D.FCI:0.000">    R24C46C.FCO to R24C46D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_4">signal_process/demodu/INS_dout_1_cry_4</A>
FCITOFCO_D  ---     0.063    R24C46D.FCI to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOF0_DE  ---     0.385    R24C52C.FCI to     R24C52C.F0 <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[51]:R24C52C.F0:R24C52C.DI0:0.000">     R24C52C.F0 to R24C52C.DI0   </A> <A href="#@net:signal_process/demodu/INS_dout_1[51]">signal_process/demodu/INS_dout_1[51]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.211   (88.6% logic, 11.4% route), 27 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52C.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_173:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52C.CLK:1.890">  PLL_BR0.CLKOP to R24C52C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 0.272ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (from <A href="#@net:clk_AD">clk_AD</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/INS_dout[54]</A>  (to <A href="#@net:clk120mhz">clk120mhz</A> +)

   Delay:               8.210ns  (88.2% logic, 11.8% route), 26 logic levels.

 Constraint Details:

      8.210ns physical path delay signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174 meets
      8.333ns delay constraint less
      0.089ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.482ns) by 0.272ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:C2Q_DEL, 4.979,EBR_R25C46.CLKR,EBR_R25C46.DO23,signal_process/demodu/fifo/pdp_ram_0_0_1:ROUTE, 0.969,EBR_R25C46.DO23,R24C46D.A0,signal_process/demodu/sample_sum_DY[5]:C0TOFCO_DEL, 0.401,R24C46D.A0,R24C46D.FCO,signal_process/demodu/SLICE_150:ROUTE, 0.000,R24C46D.FCO,R24C47A.FCI,signal_process/demodu/INS_dout_1_cry_6:FCITOFCO_DEL, 0.063,R24C47A.FCI,R24C47A.FCO,signal_process/demodu/SLICE_151:ROUTE, 0.000,R24C47A.FCO,R24C47B.FCI,signal_process/demodu/INS_dout_1_cry_8:FCITOFCO_DEL, 0.063,R24C47B.FCI,R24C47B.FCO,signal_process/demodu/SLICE_152:ROUTE, 0.000,R24C47B.FCO,R24C47C.FCI,signal_process/demodu/INS_dout_1_cry_10:FCITOFCO_DEL, 0.063,R24C47C.FCI,R24C47C.FCO,signal_process/demodu/SLICE_153:ROUTE, 0.000,R24C47C.FCO,R24C47D.FCI,signal_process/demodu/INS_dout_1_cry_12:FCITOFCO_DEL, 0.063,R24C47D.FCI,R24C47D.FCO,signal_process/demodu/SLICE_154:ROUTE, 0.000,R24C47D.FCO,R24C48A.FCI,signal_process/demodu/INS_dout_1_cry_14:FCITOFCO_DEL, 0.063,R24C48A.FCI,R24C48A.FCO,signal_process/demodu/SLICE_155:ROUTE, 0.000,R24C48A.FCO,R24C48B.FCI,signal_process/demodu/INS_dout_1_cry_16:FCITOFCO_DEL, 0.063,R24C48B.FCI,R24C48B.FCO,signal_process/demodu/SLICE_156:ROUTE, 0.000,R24C48B.FCO,R24C48C.FCI,signal_process/demodu/INS_dout_1_cry_18:FCITOFCO_DEL, 0.063,R24C48C.FCI,R24C48C.FCO,signal_process/demodu/SLICE_157:ROUTE, 0.000,R24C48C.FCO,R24C48D.FCI,signal_process/demodu/INS_dout_1_cry_20:FCITOFCO_DEL, 0.063,R24C48D.FCI,R24C48D.FCO,signal_process/demodu/SLICE_158:ROUTE, 0.000,R24C48D.FCO,R24C49A.FCI,signal_process/demodu/INS_dout_1_cry_22:FCITOFCO_DEL, 0.063,R24C49A.FCI,R24C49A.FCO,signal_process/demodu/SLICE_159:ROUTE, 0.000,R24C49A.FCO,R24C49B.FCI,signal_process/demodu/INS_dout_1_cry_24:FCITOFCO_DEL, 0.063,R24C49B.FCI,R24C49B.FCO,signal_process/demodu/SLICE_160:ROUTE, 0.000,R24C49B.FCO,R24C49C.FCI,signal_process/demodu/INS_dout_1_cry_26:FCITOFCO_DEL, 0.063,R24C49C.FCI,R24C49C.FCO,signal_process/demodu/SLICE_161:ROUTE, 0.000,R24C49C.FCO,R24C49D.FCI,signal_process/demodu/INS_dout_1_cry_28:FCITOFCO_DEL, 0.063,R24C49D.FCI,R24C49D.FCO,signal_process/demodu/SLICE_162:ROUTE, 0.000,R24C49D.FCO,R24C50A.FCI,signal_process/demodu/INS_dout_1_cry_30:FCITOFCO_DEL, 0.063,R24C50A.FCI,R24C50A.FCO,signal_process/demodu/SLICE_163:ROUTE, 0.000,R24C50A.FCO,R24C50B.FCI,signal_process/demodu/INS_dout_1_cry_32:FCITOFCO_DEL, 0.063,R24C50B.FCI,R24C50B.FCO,signal_process/demodu/SLICE_164:ROUTE, 0.000,R24C50B.FCO,R24C50C.FCI,signal_process/demodu/INS_dout_1_cry_34:FCITOFCO_DEL, 0.063,R24C50C.FCI,R24C50C.FCO,signal_process/demodu/SLICE_165:ROUTE, 0.000,R24C50C.FCO,R24C50D.FCI,signal_process/demodu/INS_dout_1_cry_36:FCITOFCO_DEL, 0.063,R24C50D.FCI,R24C50D.FCO,signal_process/demodu/SLICE_166:ROUTE, 0.000,R24C50D.FCO,R24C51A.FCI,signal_process/demodu/INS_dout_1_cry_38:FCITOFCO_DEL, 0.063,R24C51A.FCI,R24C51A.FCO,signal_process/demodu/SLICE_167:ROUTE, 0.000,R24C51A.FCO,R24C51B.FCI,signal_process/demodu/INS_dout_1_cry_40:FCITOFCO_DEL, 0.063,R24C51B.FCI,R24C51B.FCO,signal_process/demodu/SLICE_168:ROUTE, 0.000,R24C51B.FCO,R24C51C.FCI,signal_process/demodu/INS_dout_1_cry_42:FCITOFCO_DEL, 0.063,R24C51C.FCI,R24C51C.FCO,signal_process/demodu/SLICE_169:ROUTE, 0.000,R24C51C.FCO,R24C51D.FCI,signal_process/demodu/INS_dout_1_cry_44:FCITOFCO_DEL, 0.063,R24C51D.FCI,R24C51D.FCO,signal_process/demodu/SLICE_170:ROUTE, 0.000,R24C51D.FCO,R24C52A.FCI,signal_process/demodu/INS_dout_1_cry_46:FCITOFCO_DEL, 0.063,R24C52A.FCI,R24C52A.FCO,signal_process/demodu/SLICE_171:ROUTE, 0.000,R24C52A.FCO,R24C52B.FCI,signal_process/demodu/INS_dout_1_cry_48:FCITOFCO_DEL, 0.063,R24C52B.FCI,R24C52B.FCO,signal_process/demodu/SLICE_172:ROUTE, 0.000,R24C52B.FCO,R24C52C.FCI,signal_process/demodu/INS_dout_1_cry_50:FCITOFCO_DEL, 0.063,R24C52C.FCI,R24C52C.FCO,signal_process/demodu/SLICE_173:ROUTE, 0.000,R24C52C.FCO,R24C52D.FCI,signal_process/demodu/INS_dout_1_cry_52:FCITOF1_DEL, 0.412,R24C52D.FCI,R24C52D.F1,signal_process/demodu/SLICE_174:ROUTE, 0.000,R24C52D.F1,R24C52D.DI1,signal_process/demodu/INS_dout_1[54]">Data path</A> signal_process/demodu/fifo/pdp_ram_0_0_1 to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
C2Q_DEL     ---     4.979 EBR_R25C46.CLKR to EBR_R25C46.DO23 <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A> (from <A href="#@net:clk_AD">clk_AD</A>)
ROUTE         1     0.969<A href="#@net:signal_process/demodu/sample_sum_DY[5]:EBR_R25C46.DO23:R24C46D.A0:0.969"> EBR_R25C46.DO23 to R24C46D.A0    </A> <A href="#@net:signal_process/demodu/sample_sum_DY[5]">signal_process/demodu/sample_sum_DY[5]</A>
C0TOFCO_DE  ---     0.401     R24C46D.A0 to    R24C46D.FCO <A href="#@comp:signal_process/demodu/SLICE_150">signal_process/demodu/SLICE_150</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_6:R24C46D.FCO:R24C47A.FCI:0.000">    R24C46D.FCO to R24C47A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_6">signal_process/demodu/INS_dout_1_cry_6</A>
FCITOFCO_D  ---     0.063    R24C47A.FCI to    R24C47A.FCO <A href="#@comp:signal_process/demodu/SLICE_151">signal_process/demodu/SLICE_151</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_8:R24C47A.FCO:R24C47B.FCI:0.000">    R24C47A.FCO to R24C47B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_8">signal_process/demodu/INS_dout_1_cry_8</A>
FCITOFCO_D  ---     0.063    R24C47B.FCI to    R24C47B.FCO <A href="#@comp:signal_process/demodu/SLICE_152">signal_process/demodu/SLICE_152</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_10:R24C47B.FCO:R24C47C.FCI:0.000">    R24C47B.FCO to R24C47C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_10">signal_process/demodu/INS_dout_1_cry_10</A>
FCITOFCO_D  ---     0.063    R24C47C.FCI to    R24C47C.FCO <A href="#@comp:signal_process/demodu/SLICE_153">signal_process/demodu/SLICE_153</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_12:R24C47C.FCO:R24C47D.FCI:0.000">    R24C47C.FCO to R24C47D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_12">signal_process/demodu/INS_dout_1_cry_12</A>
FCITOFCO_D  ---     0.063    R24C47D.FCI to    R24C47D.FCO <A href="#@comp:signal_process/demodu/SLICE_154">signal_process/demodu/SLICE_154</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_14:R24C47D.FCO:R24C48A.FCI:0.000">    R24C47D.FCO to R24C48A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_14">signal_process/demodu/INS_dout_1_cry_14</A>
FCITOFCO_D  ---     0.063    R24C48A.FCI to    R24C48A.FCO <A href="#@comp:signal_process/demodu/SLICE_155">signal_process/demodu/SLICE_155</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_16:R24C48A.FCO:R24C48B.FCI:0.000">    R24C48A.FCO to R24C48B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_16">signal_process/demodu/INS_dout_1_cry_16</A>
FCITOFCO_D  ---     0.063    R24C48B.FCI to    R24C48B.FCO <A href="#@comp:signal_process/demodu/SLICE_156">signal_process/demodu/SLICE_156</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_18:R24C48B.FCO:R24C48C.FCI:0.000">    R24C48B.FCO to R24C48C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_18">signal_process/demodu/INS_dout_1_cry_18</A>
FCITOFCO_D  ---     0.063    R24C48C.FCI to    R24C48C.FCO <A href="#@comp:signal_process/demodu/SLICE_157">signal_process/demodu/SLICE_157</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_20:R24C48C.FCO:R24C48D.FCI:0.000">    R24C48C.FCO to R24C48D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_20">signal_process/demodu/INS_dout_1_cry_20</A>
FCITOFCO_D  ---     0.063    R24C48D.FCI to    R24C48D.FCO <A href="#@comp:signal_process/demodu/SLICE_158">signal_process/demodu/SLICE_158</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_22:R24C48D.FCO:R24C49A.FCI:0.000">    R24C48D.FCO to R24C49A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_22">signal_process/demodu/INS_dout_1_cry_22</A>
FCITOFCO_D  ---     0.063    R24C49A.FCI to    R24C49A.FCO <A href="#@comp:signal_process/demodu/SLICE_159">signal_process/demodu/SLICE_159</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_24:R24C49A.FCO:R24C49B.FCI:0.000">    R24C49A.FCO to R24C49B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_24">signal_process/demodu/INS_dout_1_cry_24</A>
FCITOFCO_D  ---     0.063    R24C49B.FCI to    R24C49B.FCO <A href="#@comp:signal_process/demodu/SLICE_160">signal_process/demodu/SLICE_160</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_26:R24C49B.FCO:R24C49C.FCI:0.000">    R24C49B.FCO to R24C49C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_26">signal_process/demodu/INS_dout_1_cry_26</A>
FCITOFCO_D  ---     0.063    R24C49C.FCI to    R24C49C.FCO <A href="#@comp:signal_process/demodu/SLICE_161">signal_process/demodu/SLICE_161</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_28:R24C49C.FCO:R24C49D.FCI:0.000">    R24C49C.FCO to R24C49D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_28">signal_process/demodu/INS_dout_1_cry_28</A>
FCITOFCO_D  ---     0.063    R24C49D.FCI to    R24C49D.FCO <A href="#@comp:signal_process/demodu/SLICE_162">signal_process/demodu/SLICE_162</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_30:R24C49D.FCO:R24C50A.FCI:0.000">    R24C49D.FCO to R24C50A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_30">signal_process/demodu/INS_dout_1_cry_30</A>
FCITOFCO_D  ---     0.063    R24C50A.FCI to    R24C50A.FCO <A href="#@comp:signal_process/demodu/SLICE_163">signal_process/demodu/SLICE_163</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_32:R24C50A.FCO:R24C50B.FCI:0.000">    R24C50A.FCO to R24C50B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_32">signal_process/demodu/INS_dout_1_cry_32</A>
FCITOFCO_D  ---     0.063    R24C50B.FCI to    R24C50B.FCO <A href="#@comp:signal_process/demodu/SLICE_164">signal_process/demodu/SLICE_164</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_34:R24C50B.FCO:R24C50C.FCI:0.000">    R24C50B.FCO to R24C50C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_34">signal_process/demodu/INS_dout_1_cry_34</A>
FCITOFCO_D  ---     0.063    R24C50C.FCI to    R24C50C.FCO <A href="#@comp:signal_process/demodu/SLICE_165">signal_process/demodu/SLICE_165</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_36:R24C50C.FCO:R24C50D.FCI:0.000">    R24C50C.FCO to R24C50D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_36">signal_process/demodu/INS_dout_1_cry_36</A>
FCITOFCO_D  ---     0.063    R24C50D.FCI to    R24C50D.FCO <A href="#@comp:signal_process/demodu/SLICE_166">signal_process/demodu/SLICE_166</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_38:R24C50D.FCO:R24C51A.FCI:0.000">    R24C50D.FCO to R24C51A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_38">signal_process/demodu/INS_dout_1_cry_38</A>
FCITOFCO_D  ---     0.063    R24C51A.FCI to    R24C51A.FCO <A href="#@comp:signal_process/demodu/SLICE_167">signal_process/demodu/SLICE_167</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_40:R24C51A.FCO:R24C51B.FCI:0.000">    R24C51A.FCO to R24C51B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_40">signal_process/demodu/INS_dout_1_cry_40</A>
FCITOFCO_D  ---     0.063    R24C51B.FCI to    R24C51B.FCO <A href="#@comp:signal_process/demodu/SLICE_168">signal_process/demodu/SLICE_168</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_42:R24C51B.FCO:R24C51C.FCI:0.000">    R24C51B.FCO to R24C51C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_42">signal_process/demodu/INS_dout_1_cry_42</A>
FCITOFCO_D  ---     0.063    R24C51C.FCI to    R24C51C.FCO <A href="#@comp:signal_process/demodu/SLICE_169">signal_process/demodu/SLICE_169</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_44:R24C51C.FCO:R24C51D.FCI:0.000">    R24C51C.FCO to R24C51D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_44">signal_process/demodu/INS_dout_1_cry_44</A>
FCITOFCO_D  ---     0.063    R24C51D.FCI to    R24C51D.FCO <A href="#@comp:signal_process/demodu/SLICE_170">signal_process/demodu/SLICE_170</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_46:R24C51D.FCO:R24C52A.FCI:0.000">    R24C51D.FCO to R24C52A.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_46">signal_process/demodu/INS_dout_1_cry_46</A>
FCITOFCO_D  ---     0.063    R24C52A.FCI to    R24C52A.FCO <A href="#@comp:signal_process/demodu/SLICE_171">signal_process/demodu/SLICE_171</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_48:R24C52A.FCO:R24C52B.FCI:0.000">    R24C52A.FCO to R24C52B.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_48">signal_process/demodu/INS_dout_1_cry_48</A>
FCITOFCO_D  ---     0.063    R24C52B.FCI to    R24C52B.FCO <A href="#@comp:signal_process/demodu/SLICE_172">signal_process/demodu/SLICE_172</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_50:R24C52B.FCO:R24C52C.FCI:0.000">    R24C52B.FCO to R24C52C.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_50">signal_process/demodu/INS_dout_1_cry_50</A>
FCITOFCO_D  ---     0.063    R24C52C.FCI to    R24C52C.FCO <A href="#@comp:signal_process/demodu/SLICE_173">signal_process/demodu/SLICE_173</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1_cry_52:R24C52C.FCO:R24C52D.FCI:0.000">    R24C52C.FCO to R24C52D.FCI   </A> <A href="#@net:signal_process/demodu/INS_dout_1_cry_52">signal_process/demodu/INS_dout_1_cry_52</A>
FCITOF1_DE  ---     0.412    R24C52D.FCI to     R24C52D.F1 <A href="#@comp:signal_process/demodu/SLICE_174">signal_process/demodu/SLICE_174</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/INS_dout_1[54]:R24C52D.F1:R24C52D.DI1:0.000">     R24C52D.F1 to R24C52D.DI1   </A> <A href="#@net:signal_process/demodu/INS_dout_1[54]">signal_process/demodu/INS_dout_1[54]</A> (to <A href="#@net:clk120mhz">clk120mhz</A>)
                  --------
                    8.210   (88.2% logic, 11.8% route), 26 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Source Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk120mhz' 120.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R24C52D.CLK,clk120mhz">Destination Clock Path</A> clk_in to signal_process/demodu/SLICE_174:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R24C52D.CLK:1.890">  PLL_BR0.CLKOP to R24C52D.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

Report:  122.669MHz is the maximum frequency for this preference.


</A><A name="FREQUENCY NET 'wendu.clk_us' 1.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;
            10 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------
<font color=#000000> 

Passed: The following path meets requirements by 990.838ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_537">wendu/cnt_us[14]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_538">wendu/cnt_us[16]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.400ns  (35.9% logic, 64.1% route), 18 logic levels.

 Constraint Details:

      9.400ns physical path delay wendu/SLICE_537 to wendu/SLICE_538 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.838ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.457,R33C17C.CLK,R33C17C.Q1,wendu/SLICE_537:ROUTE, 0.701,R33C17C.Q1,R33C18A.A1,wendu/cnt_us[14]:CTOF_DEL, 0.208,R33C18A.A1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOFCO_DEL, 0.063,R34C17B.FCI,R34C17B.FCO,wendu/SLICE_236:ROUTE, 0.000,R34C17B.FCO,R34C17C.FCI,wendu/un1_cnt_us_18_cry_10:FCITOFCO_DEL, 0.063,R34C17C.FCI,R34C17C.FCO,wendu/SLICE_237:ROUTE, 0.000,R34C17C.FCO,R34C17D.FCI,wendu/un1_cnt_us_18_cry_12:FCITOFCO_DEL, 0.063,R34C17D.FCI,R34C17D.FCO,wendu/SLICE_238:ROUTE, 0.000,R34C17D.FCO,R34C18A.FCI,wendu/un1_cnt_us_18_cry_14:FCITOF1_DEL, 0.412,R34C18A.FCI,R34C18A.F1,wendu/SLICE_239:ROUTE, 0.682,R34C18A.F1,R33C18D.A1,wendu/un1_cnt_us_18_cry_15_0_S1:CTOF_DEL, 0.208,R33C18D.A1,R33C18D.F1,wendu/SLICE_538:ROUTE, 0.000,R33C18D.F1,R33C18D.DI1,wendu/cnt_us_12[16]">Data path</A> wendu/SLICE_537 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R33C17C.CLK to     R33C17C.Q1 <A href="#@comp:wendu/SLICE_537">wendu/SLICE_537</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.701<A href="#@net:wendu/cnt_us[14]:R33C17C.Q1:R33C18A.A1:0.701">     R33C17C.Q1 to R33C18A.A1    </A> <A href="#@net:wendu/cnt_us[14]">wendu/cnt_us[14]</A>
CTOF_DEL    ---     0.208     R33C18A.A1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOFCO_D  ---     0.063    R34C17B.FCI to    R34C17B.FCO <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_10:R34C17B.FCO:R34C17C.FCI:0.000">    R34C17B.FCO to R34C17C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_10">wendu/un1_cnt_us_18_cry_10</A>
FCITOFCO_D  ---     0.063    R34C17C.FCI to    R34C17C.FCO <A href="#@comp:wendu/SLICE_237">wendu/SLICE_237</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_12:R34C17C.FCO:R34C17D.FCI:0.000">    R34C17C.FCO to R34C17D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_12">wendu/un1_cnt_us_18_cry_12</A>
FCITOFCO_D  ---     0.063    R34C17D.FCI to    R34C17D.FCO <A href="#@comp:wendu/SLICE_238">wendu/SLICE_238</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_14:R34C17D.FCO:R34C18A.FCI:0.000">    R34C17D.FCO to R34C18A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_14">wendu/un1_cnt_us_18_cry_14</A>
FCITOF1_DE  ---     0.412    R34C18A.FCI to     R34C18A.F1 <A href="#@comp:wendu/SLICE_239">wendu/SLICE_239</A>
ROUTE         1     0.682<A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S1:R34C18A.F1:R33C18D.A1:0.682">     R34C18A.F1 to R33C18D.A1    </A> <A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S1">wendu/un1_cnt_us_18_cry_15_0_S1</A>
CTOF_DEL    ---     0.208     R33C18D.A1 to     R33C18D.F1 <A href="#@comp:wendu/SLICE_538">wendu/SLICE_538</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[16]:R33C18D.F1:R33C18D.DI1:0.000">     R33C18D.F1 to R33C18D.DI1   </A> <A href="#@net:wendu/cnt_us_12[16]">wendu/cnt_us_12[16]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.400   (35.9% logic, 64.1% route), 18 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C17C.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_537:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C17C.CLK:2.392">     R38C31A.Q0 to R33C17C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18D.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18D.CLK:2.392">     R38C31A.Q0 to R33C18D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 990.878ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_538">wendu/cnt_us[15]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_538">wendu/cnt_us[16]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.360ns  (36.1% logic, 63.9% route), 18 logic levels.

 Constraint Details:

      9.360ns physical path delay wendu/SLICE_538 to wendu/SLICE_538 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.878ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.460,R33C18D.CLK,R33C18D.Q0,wendu/SLICE_538:ROUTE, 0.658,R33C18D.Q0,R33C18A.B1,wendu/cnt_us[15]:CTOF_DEL, 0.208,R33C18A.B1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOFCO_DEL, 0.063,R34C17B.FCI,R34C17B.FCO,wendu/SLICE_236:ROUTE, 0.000,R34C17B.FCO,R34C17C.FCI,wendu/un1_cnt_us_18_cry_10:FCITOFCO_DEL, 0.063,R34C17C.FCI,R34C17C.FCO,wendu/SLICE_237:ROUTE, 0.000,R34C17C.FCO,R34C17D.FCI,wendu/un1_cnt_us_18_cry_12:FCITOFCO_DEL, 0.063,R34C17D.FCI,R34C17D.FCO,wendu/SLICE_238:ROUTE, 0.000,R34C17D.FCO,R34C18A.FCI,wendu/un1_cnt_us_18_cry_14:FCITOF1_DEL, 0.412,R34C18A.FCI,R34C18A.F1,wendu/SLICE_239:ROUTE, 0.682,R34C18A.F1,R33C18D.A1,wendu/un1_cnt_us_18_cry_15_0_S1:CTOF_DEL, 0.208,R33C18D.A1,R33C18D.F1,wendu/SLICE_538:ROUTE, 0.000,R33C18D.F1,R33C18D.DI1,wendu/cnt_us_12[16]">Data path</A> wendu/SLICE_538 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R33C18D.CLK to     R33C18D.Q0 <A href="#@comp:wendu/SLICE_538">wendu/SLICE_538</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.658<A href="#@net:wendu/cnt_us[15]:R33C18D.Q0:R33C18A.B1:0.658">     R33C18D.Q0 to R33C18A.B1    </A> <A href="#@net:wendu/cnt_us[15]">wendu/cnt_us[15]</A>
CTOF_DEL    ---     0.208     R33C18A.B1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOFCO_D  ---     0.063    R34C17B.FCI to    R34C17B.FCO <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_10:R34C17B.FCO:R34C17C.FCI:0.000">    R34C17B.FCO to R34C17C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_10">wendu/un1_cnt_us_18_cry_10</A>
FCITOFCO_D  ---     0.063    R34C17C.FCI to    R34C17C.FCO <A href="#@comp:wendu/SLICE_237">wendu/SLICE_237</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_12:R34C17C.FCO:R34C17D.FCI:0.000">    R34C17C.FCO to R34C17D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_12">wendu/un1_cnt_us_18_cry_12</A>
FCITOFCO_D  ---     0.063    R34C17D.FCI to    R34C17D.FCO <A href="#@comp:wendu/SLICE_238">wendu/SLICE_238</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_14:R34C17D.FCO:R34C18A.FCI:0.000">    R34C17D.FCO to R34C18A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_14">wendu/un1_cnt_us_18_cry_14</A>
FCITOF1_DE  ---     0.412    R34C18A.FCI to     R34C18A.F1 <A href="#@comp:wendu/SLICE_239">wendu/SLICE_239</A>
ROUTE         1     0.682<A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S1:R34C18A.F1:R33C18D.A1:0.682">     R34C18A.F1 to R33C18D.A1    </A> <A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S1">wendu/un1_cnt_us_18_cry_15_0_S1</A>
CTOF_DEL    ---     0.208     R33C18D.A1 to     R33C18D.F1 <A href="#@comp:wendu/SLICE_538">wendu/SLICE_538</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[16]:R33C18D.F1:R33C18D.DI1:0.000">     R33C18D.F1 to R33C18D.DI1   </A> <A href="#@net:wendu/cnt_us_12[16]">wendu/cnt_us_12[16]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.360   (36.1% logic, 63.9% route), 18 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18D.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18D.CLK:2.392">     R38C31A.Q0 to R33C18D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18D.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18D.CLK:2.392">     R38C31A.Q0 to R33C18D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 990.891ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_537">wendu/cnt_us[14]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_539">wendu/cnt_us[19]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.347ns  (37.2% logic, 62.8% route), 20 logic levels.

 Constraint Details:

      9.347ns physical path delay wendu/SLICE_537 to wendu/SLICE_539 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.891ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.457,R33C17C.CLK,R33C17C.Q1,wendu/SLICE_537:ROUTE, 0.701,R33C17C.Q1,R33C18A.A1,wendu/cnt_us[14]:CTOF_DEL, 0.208,R33C18A.A1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOFCO_DEL, 0.063,R34C17B.FCI,R34C17B.FCO,wendu/SLICE_236:ROUTE, 0.000,R34C17B.FCO,R34C17C.FCI,wendu/un1_cnt_us_18_cry_10:FCITOFCO_DEL, 0.063,R34C17C.FCI,R34C17C.FCO,wendu/SLICE_237:ROUTE, 0.000,R34C17C.FCO,R34C17D.FCI,wendu/un1_cnt_us_18_cry_12:FCITOFCO_DEL, 0.063,R34C17D.FCI,R34C17D.FCO,wendu/SLICE_238:ROUTE, 0.000,R34C17D.FCO,R34C18A.FCI,wendu/un1_cnt_us_18_cry_14:FCITOFCO_DEL, 0.063,R34C18A.FCI,R34C18A.FCO,wendu/SLICE_239:ROUTE, 0.000,R34C18A.FCO,R34C18B.FCI,wendu/un1_cnt_us_18_cry_16:FCITOFCO_DEL, 0.063,R34C18B.FCI,R34C18B.FCO,wendu/SLICE_240:ROUTE, 0.000,R34C18B.FCO,R34C18C.FCI,wendu/un1_cnt_us_18_cry_18:FCITOF0_DEL, 0.385,R34C18C.FCI,R34C18C.F0,wendu/SLICE_225:ROUTE, 0.530,R34C18C.F0,R33C18B.C1,wendu/un1_cnt_us_18_s_19_0_S0:CTOF_DEL, 0.208,R33C18B.C1,R33C18B.F1,wendu/SLICE_539:ROUTE, 0.000,R33C18B.F1,R33C18B.DI1,wendu/cnt_us_12[19]">Data path</A> wendu/SLICE_537 to wendu/SLICE_539:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R33C17C.CLK to     R33C17C.Q1 <A href="#@comp:wendu/SLICE_537">wendu/SLICE_537</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.701<A href="#@net:wendu/cnt_us[14]:R33C17C.Q1:R33C18A.A1:0.701">     R33C17C.Q1 to R33C18A.A1    </A> <A href="#@net:wendu/cnt_us[14]">wendu/cnt_us[14]</A>
CTOF_DEL    ---     0.208     R33C18A.A1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOFCO_D  ---     0.063    R34C17B.FCI to    R34C17B.FCO <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_10:R34C17B.FCO:R34C17C.FCI:0.000">    R34C17B.FCO to R34C17C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_10">wendu/un1_cnt_us_18_cry_10</A>
FCITOFCO_D  ---     0.063    R34C17C.FCI to    R34C17C.FCO <A href="#@comp:wendu/SLICE_237">wendu/SLICE_237</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_12:R34C17C.FCO:R34C17D.FCI:0.000">    R34C17C.FCO to R34C17D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_12">wendu/un1_cnt_us_18_cry_12</A>
FCITOFCO_D  ---     0.063    R34C17D.FCI to    R34C17D.FCO <A href="#@comp:wendu/SLICE_238">wendu/SLICE_238</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_14:R34C17D.FCO:R34C18A.FCI:0.000">    R34C17D.FCO to R34C18A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_14">wendu/un1_cnt_us_18_cry_14</A>
FCITOFCO_D  ---     0.063    R34C18A.FCI to    R34C18A.FCO <A href="#@comp:wendu/SLICE_239">wendu/SLICE_239</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_16:R34C18A.FCO:R34C18B.FCI:0.000">    R34C18A.FCO to R34C18B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_16">wendu/un1_cnt_us_18_cry_16</A>
FCITOFCO_D  ---     0.063    R34C18B.FCI to    R34C18B.FCO <A href="#@comp:wendu/SLICE_240">wendu/SLICE_240</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_18:R34C18B.FCO:R34C18C.FCI:0.000">    R34C18B.FCO to R34C18C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_18">wendu/un1_cnt_us_18_cry_18</A>
FCITOF0_DE  ---     0.385    R34C18C.FCI to     R34C18C.F0 <A href="#@comp:wendu/SLICE_225">wendu/SLICE_225</A>
ROUTE         1     0.530<A href="#@net:wendu/un1_cnt_us_18_s_19_0_S0:R34C18C.F0:R33C18B.C1:0.530">     R34C18C.F0 to R33C18B.C1    </A> <A href="#@net:wendu/un1_cnt_us_18_s_19_0_S0">wendu/un1_cnt_us_18_s_19_0_S0</A>
CTOF_DEL    ---     0.208     R33C18B.C1 to     R33C18B.F1 <A href="#@comp:wendu/SLICE_539">wendu/SLICE_539</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[19]:R33C18B.F1:R33C18B.DI1:0.000">     R33C18B.F1 to R33C18B.DI1   </A> <A href="#@net:wendu/cnt_us_12[19]">wendu/cnt_us_12[19]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.347   (37.2% logic, 62.8% route), 20 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C17C.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_537:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C17C.CLK:2.392">     R38C31A.Q0 to R33C17C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18B.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_539:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18B.CLK:2.392">     R38C31A.Q0 to R33C18B.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 990.906ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_537">wendu/cnt_us[14]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_537">wendu/cnt_us[14]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.332ns  (35.5% logic, 64.5% route), 17 logic levels.

 Constraint Details:

      9.332ns physical path delay wendu/SLICE_537 to wendu/SLICE_537 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.906ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.457,R33C17C.CLK,R33C17C.Q1,wendu/SLICE_537:ROUTE, 0.701,R33C17C.Q1,R33C18A.A1,wendu/cnt_us[14]:CTOF_DEL, 0.208,R33C18A.A1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOFCO_DEL, 0.063,R34C17B.FCI,R34C17B.FCO,wendu/SLICE_236:ROUTE, 0.000,R34C17B.FCO,R34C17C.FCI,wendu/un1_cnt_us_18_cry_10:FCITOFCO_DEL, 0.063,R34C17C.FCI,R34C17C.FCO,wendu/SLICE_237:ROUTE, 0.000,R34C17C.FCO,R34C17D.FCI,wendu/un1_cnt_us_18_cry_12:FCITOF1_DEL, 0.412,R34C17D.FCI,R34C17D.F1,wendu/SLICE_238:ROUTE, 0.677,R34C17D.F1,R33C17C.B1,wendu/un1_cnt_us_18_cry_13_0_S1:CTOF_DEL, 0.208,R33C17C.B1,R33C17C.F1,wendu/SLICE_537:ROUTE, 0.000,R33C17C.F1,R33C17C.DI1,wendu/cnt_us_12[14]">Data path</A> wendu/SLICE_537 to wendu/SLICE_537:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R33C17C.CLK to     R33C17C.Q1 <A href="#@comp:wendu/SLICE_537">wendu/SLICE_537</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.701<A href="#@net:wendu/cnt_us[14]:R33C17C.Q1:R33C18A.A1:0.701">     R33C17C.Q1 to R33C18A.A1    </A> <A href="#@net:wendu/cnt_us[14]">wendu/cnt_us[14]</A>
CTOF_DEL    ---     0.208     R33C18A.A1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOFCO_D  ---     0.063    R34C17B.FCI to    R34C17B.FCO <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_10:R34C17B.FCO:R34C17C.FCI:0.000">    R34C17B.FCO to R34C17C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_10">wendu/un1_cnt_us_18_cry_10</A>
FCITOFCO_D  ---     0.063    R34C17C.FCI to    R34C17C.FCO <A href="#@comp:wendu/SLICE_237">wendu/SLICE_237</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_12:R34C17C.FCO:R34C17D.FCI:0.000">    R34C17C.FCO to R34C17D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_12">wendu/un1_cnt_us_18_cry_12</A>
FCITOF1_DE  ---     0.412    R34C17D.FCI to     R34C17D.F1 <A href="#@comp:wendu/SLICE_238">wendu/SLICE_238</A>
ROUTE         1     0.677<A href="#@net:wendu/un1_cnt_us_18_cry_13_0_S1:R34C17D.F1:R33C17C.B1:0.677">     R34C17D.F1 to R33C17C.B1    </A> <A href="#@net:wendu/un1_cnt_us_18_cry_13_0_S1">wendu/un1_cnt_us_18_cry_13_0_S1</A>
CTOF_DEL    ---     0.208     R33C17C.B1 to     R33C17C.F1 <A href="#@comp:wendu/SLICE_537">wendu/SLICE_537</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[14]:R33C17C.F1:R33C17C.DI1:0.000">     R33C17C.F1 to R33C17C.DI1   </A> <A href="#@net:wendu/cnt_us_12[14]">wendu/cnt_us_12[14]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.332   (35.5% logic, 64.5% route), 17 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C17C.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_537:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C17C.CLK:2.392">     R38C31A.Q0 to R33C17C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C17C.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_537:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C17C.CLK:2.392">     R38C31A.Q0 to R33C17C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 990.931ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_538">wendu/cnt_us[15]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_539">wendu/cnt_us[19]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.307ns  (37.4% logic, 62.6% route), 20 logic levels.

 Constraint Details:

      9.307ns physical path delay wendu/SLICE_538 to wendu/SLICE_539 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.931ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.460,R33C18D.CLK,R33C18D.Q0,wendu/SLICE_538:ROUTE, 0.658,R33C18D.Q0,R33C18A.B1,wendu/cnt_us[15]:CTOF_DEL, 0.208,R33C18A.B1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOFCO_DEL, 0.063,R34C17B.FCI,R34C17B.FCO,wendu/SLICE_236:ROUTE, 0.000,R34C17B.FCO,R34C17C.FCI,wendu/un1_cnt_us_18_cry_10:FCITOFCO_DEL, 0.063,R34C17C.FCI,R34C17C.FCO,wendu/SLICE_237:ROUTE, 0.000,R34C17C.FCO,R34C17D.FCI,wendu/un1_cnt_us_18_cry_12:FCITOFCO_DEL, 0.063,R34C17D.FCI,R34C17D.FCO,wendu/SLICE_238:ROUTE, 0.000,R34C17D.FCO,R34C18A.FCI,wendu/un1_cnt_us_18_cry_14:FCITOFCO_DEL, 0.063,R34C18A.FCI,R34C18A.FCO,wendu/SLICE_239:ROUTE, 0.000,R34C18A.FCO,R34C18B.FCI,wendu/un1_cnt_us_18_cry_16:FCITOFCO_DEL, 0.063,R34C18B.FCI,R34C18B.FCO,wendu/SLICE_240:ROUTE, 0.000,R34C18B.FCO,R34C18C.FCI,wendu/un1_cnt_us_18_cry_18:FCITOF0_DEL, 0.385,R34C18C.FCI,R34C18C.F0,wendu/SLICE_225:ROUTE, 0.530,R34C18C.F0,R33C18B.C1,wendu/un1_cnt_us_18_s_19_0_S0:CTOF_DEL, 0.208,R33C18B.C1,R33C18B.F1,wendu/SLICE_539:ROUTE, 0.000,R33C18B.F1,R33C18B.DI1,wendu/cnt_us_12[19]">Data path</A> wendu/SLICE_538 to wendu/SLICE_539:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R33C18D.CLK to     R33C18D.Q0 <A href="#@comp:wendu/SLICE_538">wendu/SLICE_538</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.658<A href="#@net:wendu/cnt_us[15]:R33C18D.Q0:R33C18A.B1:0.658">     R33C18D.Q0 to R33C18A.B1    </A> <A href="#@net:wendu/cnt_us[15]">wendu/cnt_us[15]</A>
CTOF_DEL    ---     0.208     R33C18A.B1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOFCO_D  ---     0.063    R34C17B.FCI to    R34C17B.FCO <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_10:R34C17B.FCO:R34C17C.FCI:0.000">    R34C17B.FCO to R34C17C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_10">wendu/un1_cnt_us_18_cry_10</A>
FCITOFCO_D  ---     0.063    R34C17C.FCI to    R34C17C.FCO <A href="#@comp:wendu/SLICE_237">wendu/SLICE_237</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_12:R34C17C.FCO:R34C17D.FCI:0.000">    R34C17C.FCO to R34C17D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_12">wendu/un1_cnt_us_18_cry_12</A>
FCITOFCO_D  ---     0.063    R34C17D.FCI to    R34C17D.FCO <A href="#@comp:wendu/SLICE_238">wendu/SLICE_238</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_14:R34C17D.FCO:R34C18A.FCI:0.000">    R34C17D.FCO to R34C18A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_14">wendu/un1_cnt_us_18_cry_14</A>
FCITOFCO_D  ---     0.063    R34C18A.FCI to    R34C18A.FCO <A href="#@comp:wendu/SLICE_239">wendu/SLICE_239</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_16:R34C18A.FCO:R34C18B.FCI:0.000">    R34C18A.FCO to R34C18B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_16">wendu/un1_cnt_us_18_cry_16</A>
FCITOFCO_D  ---     0.063    R34C18B.FCI to    R34C18B.FCO <A href="#@comp:wendu/SLICE_240">wendu/SLICE_240</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_18:R34C18B.FCO:R34C18C.FCI:0.000">    R34C18B.FCO to R34C18C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_18">wendu/un1_cnt_us_18_cry_18</A>
FCITOF0_DE  ---     0.385    R34C18C.FCI to     R34C18C.F0 <A href="#@comp:wendu/SLICE_225">wendu/SLICE_225</A>
ROUTE         1     0.530<A href="#@net:wendu/un1_cnt_us_18_s_19_0_S0:R34C18C.F0:R33C18B.C1:0.530">     R34C18C.F0 to R33C18B.C1    </A> <A href="#@net:wendu/un1_cnt_us_18_s_19_0_S0">wendu/un1_cnt_us_18_s_19_0_S0</A>
CTOF_DEL    ---     0.208     R33C18B.C1 to     R33C18B.F1 <A href="#@comp:wendu/SLICE_539">wendu/SLICE_539</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[19]:R33C18B.F1:R33C18B.DI1:0.000">     R33C18B.F1 to R33C18B.DI1   </A> <A href="#@net:wendu/cnt_us_12[19]">wendu/cnt_us_12[19]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.307   (37.4% logic, 62.6% route), 20 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18D.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18D.CLK:2.392">     R38C31A.Q0 to R33C18D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18B.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_539:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18B.CLK:2.392">     R38C31A.Q0 to R33C18B.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 990.946ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_538">wendu/cnt_us[15]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_537">wendu/cnt_us[14]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.292ns  (35.7% logic, 64.3% route), 17 logic levels.

 Constraint Details:

      9.292ns physical path delay wendu/SLICE_538 to wendu/SLICE_537 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 990.946ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.460,R33C18D.CLK,R33C18D.Q0,wendu/SLICE_538:ROUTE, 0.658,R33C18D.Q0,R33C18A.B1,wendu/cnt_us[15]:CTOF_DEL, 0.208,R33C18A.B1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOFCO_DEL, 0.063,R34C17B.FCI,R34C17B.FCO,wendu/SLICE_236:ROUTE, 0.000,R34C17B.FCO,R34C17C.FCI,wendu/un1_cnt_us_18_cry_10:FCITOFCO_DEL, 0.063,R34C17C.FCI,R34C17C.FCO,wendu/SLICE_237:ROUTE, 0.000,R34C17C.FCO,R34C17D.FCI,wendu/un1_cnt_us_18_cry_12:FCITOF1_DEL, 0.412,R34C17D.FCI,R34C17D.F1,wendu/SLICE_238:ROUTE, 0.677,R34C17D.F1,R33C17C.B1,wendu/un1_cnt_us_18_cry_13_0_S1:CTOF_DEL, 0.208,R33C17C.B1,R33C17C.F1,wendu/SLICE_537:ROUTE, 0.000,R33C17C.F1,R33C17C.DI1,wendu/cnt_us_12[14]">Data path</A> wendu/SLICE_538 to wendu/SLICE_537:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R33C18D.CLK to     R33C18D.Q0 <A href="#@comp:wendu/SLICE_538">wendu/SLICE_538</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.658<A href="#@net:wendu/cnt_us[15]:R33C18D.Q0:R33C18A.B1:0.658">     R33C18D.Q0 to R33C18A.B1    </A> <A href="#@net:wendu/cnt_us[15]">wendu/cnt_us[15]</A>
CTOF_DEL    ---     0.208     R33C18A.B1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOFCO_D  ---     0.063    R34C17B.FCI to    R34C17B.FCO <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_10:R34C17B.FCO:R34C17C.FCI:0.000">    R34C17B.FCO to R34C17C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_10">wendu/un1_cnt_us_18_cry_10</A>
FCITOFCO_D  ---     0.063    R34C17C.FCI to    R34C17C.FCO <A href="#@comp:wendu/SLICE_237">wendu/SLICE_237</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_12:R34C17C.FCO:R34C17D.FCI:0.000">    R34C17C.FCO to R34C17D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_12">wendu/un1_cnt_us_18_cry_12</A>
FCITOF1_DE  ---     0.412    R34C17D.FCI to     R34C17D.F1 <A href="#@comp:wendu/SLICE_238">wendu/SLICE_238</A>
ROUTE         1     0.677<A href="#@net:wendu/un1_cnt_us_18_cry_13_0_S1:R34C17D.F1:R33C17C.B1:0.677">     R34C17D.F1 to R33C17C.B1    </A> <A href="#@net:wendu/un1_cnt_us_18_cry_13_0_S1">wendu/un1_cnt_us_18_cry_13_0_S1</A>
CTOF_DEL    ---     0.208     R33C17C.B1 to     R33C17C.F1 <A href="#@comp:wendu/SLICE_537">wendu/SLICE_537</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[14]:R33C17C.F1:R33C17C.DI1:0.000">     R33C17C.F1 to R33C17C.DI1   </A> <A href="#@net:wendu/cnt_us_12[14]">wendu/cnt_us_12[14]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.292   (35.7% logic, 64.3% route), 17 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18D.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18D.CLK:2.392">     R38C31A.Q0 to R33C18D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C17C.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_537:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C17C.CLK:2.392">     R38C31A.Q0 to R33C17C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 991.014ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_537">wendu/cnt_us[14]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_538">wendu/cnt_us[15]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.221ns  (36.3% logic, 63.7% route), 18 logic levels.

 Constraint Details:

      9.221ns physical path delay wendu/SLICE_537 to wendu/SLICE_538 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.235ns DIN_SET requirement (totaling 1000.235ns) by 991.014ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.457,R33C17C.CLK,R33C17C.Q1,wendu/SLICE_537:ROUTE, 0.701,R33C17C.Q1,R33C18A.A1,wendu/cnt_us[14]:CTOF_DEL, 0.208,R33C18A.A1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOFCO_DEL, 0.063,R34C17B.FCI,R34C17B.FCO,wendu/SLICE_236:ROUTE, 0.000,R34C17B.FCO,R34C17C.FCI,wendu/un1_cnt_us_18_cry_10:FCITOFCO_DEL, 0.063,R34C17C.FCI,R34C17C.FCO,wendu/SLICE_237:ROUTE, 0.000,R34C17C.FCO,R34C17D.FCI,wendu/un1_cnt_us_18_cry_12:FCITOFCO_DEL, 0.063,R34C17D.FCI,R34C17D.FCO,wendu/SLICE_238:ROUTE, 0.000,R34C17D.FCO,R34C18A.FCI,wendu/un1_cnt_us_18_cry_14:FCITOF0_DEL, 0.385,R34C18A.FCI,R34C18A.F0,wendu/SLICE_239:ROUTE, 0.530,R34C18A.F0,R33C18D.C0,wendu/un1_cnt_us_18_cry_15_0_S0:CTOF_DEL, 0.208,R33C18D.C0,R33C18D.F0,wendu/SLICE_538:ROUTE, 0.000,R33C18D.F0,R33C18D.DI0,wendu/cnt_us_12[15]">Data path</A> wendu/SLICE_537 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R33C17C.CLK to     R33C17C.Q1 <A href="#@comp:wendu/SLICE_537">wendu/SLICE_537</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.701<A href="#@net:wendu/cnt_us[14]:R33C17C.Q1:R33C18A.A1:0.701">     R33C17C.Q1 to R33C18A.A1    </A> <A href="#@net:wendu/cnt_us[14]">wendu/cnt_us[14]</A>
CTOF_DEL    ---     0.208     R33C18A.A1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOFCO_D  ---     0.063    R34C17B.FCI to    R34C17B.FCO <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_10:R34C17B.FCO:R34C17C.FCI:0.000">    R34C17B.FCO to R34C17C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_10">wendu/un1_cnt_us_18_cry_10</A>
FCITOFCO_D  ---     0.063    R34C17C.FCI to    R34C17C.FCO <A href="#@comp:wendu/SLICE_237">wendu/SLICE_237</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_12:R34C17C.FCO:R34C17D.FCI:0.000">    R34C17C.FCO to R34C17D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_12">wendu/un1_cnt_us_18_cry_12</A>
FCITOFCO_D  ---     0.063    R34C17D.FCI to    R34C17D.FCO <A href="#@comp:wendu/SLICE_238">wendu/SLICE_238</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_14:R34C17D.FCO:R34C18A.FCI:0.000">    R34C17D.FCO to R34C18A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_14">wendu/un1_cnt_us_18_cry_14</A>
FCITOF0_DE  ---     0.385    R34C18A.FCI to     R34C18A.F0 <A href="#@comp:wendu/SLICE_239">wendu/SLICE_239</A>
ROUTE         1     0.530<A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S0:R34C18A.F0:R33C18D.C0:0.530">     R34C18A.F0 to R33C18D.C0    </A> <A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S0">wendu/un1_cnt_us_18_cry_15_0_S0</A>
CTOF_DEL    ---     0.208     R33C18D.C0 to     R33C18D.F0 <A href="#@comp:wendu/SLICE_538">wendu/SLICE_538</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[15]:R33C18D.F0:R33C18D.DI0:0.000">     R33C18D.F0 to R33C18D.DI0   </A> <A href="#@net:wendu/cnt_us_12[15]">wendu/cnt_us_12[15]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.221   (36.3% logic, 63.7% route), 18 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C17C.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_537:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C17C.CLK:2.392">     R38C31A.Q0 to R33C17C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18D.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18D.CLK:2.392">     R38C31A.Q0 to R33C18D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 991.020ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_539">wendu/cnt_us[17]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_538">wendu/cnt_us[16]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.218ns  (36.6% logic, 63.4% route), 18 logic levels.

 Constraint Details:

      9.218ns physical path delay wendu/SLICE_539 to wendu/SLICE_538 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.020ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.460,R33C18B.CLK,R33C18B.Q0,wendu/SLICE_539:ROUTE, 0.516,R33C18B.Q0,R33C18A.C1,wendu/cnt_us[17]:CTOF_DEL, 0.208,R33C18A.C1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOFCO_DEL, 0.063,R34C17B.FCI,R34C17B.FCO,wendu/SLICE_236:ROUTE, 0.000,R34C17B.FCO,R34C17C.FCI,wendu/un1_cnt_us_18_cry_10:FCITOFCO_DEL, 0.063,R34C17C.FCI,R34C17C.FCO,wendu/SLICE_237:ROUTE, 0.000,R34C17C.FCO,R34C17D.FCI,wendu/un1_cnt_us_18_cry_12:FCITOFCO_DEL, 0.063,R34C17D.FCI,R34C17D.FCO,wendu/SLICE_238:ROUTE, 0.000,R34C17D.FCO,R34C18A.FCI,wendu/un1_cnt_us_18_cry_14:FCITOF1_DEL, 0.412,R34C18A.FCI,R34C18A.F1,wendu/SLICE_239:ROUTE, 0.682,R34C18A.F1,R33C18D.A1,wendu/un1_cnt_us_18_cry_15_0_S1:CTOF_DEL, 0.208,R33C18D.A1,R33C18D.F1,wendu/SLICE_538:ROUTE, 0.000,R33C18D.F1,R33C18D.DI1,wendu/cnt_us_12[16]">Data path</A> wendu/SLICE_539 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R33C18B.CLK to     R33C18B.Q0 <A href="#@comp:wendu/SLICE_539">wendu/SLICE_539</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.516<A href="#@net:wendu/cnt_us[17]:R33C18B.Q0:R33C18A.C1:0.516">     R33C18B.Q0 to R33C18A.C1    </A> <A href="#@net:wendu/cnt_us[17]">wendu/cnt_us[17]</A>
CTOF_DEL    ---     0.208     R33C18A.C1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOFCO_D  ---     0.063    R34C17B.FCI to    R34C17B.FCO <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_10:R34C17B.FCO:R34C17C.FCI:0.000">    R34C17B.FCO to R34C17C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_10">wendu/un1_cnt_us_18_cry_10</A>
FCITOFCO_D  ---     0.063    R34C17C.FCI to    R34C17C.FCO <A href="#@comp:wendu/SLICE_237">wendu/SLICE_237</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_12:R34C17C.FCO:R34C17D.FCI:0.000">    R34C17C.FCO to R34C17D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_12">wendu/un1_cnt_us_18_cry_12</A>
FCITOFCO_D  ---     0.063    R34C17D.FCI to    R34C17D.FCO <A href="#@comp:wendu/SLICE_238">wendu/SLICE_238</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_14:R34C17D.FCO:R34C18A.FCI:0.000">    R34C17D.FCO to R34C18A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_14">wendu/un1_cnt_us_18_cry_14</A>
FCITOF1_DE  ---     0.412    R34C18A.FCI to     R34C18A.F1 <A href="#@comp:wendu/SLICE_239">wendu/SLICE_239</A>
ROUTE         1     0.682<A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S1:R34C18A.F1:R33C18D.A1:0.682">     R34C18A.F1 to R33C18D.A1    </A> <A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S1">wendu/un1_cnt_us_18_cry_15_0_S1</A>
CTOF_DEL    ---     0.208     R33C18D.A1 to     R33C18D.F1 <A href="#@comp:wendu/SLICE_538">wendu/SLICE_538</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[16]:R33C18D.F1:R33C18D.DI1:0.000">     R33C18D.F1 to R33C18D.DI1   </A> <A href="#@net:wendu/cnt_us_12[16]">wendu/cnt_us_12[16]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.218   (36.6% logic, 63.4% route), 18 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18B.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_539:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18B.CLK:2.392">     R38C31A.Q0 to R33C18B.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18D.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18D.CLK:2.392">     R38C31A.Q0 to R33C18D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 991.027ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_537">wendu/cnt_us[14]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_536">wendu/cnt_us[10]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.211ns  (34.6% logic, 65.4% route), 15 logic levels.

 Constraint Details:

      9.211ns physical path delay wendu/SLICE_537 to wendu/SLICE_536 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.238ns DIN_SET requirement (totaling 1000.238ns) by 991.027ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.457,R33C17C.CLK,R33C17C.Q1,wendu/SLICE_537:ROUTE, 0.701,R33C17C.Q1,R33C18A.A1,wendu/cnt_us[14]:CTOF_DEL, 0.208,R33C18A.A1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOF1_DEL, 0.412,R34C17B.FCI,R34C17B.F1,wendu/SLICE_236:ROUTE, 0.682,R34C17B.F1,R35C17D.A1,wendu/un1_cnt_us_18_cry_9_0_S1:CTOF_DEL, 0.208,R35C17D.A1,R35C17D.F1,wendu/SLICE_536:ROUTE, 0.000,R35C17D.F1,R35C17D.DI1,wendu/cnt_us_12[10]">Data path</A> wendu/SLICE_537 to wendu/SLICE_536:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.457    R33C17C.CLK to     R33C17C.Q1 <A href="#@comp:wendu/SLICE_537">wendu/SLICE_537</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.701<A href="#@net:wendu/cnt_us[14]:R33C17C.Q1:R33C18A.A1:0.701">     R33C17C.Q1 to R33C18A.A1    </A> <A href="#@net:wendu/cnt_us[14]">wendu/cnt_us[14]</A>
CTOF_DEL    ---     0.208     R33C18A.A1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOF1_DE  ---     0.412    R34C17B.FCI to     R34C17B.F1 <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.682<A href="#@net:wendu/un1_cnt_us_18_cry_9_0_S1:R34C17B.F1:R35C17D.A1:0.682">     R34C17B.F1 to R35C17D.A1    </A> <A href="#@net:wendu/un1_cnt_us_18_cry_9_0_S1">wendu/un1_cnt_us_18_cry_9_0_S1</A>
CTOF_DEL    ---     0.208     R35C17D.A1 to     R35C17D.F1 <A href="#@comp:wendu/SLICE_536">wendu/SLICE_536</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[10]:R35C17D.F1:R35C17D.DI1:0.000">     R35C17D.F1 to R35C17D.DI1   </A> <A href="#@net:wendu/cnt_us_12[10]">wendu/cnt_us_12[10]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.211   (34.6% logic, 65.4% route), 15 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C17C.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_537:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C17C.CLK:2.392">     R38C31A.Q0 to R33C17C.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R35C17D.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_536:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R35C17D.CLK:2.392">     R38C31A.Q0 to R35C17D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 991.054ns
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:wendu/SLICE_538">wendu/cnt_us[15]</A>  (from <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)
   Destination:    FF         Data in        <A href="#@comp:wendu/SLICE_538">wendu/cnt_us[15]</A>  (to <A href="#@net:wendu.clk_us">wendu.clk_us</A> +)

   Delay:               9.181ns  (36.5% logic, 63.5% route), 18 logic levels.

 Constraint Details:

      9.181ns physical path delay wendu/SLICE_538 to wendu/SLICE_538 meets
    1000.000ns delay constraint less
      0.000ns skew and
     -0.235ns DIN_SET requirement (totaling 1000.235ns) by 991.054ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:REG_DEL, 0.460,R33C18D.CLK,R33C18D.Q0,wendu/SLICE_538:ROUTE, 0.658,R33C18D.Q0,R33C18A.B1,wendu/cnt_us[15]:CTOF_DEL, 0.208,R33C18A.B1,R33C18A.F1,wendu/SLICE_591:ROUTE, 0.483,R33C18A.F1,R33C18A.B0,wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:CTOF_DEL, 0.208,R33C18A.B0,R33C18A.F0,wendu/SLICE_591:ROUTE, 0.395,R33C18A.F0,R35C18A.D1,wendu/cnt_us_RNISNS02[10]:CTOF_DEL, 0.208,R35C18A.D1,R35C18A.F1,wendu/SLICE_588:ROUTE, 0.493,R35C18A.F1,R35C18A.B0,wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:CTOF_DEL, 0.208,R35C18A.B0,R35C18A.F0,wendu/SLICE_588:ROUTE, 0.745,R35C18A.F0,R36C19D.C1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:CTOF_DEL, 0.208,R36C19D.C1,R36C19D.F1,wendu/SLICE_526:ROUTE, 0.507,R36C19D.F1,R36C19C.A1,wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:CTOF_DEL, 0.208,R36C19C.A1,R36C19C.F1,wendu/SLICE_597:ROUTE, 1.096,R36C19C.F1,R35C19B.A0,wendu/N_175_i:CTOF_DEL, 0.208,R35C19B.A0,R35C19B.F0,wendu/SLICE_556:ROUTE, 0.923,R35C19B.F0,R34C16A.B0,wendu/un1_cnt_us_0_sqmuxa_2_0_0:C0TOFCO_DEL, 0.401,R34C16A.B0,R34C16A.FCO,wendu/SLICE_231:ROUTE, 0.000,R34C16A.FCO,R34C16B.FCI,wendu/un1_cnt_us_18_cry_0:FCITOFCO_DEL, 0.063,R34C16B.FCI,R34C16B.FCO,wendu/SLICE_232:ROUTE, 0.000,R34C16B.FCO,R34C16C.FCI,wendu/un1_cnt_us_18_cry_2:FCITOFCO_DEL, 0.063,R34C16C.FCI,R34C16C.FCO,wendu/SLICE_233:ROUTE, 0.000,R34C16C.FCO,R34C16D.FCI,wendu/un1_cnt_us_18_cry_4:FCITOFCO_DEL, 0.063,R34C16D.FCI,R34C16D.FCO,wendu/SLICE_234:ROUTE, 0.000,R34C16D.FCO,R34C17A.FCI,wendu/un1_cnt_us_18_cry_6:FCITOFCO_DEL, 0.063,R34C17A.FCI,R34C17A.FCO,wendu/SLICE_235:ROUTE, 0.000,R34C17A.FCO,R34C17B.FCI,wendu/un1_cnt_us_18_cry_8:FCITOFCO_DEL, 0.063,R34C17B.FCI,R34C17B.FCO,wendu/SLICE_236:ROUTE, 0.000,R34C17B.FCO,R34C17C.FCI,wendu/un1_cnt_us_18_cry_10:FCITOFCO_DEL, 0.063,R34C17C.FCI,R34C17C.FCO,wendu/SLICE_237:ROUTE, 0.000,R34C17C.FCO,R34C17D.FCI,wendu/un1_cnt_us_18_cry_12:FCITOFCO_DEL, 0.063,R34C17D.FCI,R34C17D.FCO,wendu/SLICE_238:ROUTE, 0.000,R34C17D.FCO,R34C18A.FCI,wendu/un1_cnt_us_18_cry_14:FCITOF0_DEL, 0.385,R34C18A.FCI,R34C18A.F0,wendu/SLICE_239:ROUTE, 0.530,R34C18A.F0,R33C18D.C0,wendu/un1_cnt_us_18_cry_15_0_S0:CTOF_DEL, 0.208,R33C18D.C0,R33C18D.F0,wendu/SLICE_538:ROUTE, 0.000,R33C18D.F0,R33C18D.DI0,wendu/cnt_us_12[15]">Data path</A> wendu/SLICE_538 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R33C18D.CLK to     R33C18D.Q0 <A href="#@comp:wendu/SLICE_538">wendu/SLICE_538</A> (from <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
ROUTE         3     0.658<A href="#@net:wendu/cnt_us[15]:R33C18D.Q0:R33C18A.B1:0.658">     R33C18D.Q0 to R33C18A.B1    </A> <A href="#@net:wendu/cnt_us[15]">wendu/cnt_us[15]</A>
CTOF_DEL    ---     0.208     R33C18A.B1 to     R33C18A.F1 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         1     0.483<A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4:R33C18A.F1:R33C18A.B0:0.483">     R33C18A.F1 to R33C18A.B0    </A> <A href="#@net:wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4">wendu/un1_cur_state_5_0_93_i_a2_i_i_o3_3_4</A>
CTOF_DEL    ---     0.208     R33C18A.B0 to     R33C18A.F0 <A href="#@comp:wendu/SLICE_591">wendu/SLICE_591</A>
ROUTE         4     0.395<A href="#@net:wendu/cnt_us_RNISNS02[10]:R33C18A.F0:R35C18A.D1:0.395">     R33C18A.F0 to R35C18A.D1    </A> <A href="#@net:wendu/cnt_us_RNISNS02[10]">wendu/cnt_us_RNISNS02[10]</A>
CTOF_DEL    ---     0.208     R35C18A.D1 to     R35C18A.F1 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         3     0.493<A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1:R35C18A.F1:R35C18A.B0:0.493">     R35C18A.F1 to R35C18A.B0    </A> <A href="#@net:wendu/data_temp_1_sqmuxa_0_a2_0_o3_1">wendu/data_temp_1_sqmuxa_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R35C18A.B0 to     R35C18A.F0 <A href="#@comp:wendu/SLICE_588">wendu/SLICE_588</A>
ROUTE         2     0.745<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1:R35C18A.F0:R36C19D.C1:0.745">     R35C18A.F0 to R36C19D.C1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1</A>
CTOF_DEL    ---     0.208     R36C19D.C1 to     R36C19D.F1 <A href="#@comp:wendu/SLICE_526">wendu/SLICE_526</A>
ROUTE         3     0.507<A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91:R36C19D.F1:R36C19C.A1:0.507">     R36C19D.F1 to R36C19C.A1    </A> <A href="#@net:wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91">wendu/next_state_0_sqmuxa_1_0_a2_0_o3_1_RNIB0F91</A>
CTOF_DEL    ---     0.208     R36C19C.A1 to     R36C19C.F1 <A href="#@comp:wendu/SLICE_597">wendu/SLICE_597</A>
ROUTE         3     1.096<A href="#@net:wendu/N_175_i:R36C19C.F1:R35C19B.A0:1.096">     R36C19C.F1 to R35C19B.A0    </A> <A href="#@net:wendu/N_175_i">wendu/N_175_i</A>
CTOF_DEL    ---     0.208     R35C19B.A0 to     R35C19B.F0 <A href="#@comp:wendu/SLICE_556">wendu/SLICE_556</A>
ROUTE        17     0.923<A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0:R35C19B.F0:R34C16A.B0:0.923">     R35C19B.F0 to R34C16A.B0    </A> <A href="#@net:wendu/un1_cnt_us_0_sqmuxa_2_0_0">wendu/un1_cnt_us_0_sqmuxa_2_0_0</A>
C0TOFCO_DE  ---     0.401     R34C16A.B0 to    R34C16A.FCO <A href="#@comp:wendu/SLICE_231">wendu/SLICE_231</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_0:R34C16A.FCO:R34C16B.FCI:0.000">    R34C16A.FCO to R34C16B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_0">wendu/un1_cnt_us_18_cry_0</A>
FCITOFCO_D  ---     0.063    R34C16B.FCI to    R34C16B.FCO <A href="#@comp:wendu/SLICE_232">wendu/SLICE_232</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_2:R34C16B.FCO:R34C16C.FCI:0.000">    R34C16B.FCO to R34C16C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_2">wendu/un1_cnt_us_18_cry_2</A>
FCITOFCO_D  ---     0.063    R34C16C.FCI to    R34C16C.FCO <A href="#@comp:wendu/SLICE_233">wendu/SLICE_233</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_4:R34C16C.FCO:R34C16D.FCI:0.000">    R34C16C.FCO to R34C16D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_4">wendu/un1_cnt_us_18_cry_4</A>
FCITOFCO_D  ---     0.063    R34C16D.FCI to    R34C16D.FCO <A href="#@comp:wendu/SLICE_234">wendu/SLICE_234</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_6:R34C16D.FCO:R34C17A.FCI:0.000">    R34C16D.FCO to R34C17A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_6">wendu/un1_cnt_us_18_cry_6</A>
FCITOFCO_D  ---     0.063    R34C17A.FCI to    R34C17A.FCO <A href="#@comp:wendu/SLICE_235">wendu/SLICE_235</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_8:R34C17A.FCO:R34C17B.FCI:0.000">    R34C17A.FCO to R34C17B.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_8">wendu/un1_cnt_us_18_cry_8</A>
FCITOFCO_D  ---     0.063    R34C17B.FCI to    R34C17B.FCO <A href="#@comp:wendu/SLICE_236">wendu/SLICE_236</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_10:R34C17B.FCO:R34C17C.FCI:0.000">    R34C17B.FCO to R34C17C.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_10">wendu/un1_cnt_us_18_cry_10</A>
FCITOFCO_D  ---     0.063    R34C17C.FCI to    R34C17C.FCO <A href="#@comp:wendu/SLICE_237">wendu/SLICE_237</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_12:R34C17C.FCO:R34C17D.FCI:0.000">    R34C17C.FCO to R34C17D.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_12">wendu/un1_cnt_us_18_cry_12</A>
FCITOFCO_D  ---     0.063    R34C17D.FCI to    R34C17D.FCO <A href="#@comp:wendu/SLICE_238">wendu/SLICE_238</A>
ROUTE         1     0.000<A href="#@net:wendu/un1_cnt_us_18_cry_14:R34C17D.FCO:R34C18A.FCI:0.000">    R34C17D.FCO to R34C18A.FCI   </A> <A href="#@net:wendu/un1_cnt_us_18_cry_14">wendu/un1_cnt_us_18_cry_14</A>
FCITOF0_DE  ---     0.385    R34C18A.FCI to     R34C18A.F0 <A href="#@comp:wendu/SLICE_239">wendu/SLICE_239</A>
ROUTE         1     0.530<A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S0:R34C18A.F0:R33C18D.C0:0.530">     R34C18A.F0 to R33C18D.C0    </A> <A href="#@net:wendu/un1_cnt_us_18_cry_15_0_S0">wendu/un1_cnt_us_18_cry_15_0_S0</A>
CTOF_DEL    ---     0.208     R33C18D.C0 to     R33C18D.F0 <A href="#@comp:wendu/SLICE_538">wendu/SLICE_538</A>
ROUTE         1     0.000<A href="#@net:wendu/cnt_us_12[15]:R33C18D.F0:R33C18D.DI0:0.000">     R33C18D.F0 to R33C18D.DI0   </A> <A href="#@net:wendu/cnt_us_12[15]">wendu/cnt_us_12[15]</A> (to <A href="#@net:wendu.clk_us">wendu.clk_us</A>)
                  --------
                    9.181   (36.5% logic, 63.5% route), 18 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18D.CLK,wendu.clk_us">Source Clock Path</A> wendu/SLICE_241 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18D.CLK:2.392">     R38C31A.Q0 to R33C18D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

      <A href="#@path:FREQUENCY NET 'wendu.clk_us' 1.000000 MHz ;:ROUTE, 2.392,R38C31A.Q0,R33C18D.CLK,wendu.clk_us">Destination Clock Path</A> wendu/SLICE_241 to wendu/SLICE_538:

   Name    Fanout   Delay (ns)          Site               Resource
ROUTE        37     2.392<A href="#@net:wendu.clk_us:R38C31A.Q0:R33C18D.CLK:2.392">     R38C31A.Q0 to R33C18D.CLK   </A> <A href="#@net:wendu.clk_us">wendu.clk_us</A>
                  --------
                    2.392   (0.0% logic, 100.0% route), 0 logic levels.

Report:  109.146MHz is the maximum frequency for this preference.


</A><A name="FREQUENCY NET 'clk_in_c' 20.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "clk_in_c" 20.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


</A><A name="FREQUENCY NET 'clk_AD' 60.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "clk_AD" 60.000000 MHz ;
            10 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------
<font color=#000000> 

Passed: The following path meets requirements by 4.291ns (weighted slack = 8.582ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/fifo/SLICE_297">signal_process/demodu/fifo/FF_19</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               4.278ns  (43.3% logic, 56.7% route), 8 logic levels.

 Constraint Details:

      4.278ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_297 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.569ns) by 4.291ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.676,R42C48C.Q0,R42C48B.B0,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48B.B0,R42C48B.F0,signal_process/demodu/fifo/SLICE_617:ROUTE, 0.879,R42C48B.F0,R45C48B.B0,signal_process/demodu/fifo/rden_i:C0TOFCO_DEL, 0.401,R45C48B.B0,R45C48B.FCO,signal_process/demodu/fifo/SLICE_126:ROUTE, 0.000,R45C48B.FCO,R45C48C.FCI,signal_process/demodu/fifo/co0_1:FCITOFCO_DEL, 0.063,R45C48C.FCI,R45C48C.FCO,signal_process/demodu/fifo/SLICE_127:ROUTE, 0.000,R45C48C.FCO,R45C48D.FCI,signal_process/demodu/fifo/co1_1:FCITOFCO_DEL, 0.063,R45C48D.FCI,R45C48D.FCO,signal_process/demodu/fifo/SLICE_128:ROUTE, 0.000,R45C48D.FCO,R45C49A.FCI,signal_process/demodu/fifo/co2_1:FCITOFCO_DEL, 0.063,R45C49A.FCI,R45C49A.FCO,signal_process/demodu/fifo/SLICE_129:ROUTE, 0.000,R45C49A.FCO,R45C49B.FCI,signal_process/demodu/fifo/cmp_le_1_c:FCITOF0_DEL, 0.385,R45C49B.FCI,R45C49B.F0,signal_process/demodu/fifo/SLICE_130:ROUTE, 0.872,R45C49B.F0,R42C49A.B0,signal_process/demodu/fifo/cmp_le_1:CTOF_DEL, 0.208,R42C49A.B0,R42C49A.F0,signal_process/demodu/fifo/SLICE_297:ROUTE, 0.000,R42C49A.F0,R42C49A.DI0,signal_process/demodu/fifo/empty_d">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_297:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.676<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48B.B0:0.676">     R42C48C.Q0 to R42C48B.B0    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48B.B0 to     R42C48B.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_617">signal_process/demodu/fifo/SLICE_617</A>
ROUTE         9     0.879<A href="#@net:signal_process/demodu/fifo/rden_i:R42C48B.F0:R45C48B.B0:0.879">     R42C48B.F0 to R45C48B.B0    </A> <A href="#@net:signal_process/demodu/fifo/rden_i">signal_process/demodu/fifo/rden_i</A>
C0TOFCO_DE  ---     0.401     R45C48B.B0 to    R45C48B.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_126">signal_process/demodu/fifo/SLICE_126</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co0_1:R45C48B.FCO:R45C48C.FCI:0.000">    R45C48B.FCO to R45C48C.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co0_1">signal_process/demodu/fifo/co0_1</A>
FCITOFCO_D  ---     0.063    R45C48C.FCI to    R45C48C.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_127">signal_process/demodu/fifo/SLICE_127</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co1_1:R45C48C.FCO:R45C48D.FCI:0.000">    R45C48C.FCO to R45C48D.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co1_1">signal_process/demodu/fifo/co1_1</A>
FCITOFCO_D  ---     0.063    R45C48D.FCI to    R45C48D.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_128">signal_process/demodu/fifo/SLICE_128</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co2_1:R45C48D.FCO:R45C49A.FCI:0.000">    R45C48D.FCO to R45C49A.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co2_1">signal_process/demodu/fifo/co2_1</A>
FCITOFCO_D  ---     0.063    R45C49A.FCI to    R45C49A.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_129">signal_process/demodu/fifo/SLICE_129</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/cmp_le_1_c:R45C49A.FCO:R45C49B.FCI:0.000">    R45C49A.FCO to R45C49B.FCI   </A> <A href="#@net:signal_process/demodu/fifo/cmp_le_1_c">signal_process/demodu/fifo/cmp_le_1_c</A>
FCITOF0_DE  ---     0.385    R45C49B.FCI to     R45C49B.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_130">signal_process/demodu/fifo/SLICE_130</A>
ROUTE         1     0.872<A href="#@net:signal_process/demodu/fifo/cmp_le_1:R45C49B.F0:R42C49A.B0:0.872">     R45C49B.F0 to R42C49A.B0    </A> <A href="#@net:signal_process/demodu/fifo/cmp_le_1">signal_process/demodu/fifo/cmp_le_1</A>
CTOF_DEL    ---     0.208     R42C49A.B0 to     R42C49A.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_297">signal_process/demodu/fifo/SLICE_297</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/empty_d:R42C49A.F0:R42C49A.DI0:0.000">     R42C49A.F0 to R42C49A.DI0   </A> <A href="#@net:signal_process/demodu/fifo/empty_d">signal_process/demodu/fifo/empty_d</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    4.278   (43.3% logic, 56.7% route), 8 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOS,R42C49A.CLK,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/SLICE_297:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.890<A href="#@net:clk_AD:PLL_BR0.CLKOS:R42C49A.CLK:1.890">  PLL_BR0.CLKOS to R42C49A.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 4.414ns (weighted slack = 8.828ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               3.697ns  (18.1% logic, 81.9% route), 2 logic levels.

 Constraint Details:

      3.697ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      8.334ns delay constraint less
     -0.089ns skew and
      0.000ns feedback compensation and
      0.312ns CE_SET requirement (totaling 8.111ns) by 4.414ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.676,R42C48C.Q0,R42C48B.B0,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48B.B0,R42C48B.F0,signal_process/demodu/fifo/SLICE_617:ROUTE, 2.353,R42C48B.F0,EBR_R25C46.CER,signal_process/demodu/fifo/rden_i">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.676<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48B.B0:0.676">     R42C48C.Q0 to R42C48B.B0    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48B.B0 to     R42C48B.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_617">signal_process/demodu/fifo/SLICE_617</A>
ROUTE         9     2.353<A href="#@net:signal_process/demodu/fifo/rden_i:R42C48B.F0:EBR_R25C46.CER:2.353">     R42C48B.F0 to EBR_R25C46.CER</A> <A href="#@net:signal_process/demodu/fifo/rden_i">signal_process/demodu/fifo/rden_i</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    3.697   (18.1% logic, 81.9% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 4.503ns (weighted slack = 9.006ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_0_1">signal_process/demodu/fifo/pdp_ram_0_0_1</A>(ASIC)  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               3.697ns  (18.1% logic, 81.9% route), 2 logic levels.

 Constraint Details:

      3.697ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/pdp_ram_0_0_1 meets
      8.334ns delay constraint less
     -0.089ns skew and
      0.000ns feedback compensation and
      0.223ns CE_SET requirement (totaling 8.200ns) by 4.503ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.676,R42C48C.Q0,R42C48B.B0,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48B.B0,R42C48B.F0,signal_process/demodu/fifo/SLICE_617:ROUTE, 2.353,R42C48B.F0,EBR_R25C46.OCER,signal_process/demodu/fifo/rden_i">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.676<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48B.B0:0.676">     R42C48C.Q0 to R42C48B.B0    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48B.B0 to     R42C48B.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_617">signal_process/demodu/fifo/SLICE_617</A>
ROUTE         9     2.353<A href="#@net:signal_process/demodu/fifo/rden_i:R42C48B.F0:EBR_R25C46.OCER:2.353">     R42C48B.F0 to EBR_R25C46.OCER</A> <A href="#@net:signal_process/demodu/fifo/rden_i">signal_process/demodu/fifo/rden_i</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    3.697   (18.1% logic, 81.9% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C46.CLKR,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_0_1:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C46.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C46.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 4.609ns (weighted slack = 9.218ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_1_0">signal_process/demodu/fifo/pdp_ram_0_1_0</A>(ASIC)  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               3.502ns  (19.1% logic, 80.9% route), 2 logic levels.

 Constraint Details:

      3.502ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/pdp_ram_0_1_0 meets
      8.334ns delay constraint less
     -0.089ns skew and
      0.000ns feedback compensation and
      0.312ns CE_SET requirement (totaling 8.111ns) by 4.609ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.676,R42C48C.Q0,R42C48B.B0,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48B.B0,R42C48B.F0,signal_process/demodu/fifo/SLICE_617:ROUTE, 2.158,R42C48B.F0,EBR_R25C48.CER,signal_process/demodu/fifo/rden_i">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.676<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48B.B0:0.676">     R42C48C.Q0 to R42C48B.B0    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48B.B0 to     R42C48B.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_617">signal_process/demodu/fifo/SLICE_617</A>
ROUTE         9     2.158<A href="#@net:signal_process/demodu/fifo/rden_i:R42C48B.F0:EBR_R25C48.CER:2.158">     R42C48B.F0 to EBR_R25C48.CER</A> <A href="#@net:signal_process/demodu/fifo/rden_i">signal_process/demodu/fifo/rden_i</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    3.502   (19.1% logic, 80.9% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C48.CLKR,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C48.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C48.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 4.698ns (weighted slack = 9.396ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    PDPW16KD   Port           <A href="#@comp:signal_process/demodu/fifo/pdp_ram_0_1_0">signal_process/demodu/fifo/pdp_ram_0_1_0</A>(ASIC)  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               3.502ns  (19.1% logic, 80.9% route), 2 logic levels.

 Constraint Details:

      3.502ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/pdp_ram_0_1_0 meets
      8.334ns delay constraint less
     -0.089ns skew and
      0.000ns feedback compensation and
      0.223ns CE_SET requirement (totaling 8.200ns) by 4.698ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.676,R42C48C.Q0,R42C48B.B0,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48B.B0,R42C48B.F0,signal_process/demodu/fifo/SLICE_617:ROUTE, 2.158,R42C48B.F0,EBR_R25C48.OCER,signal_process/demodu/fifo/rden_i">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.676<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48B.B0:0.676">     R42C48C.Q0 to R42C48B.B0    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48B.B0 to     R42C48B.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_617">signal_process/demodu/fifo/SLICE_617</A>
ROUTE         9     2.158<A href="#@net:signal_process/demodu/fifo/rden_i:R42C48B.F0:EBR_R25C48.OCER:2.158">     R42C48B.F0 to EBR_R25C48.OCER</A> <A href="#@net:signal_process/demodu/fifo/rden_i">signal_process/demodu/fifo/rden_i</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    3.502   (19.1% logic, 80.9% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.979,PLL_BR0.CLKOS,EBR_R25C48.CLKR,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/pdp_ram_0_1_0:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.979<A href="#@net:clk_AD:PLL_BR0.CLKOS:EBR_R25C48.CLKR:1.979">  PLL_BR0.CLKOS to EBR_R25C48.CLKR</A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.384   (31.2% logic, 68.8% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 5.269ns (weighted slack = 10.538ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/fifo/SLICE_146">signal_process/demodu/fifo/FF_2</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)
                   FF                        <A href="#@net:signal_process/demodu/fifo/FF_3">signal_process/demodu/fifo/FF_3</A>

   Delay:               3.183ns  (21.0% logic, 79.0% route), 2 logic levels.

 Constraint Details:

      3.183ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_146 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.118ns CE_SET requirement (totaling 8.452ns) by 5.269ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.676,R42C48C.Q0,R42C48B.B0,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48B.B0,R42C48B.F0,signal_process/demodu/fifo/SLICE_617:ROUTE, 1.839,R42C48B.F0,R23C50A.CE,signal_process/demodu/fifo/rden_i">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_146:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.676<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48B.B0:0.676">     R42C48C.Q0 to R42C48B.B0    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48B.B0 to     R42C48B.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_617">signal_process/demodu/fifo/SLICE_617</A>
ROUTE         9     1.839<A href="#@net:signal_process/demodu/fifo/rden_i:R42C48B.F0:R23C50A.CE:1.839">     R42C48B.F0 to R23C50A.CE    </A> <A href="#@net:signal_process/demodu/fifo/rden_i">signal_process/demodu/fifo/rden_i</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    3.183   (21.0% logic, 79.0% route), 2 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOS,R23C50A.CLK,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/SLICE_146:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.890<A href="#@net:clk_AD:PLL_BR0.CLKOS:R23C50A.CLK:1.890">  PLL_BR0.CLKOS to R23C50A.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 5.290ns (weighted slack = 10.580ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/fifo/SLICE_124">signal_process/demodu/fifo/FF_20</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               3.282ns  (50.9% logic, 49.1% route), 7 logic levels.

 Constraint Details:

      3.282ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_124 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.572ns) by 5.290ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.681,R42C48C.Q0,R42C48A.A1,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48A.A1,R42C48A.F1,signal_process/demodu/fifo/SLICE_593:ROUTE, 0.931,R42C48A.F1,R44C48A.A1,signal_process/demodu/fifo/cnt_con:C1TOFCO_DEL, 0.401,R44C48A.A1,R44C48A.FCO,signal_process/demodu/fifo/SLICE_120:ROUTE, 0.000,R44C48A.FCO,R44C48B.FCI,signal_process/demodu/fifo/bdcnt_bctr_ci:FCITOFCO_DEL, 0.063,R44C48B.FCI,R44C48B.FCO,signal_process/demodu/fifo/SLICE_121:ROUTE, 0.000,R44C48B.FCO,R44C48C.FCI,signal_process/demodu/fifo/co0:FCITOFCO_DEL, 0.063,R44C48C.FCI,R44C48C.FCO,signal_process/demodu/fifo/SLICE_122:ROUTE, 0.000,R44C48C.FCO,R44C48D.FCI,signal_process/demodu/fifo/co1:FCITOFCO_DEL, 0.063,R44C48D.FCI,R44C48D.FCO,signal_process/demodu/fifo/SLICE_123:ROUTE, 0.000,R44C48D.FCO,R44C49A.FCI,signal_process/demodu/fifo/co2:FCITOF1_DEL, 0.412,R44C49A.FCI,R44C49A.F1,signal_process/demodu/fifo/SLICE_124:ROUTE, 0.000,R44C49A.F1,R44C49A.DI1,signal_process/demodu/fifo/ifcount_7">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_124:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.681<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48A.A1:0.681">     R42C48C.Q0 to R42C48A.A1    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48A.A1 to     R42C48A.F1 <A href="#@comp:signal_process/demodu/fifo/SLICE_593">signal_process/demodu/fifo/SLICE_593</A>
ROUTE        10     0.931<A href="#@net:signal_process/demodu/fifo/cnt_con:R42C48A.F1:R44C48A.A1:0.931">     R42C48A.F1 to R44C48A.A1    </A> <A href="#@net:signal_process/demodu/fifo/cnt_con">signal_process/demodu/fifo/cnt_con</A>
C1TOFCO_DE  ---     0.401     R44C48A.A1 to    R44C48A.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_120">signal_process/demodu/fifo/SLICE_120</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/bdcnt_bctr_ci:R44C48A.FCO:R44C48B.FCI:0.000">    R44C48A.FCO to R44C48B.FCI   </A> <A href="#@net:signal_process/demodu/fifo/bdcnt_bctr_ci">signal_process/demodu/fifo/bdcnt_bctr_ci</A>
FCITOFCO_D  ---     0.063    R44C48B.FCI to    R44C48B.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_121">signal_process/demodu/fifo/SLICE_121</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co0:R44C48B.FCO:R44C48C.FCI:0.000">    R44C48B.FCO to R44C48C.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co0">signal_process/demodu/fifo/co0</A>
FCITOFCO_D  ---     0.063    R44C48C.FCI to    R44C48C.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_122">signal_process/demodu/fifo/SLICE_122</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co1:R44C48C.FCO:R44C48D.FCI:0.000">    R44C48C.FCO to R44C48D.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co1">signal_process/demodu/fifo/co1</A>
FCITOFCO_D  ---     0.063    R44C48D.FCI to    R44C48D.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_123">signal_process/demodu/fifo/SLICE_123</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co2:R44C48D.FCO:R44C49A.FCI:0.000">    R44C48D.FCO to R44C49A.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co2">signal_process/demodu/fifo/co2</A>
FCITOF1_DE  ---     0.412    R44C49A.FCI to     R44C49A.F1 <A href="#@comp:signal_process/demodu/fifo/SLICE_124">signal_process/demodu/fifo/SLICE_124</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/ifcount_7:R44C49A.F1:R44C49A.DI1:0.000">     R44C49A.F1 to R44C49A.DI1   </A> <A href="#@net:signal_process/demodu/fifo/ifcount_7">signal_process/demodu/fifo/ifcount_7</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    3.282   (50.9% logic, 49.1% route), 7 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOS,R44C49A.CLK,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/SLICE_124:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.890<A href="#@net:clk_AD:PLL_BR0.CLKOS:R44C49A.CLK:1.890">  PLL_BR0.CLKOS to R44C49A.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 5.314ns (weighted slack = 10.628ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/fifo/SLICE_124">signal_process/demodu/fifo/FF_21</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               3.255ns  (50.5% logic, 49.5% route), 7 logic levels.

 Constraint Details:

      3.255ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_124 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.569ns) by 5.314ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.681,R42C48C.Q0,R42C48A.A1,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48A.A1,R42C48A.F1,signal_process/demodu/fifo/SLICE_593:ROUTE, 0.931,R42C48A.F1,R44C48A.A1,signal_process/demodu/fifo/cnt_con:C1TOFCO_DEL, 0.401,R44C48A.A1,R44C48A.FCO,signal_process/demodu/fifo/SLICE_120:ROUTE, 0.000,R44C48A.FCO,R44C48B.FCI,signal_process/demodu/fifo/bdcnt_bctr_ci:FCITOFCO_DEL, 0.063,R44C48B.FCI,R44C48B.FCO,signal_process/demodu/fifo/SLICE_121:ROUTE, 0.000,R44C48B.FCO,R44C48C.FCI,signal_process/demodu/fifo/co0:FCITOFCO_DEL, 0.063,R44C48C.FCI,R44C48C.FCO,signal_process/demodu/fifo/SLICE_122:ROUTE, 0.000,R44C48C.FCO,R44C48D.FCI,signal_process/demodu/fifo/co1:FCITOFCO_DEL, 0.063,R44C48D.FCI,R44C48D.FCO,signal_process/demodu/fifo/SLICE_123:ROUTE, 0.000,R44C48D.FCO,R44C49A.FCI,signal_process/demodu/fifo/co2:FCITOF0_DEL, 0.385,R44C49A.FCI,R44C49A.F0,signal_process/demodu/fifo/SLICE_124:ROUTE, 0.000,R44C49A.F0,R44C49A.DI0,signal_process/demodu/fifo/ifcount_6">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_124:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.681<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48A.A1:0.681">     R42C48C.Q0 to R42C48A.A1    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48A.A1 to     R42C48A.F1 <A href="#@comp:signal_process/demodu/fifo/SLICE_593">signal_process/demodu/fifo/SLICE_593</A>
ROUTE        10     0.931<A href="#@net:signal_process/demodu/fifo/cnt_con:R42C48A.F1:R44C48A.A1:0.931">     R42C48A.F1 to R44C48A.A1    </A> <A href="#@net:signal_process/demodu/fifo/cnt_con">signal_process/demodu/fifo/cnt_con</A>
C1TOFCO_DE  ---     0.401     R44C48A.A1 to    R44C48A.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_120">signal_process/demodu/fifo/SLICE_120</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/bdcnt_bctr_ci:R44C48A.FCO:R44C48B.FCI:0.000">    R44C48A.FCO to R44C48B.FCI   </A> <A href="#@net:signal_process/demodu/fifo/bdcnt_bctr_ci">signal_process/demodu/fifo/bdcnt_bctr_ci</A>
FCITOFCO_D  ---     0.063    R44C48B.FCI to    R44C48B.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_121">signal_process/demodu/fifo/SLICE_121</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co0:R44C48B.FCO:R44C48C.FCI:0.000">    R44C48B.FCO to R44C48C.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co0">signal_process/demodu/fifo/co0</A>
FCITOFCO_D  ---     0.063    R44C48C.FCI to    R44C48C.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_122">signal_process/demodu/fifo/SLICE_122</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co1:R44C48C.FCO:R44C48D.FCI:0.000">    R44C48C.FCO to R44C48D.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co1">signal_process/demodu/fifo/co1</A>
FCITOFCO_D  ---     0.063    R44C48D.FCI to    R44C48D.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_123">signal_process/demodu/fifo/SLICE_123</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co2:R44C48D.FCO:R44C49A.FCI:0.000">    R44C48D.FCO to R44C49A.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co2">signal_process/demodu/fifo/co2</A>
FCITOF0_DE  ---     0.385    R44C49A.FCI to     R44C49A.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_124">signal_process/demodu/fifo/SLICE_124</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/ifcount_6:R44C49A.F0:R44C49A.DI0:0.000">     R44C49A.F0 to R44C49A.DI0   </A> <A href="#@net:signal_process/demodu/fifo/ifcount_6">signal_process/demodu/fifo/ifcount_6</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    3.255   (50.5% logic, 49.5% route), 7 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOS,R44C49A.CLK,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/SLICE_124:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.890<A href="#@net:clk_AD:PLL_BR0.CLKOS:R44C49A.CLK:1.890">  PLL_BR0.CLKOS to R44C49A.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 5.353ns (weighted slack = 10.706ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/fifo/SLICE_123">signal_process/demodu/fifo/FF_22</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               3.219ns  (49.9% logic, 50.1% route), 6 logic levels.

 Constraint Details:

      3.219ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_123 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.238ns DIN_SET requirement (totaling 8.572ns) by 5.353ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.681,R42C48C.Q0,R42C48A.A1,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48A.A1,R42C48A.F1,signal_process/demodu/fifo/SLICE_593:ROUTE, 0.931,R42C48A.F1,R44C48A.A1,signal_process/demodu/fifo/cnt_con:C1TOFCO_DEL, 0.401,R44C48A.A1,R44C48A.FCO,signal_process/demodu/fifo/SLICE_120:ROUTE, 0.000,R44C48A.FCO,R44C48B.FCI,signal_process/demodu/fifo/bdcnt_bctr_ci:FCITOFCO_DEL, 0.063,R44C48B.FCI,R44C48B.FCO,signal_process/demodu/fifo/SLICE_121:ROUTE, 0.000,R44C48B.FCO,R44C48C.FCI,signal_process/demodu/fifo/co0:FCITOFCO_DEL, 0.063,R44C48C.FCI,R44C48C.FCO,signal_process/demodu/fifo/SLICE_122:ROUTE, 0.000,R44C48C.FCO,R44C48D.FCI,signal_process/demodu/fifo/co1:FCITOF1_DEL, 0.412,R44C48D.FCI,R44C48D.F1,signal_process/demodu/fifo/SLICE_123:ROUTE, 0.000,R44C48D.F1,R44C48D.DI1,signal_process/demodu/fifo/ifcount_5">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_123:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.681<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48A.A1:0.681">     R42C48C.Q0 to R42C48A.A1    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48A.A1 to     R42C48A.F1 <A href="#@comp:signal_process/demodu/fifo/SLICE_593">signal_process/demodu/fifo/SLICE_593</A>
ROUTE        10     0.931<A href="#@net:signal_process/demodu/fifo/cnt_con:R42C48A.F1:R44C48A.A1:0.931">     R42C48A.F1 to R44C48A.A1    </A> <A href="#@net:signal_process/demodu/fifo/cnt_con">signal_process/demodu/fifo/cnt_con</A>
C1TOFCO_DE  ---     0.401     R44C48A.A1 to    R44C48A.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_120">signal_process/demodu/fifo/SLICE_120</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/bdcnt_bctr_ci:R44C48A.FCO:R44C48B.FCI:0.000">    R44C48A.FCO to R44C48B.FCI   </A> <A href="#@net:signal_process/demodu/fifo/bdcnt_bctr_ci">signal_process/demodu/fifo/bdcnt_bctr_ci</A>
FCITOFCO_D  ---     0.063    R44C48B.FCI to    R44C48B.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_121">signal_process/demodu/fifo/SLICE_121</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co0:R44C48B.FCO:R44C48C.FCI:0.000">    R44C48B.FCO to R44C48C.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co0">signal_process/demodu/fifo/co0</A>
FCITOFCO_D  ---     0.063    R44C48C.FCI to    R44C48C.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_122">signal_process/demodu/fifo/SLICE_122</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co1:R44C48C.FCO:R44C48D.FCI:0.000">    R44C48C.FCO to R44C48D.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co1">signal_process/demodu/fifo/co1</A>
FCITOF1_DE  ---     0.412    R44C48D.FCI to     R44C48D.F1 <A href="#@comp:signal_process/demodu/fifo/SLICE_123">signal_process/demodu/fifo/SLICE_123</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/ifcount_5:R44C48D.F1:R44C48D.DI1:0.000">     R44C48D.F1 to R44C48D.DI1   </A> <A href="#@net:signal_process/demodu/fifo/ifcount_5">signal_process/demodu/fifo/ifcount_5</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    3.219   (49.9% logic, 50.1% route), 6 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOS,R44C48D.CLK,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/SLICE_123:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.890<A href="#@net:clk_AD:PLL_BR0.CLKOS:R44C48D.CLK:1.890">  PLL_BR0.CLKOS to R44C48D.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.
<font color=#000000> 

Passed: The following path meets requirements by 5.377ns (weighted slack = 10.754ns)
 </font>
 Logical Details:  Cell type  Pin type       Cell/ASIC name  (clock net +/-)

   Source:         FF         Q              <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/Read_en</A>  (from <A href="#@net:clk120mhz">clk120mhz</A> +)
   Destination:    FF         Data in        <A href="#@comp:signal_process/demodu/fifo/SLICE_123">signal_process/demodu/fifo/FF_23</A>  (to <A href="#@net:clk_AD">clk_AD</A> +)

   Delay:               3.192ns  (49.5% logic, 50.5% route), 6 logic levels.

 Constraint Details:

      3.192ns physical path delay signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_123 meets
      8.334ns delay constraint less
      0.000ns skew and
      0.000ns feedback compensation and
     -0.235ns DIN_SET requirement (totaling 8.569ns) by 5.377ns

 Physical Path Details:

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:REG_DEL, 0.460,R42C48C.CLK,R42C48C.Q0,signal_process/demodu/SLICE_287:ROUTE, 0.681,R42C48C.Q0,R42C48A.A1,signal_process/demodu/Read_en:CTOF_DEL, 0.208,R42C48A.A1,R42C48A.F1,signal_process/demodu/fifo/SLICE_593:ROUTE, 0.931,R42C48A.F1,R44C48A.A1,signal_process/demodu/fifo/cnt_con:C1TOFCO_DEL, 0.401,R44C48A.A1,R44C48A.FCO,signal_process/demodu/fifo/SLICE_120:ROUTE, 0.000,R44C48A.FCO,R44C48B.FCI,signal_process/demodu/fifo/bdcnt_bctr_ci:FCITOFCO_DEL, 0.063,R44C48B.FCI,R44C48B.FCO,signal_process/demodu/fifo/SLICE_121:ROUTE, 0.000,R44C48B.FCO,R44C48C.FCI,signal_process/demodu/fifo/co0:FCITOFCO_DEL, 0.063,R44C48C.FCI,R44C48C.FCO,signal_process/demodu/fifo/SLICE_122:ROUTE, 0.000,R44C48C.FCO,R44C48D.FCI,signal_process/demodu/fifo/co1:FCITOF0_DEL, 0.385,R44C48D.FCI,R44C48D.F0,signal_process/demodu/fifo/SLICE_123:ROUTE, 0.000,R44C48D.F0,R44C48D.DI0,signal_process/demodu/fifo/ifcount_4">Data path</A> signal_process/demodu/SLICE_287 to signal_process/demodu/fifo/SLICE_123:

   Name    Fanout   Delay (ns)          Site               Resource
REG_DEL     ---     0.460    R42C48C.CLK to     R42C48C.Q0 <A href="#@comp:signal_process/demodu/SLICE_287">signal_process/demodu/SLICE_287</A> (from <A href="#@net:clk120mhz">clk120mhz</A>)
ROUTE         4     0.681<A href="#@net:signal_process/demodu/Read_en:R42C48C.Q0:R42C48A.A1:0.681">     R42C48C.Q0 to R42C48A.A1    </A> <A href="#@net:signal_process/demodu/Read_en">signal_process/demodu/Read_en</A>
CTOF_DEL    ---     0.208     R42C48A.A1 to     R42C48A.F1 <A href="#@comp:signal_process/demodu/fifo/SLICE_593">signal_process/demodu/fifo/SLICE_593</A>
ROUTE        10     0.931<A href="#@net:signal_process/demodu/fifo/cnt_con:R42C48A.F1:R44C48A.A1:0.931">     R42C48A.F1 to R44C48A.A1    </A> <A href="#@net:signal_process/demodu/fifo/cnt_con">signal_process/demodu/fifo/cnt_con</A>
C1TOFCO_DE  ---     0.401     R44C48A.A1 to    R44C48A.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_120">signal_process/demodu/fifo/SLICE_120</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/bdcnt_bctr_ci:R44C48A.FCO:R44C48B.FCI:0.000">    R44C48A.FCO to R44C48B.FCI   </A> <A href="#@net:signal_process/demodu/fifo/bdcnt_bctr_ci">signal_process/demodu/fifo/bdcnt_bctr_ci</A>
FCITOFCO_D  ---     0.063    R44C48B.FCI to    R44C48B.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_121">signal_process/demodu/fifo/SLICE_121</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co0:R44C48B.FCO:R44C48C.FCI:0.000">    R44C48B.FCO to R44C48C.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co0">signal_process/demodu/fifo/co0</A>
FCITOFCO_D  ---     0.063    R44C48C.FCI to    R44C48C.FCO <A href="#@comp:signal_process/demodu/fifo/SLICE_122">signal_process/demodu/fifo/SLICE_122</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/co1:R44C48C.FCO:R44C48D.FCI:0.000">    R44C48C.FCO to R44C48D.FCI   </A> <A href="#@net:signal_process/demodu/fifo/co1">signal_process/demodu/fifo/co1</A>
FCITOF0_DE  ---     0.385    R44C48D.FCI to     R44C48D.F0 <A href="#@comp:signal_process/demodu/fifo/SLICE_123">signal_process/demodu/fifo/SLICE_123</A>
ROUTE         1     0.000<A href="#@net:signal_process/demodu/fifo/ifcount_4:R44C48D.F0:R44C48D.DI0:0.000">     R44C48D.F0 to R44C48D.DI0   </A> <A href="#@net:signal_process/demodu/fifo/ifcount_4">signal_process/demodu/fifo/ifcount_4</A> (to <A href="#@net:clk_AD">clk_AD</A>)
                  --------
                    3.192   (49.5% logic, 50.5% route), 6 logic levels.

 Clock Skew Details: 

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OP_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOP,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOP,R42C48C.CLK,clk120mhz">Source Clock Path</A> clk_in to signal_process/demodu/SLICE_287:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OP_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.890<A href="#@net:clk120mhz:PLL_BR0.CLKOP:R42C48C.CLK:1.890">  PLL_BR0.CLKOP to R42C48C.CLK   </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Source Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

      <A href="#@path:FREQUENCY NET 'clk_AD' 60.000000 MHz ;:PADI_DEL, 1.055,L16.PAD,L16.PADDI,clk_in:ROUTE, 0.350,L16.PADDI,PLL_BR0.CLKI,clk_in_c:CLKI2OS_DEL, 0.000,PLL_BR0.CLKI,PLL_BR0.CLKOS,CLK120/PLLInst_0:ROUTE, 1.890,PLL_BR0.CLKOS,R44C48D.CLK,clk_AD">Destination Clock Path</A> clk_in to signal_process/demodu/fifo/SLICE_123:

   Name    Fanout   Delay (ns)          Site               Resource
PADI_DEL    ---     1.055        L16.PAD to      L16.PADDI <A href="#@comp:clk_in">clk_in</A>
ROUTE         1     0.350<A href="#@net:clk_in_c:L16.PADDI:PLL_BR0.CLKI:0.350">      L16.PADDI to PLL_BR0.CLKI  </A> <A href="#@net:clk_in_c">clk_in_c</A>
CLKI2OS_DE  ---     0.000   PLL_BR0.CLKI to  PLL_BR0.CLKOS <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       101     1.890<A href="#@net:clk_AD:PLL_BR0.CLKOS:R44C48D.CLK:1.890">  PLL_BR0.CLKOS to R44C48D.CLK   </A> <A href="#@net:clk_AD">clk_AD</A>
                  --------
                    3.295   (32.0% logic, 68.0% route), 2 logic levels.

      Destination Clock f/b:

   Name    Fanout   Delay (ns)          Site               Resource
CLKFB2OP_D  ---     0.000  PLL_BR0.CLKFB to  PLL_BR0.CLKOP <A href="#@comp:CLK120/PLLInst_0">CLK120/PLLInst_0</A>
ROUTE       413     1.979<A href="#@net:clk120mhz:PLL_BR0.CLKOP:PLL_BR0.CLKFB:1.979">  PLL_BR0.CLKOP to PLL_BR0.CLKFB </A> <A href="#@net:clk120mhz">clk120mhz</A>
                  --------
                    1.979   (0.0% logic, 100.0% route), 1 logic levels.

Report:  123.671MHz is the maximum frequency for this preference.


</A><A name="FREQUENCY NET 'clk_AD_t_c' 60.000000 MH"></A>================================================================================
Preference: FREQUENCY NET "clk_AD_t_c" 60.000000 MHz ;
            0 items scored, 0 timing errors detected.
--------------------------------------------------------------------------------


</A><A name="BLOCK PATH FROM CLKNET 'clk_AD_t_c' TO CLKNET 'clk120mhz"></A>================================================================================
Preference: BLOCK PATH FROM CLKNET "clk_AD_t_c" TO CLKNET "clk120mhz" ;
            0 items scored.
--------------------------------------------------------------------------------

<A name="Report Summary"></A><B><U><big>Report Summary</big></U></B>
--------------
----------------------------------------------------------------------------
Preference                              |   Constraint|       Actual|Levels
----------------------------------------------------------------------------
                                        |             |             |
FREQUENCY NET "clk120mhz" 120.000000    |             |             |
MHz ;                                   |  120.000 MHz|  122.669 MHz|  28  
                                        |             |             |
FREQUENCY NET "wendu.clk_us" 1.000000   |             |             |
MHz ;                                   |    1.000 MHz|  109.146 MHz|  18  
                                        |             |             |
FREQUENCY NET "clk_in_c" 20.000000 MHz  |             |             |
;                                       |            -|            -|   0  
                                        |             |             |
FREQUENCY NET "clk_AD" 60.000000 MHz ;  |   60.000 MHz|  123.671 MHz|   8  
                                        |             |             |
FREQUENCY NET "clk_AD_t_c" 60.000000    |             |             |
MHz ;                                   |            -|            -|   0  
                                        |             |             |
----------------------------------------------------------------------------


All preferences were met.


<A name="Clock Domains Analysis"></A><B><U><big>Clock Domains Analysis</big></U></B>
------------------------

Found 4 clocks:

Clock Domain: <A href="#@net:wendu.clk_us">wendu.clk_us</A>   Source: wendu/SLICE_241.Q0   Loads: 37
   Covered under: FREQUENCY NET "wendu.clk_us" 1.000000 MHz ;

Clock Domain: <A href="#@net:clk_in_c">clk_in_c</A>   Source: clk_in.PAD   Loads: 1
   No transfer within this clock domain is found

Clock Domain: <A href="#@net:clk_AD">clk_AD</A>   Source: CLK120/PLLInst_0.CLKOS   Loads: 101
   Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;

   Data transfers from:
   Clock Domain: <A href="#@net:clk120mhz">clk120mhz</A>   Source: CLK120/PLLInst_0.CLKOP
      Covered under: FREQUENCY NET "clk_AD" 60.000000 MHz ;   Transfers: 2

Clock Domain: <A href="#@net:clk120mhz">clk120mhz</A>   Source: CLK120/PLLInst_0.CLKOP   Loads: 413
   Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;

   Data transfers from:
   Clock Domain: <A href="#@net:wendu.clk_us">wendu.clk_us</A>   Source: wendu/SLICE_241.Q0
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 16

   Clock Domain: <A href="#@net:clk_AD_t_c">clk_AD_t_c</A>   Source: CLK120/PLLInst_0.CLKOS2
      Blocked under: BLOCK PATH FROM CLKNET "clk_AD_t_c" TO CLKNET "clk120mhz" ;

   Clock Domain: <A href="#@net:clk_AD">clk_AD</A>   Source: CLK120/PLLInst_0.CLKOS
      Covered under: FREQUENCY NET "clk120mhz" 120.000000 MHz ;   Transfers: 111


Timing summary (Setup):
---------------

Timing errors: 0  Score: 0
Cumulative negative slack: 0

Constraints cover 38518 paths, 6 nets, and 3975 connections (99.18% coverage)

