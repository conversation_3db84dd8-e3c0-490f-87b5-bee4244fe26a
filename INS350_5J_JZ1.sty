<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE strategy>
<Strategy version="1.0" predefined="0" description="" label="Strategy1">
    <Property name="PROP_BD_CmdLineArgs" value="" time="0"/>
    <Property name="PROP_BD_EdfHardtimer" value="Enable" time="0"/>
    <Property name="PROP_BD_EdfInBusNameConv" value="None" time="0"/>
    <Property name="PROP_BD_EdfInLibPath" value="" time="0"/>
    <Property name="PROP_BD_EdfInRemLoc" value="Off" time="0"/>
    <Property name="PROP_BD_EdfMemPath" value="" time="0"/>
    <Property name="PROP_BD_ParSearchPath" value="" time="0"/>
    <Property name="PROP_BIT_AddressBitGen" value="Increment" time="0"/>
    <Property name="PROP_BIT_AllowReadBitGen" value="Disable" time="0"/>
    <Property name="PROP_BIT_ByteWideBitMirror" value="Disable" time="0"/>
    <Property name="PROP_BIT_CapReadBitGen" value="Disable" time="0"/>
    <Property name="PROP_BIT_ConModBitGen" value="Disable" time="0"/>
    <Property name="PROP_BIT_CreateBitFile" value="True" time="0"/>
    <Property name="PROP_BIT_DisRAMResBitGen" value="True" time="0"/>
    <Property name="PROP_BIT_DisableUESBitgen" value="False" time="0"/>
    <Property name="PROP_BIT_DonePinBitGen" value="Pullup" time="0"/>
    <Property name="PROP_BIT_DoneSigBitGen" value="4" time="0"/>
    <Property name="PROP_BIT_EnIOBitGen" value="TriStateDuringReConfig" time="0"/>
    <Property name="PROP_BIT_EnIntOscBitGen" value="Disable" time="0"/>
    <Property name="PROP_BIT_ExtClockBitGen" value="False" time="0"/>
    <Property name="PROP_BIT_GSREnableBitGen" value="True" time="0"/>
    <Property name="PROP_BIT_GSRRelOnBitGen" value="DoneIn" time="0"/>
    <Property name="PROP_BIT_GranTimBitGen" value="0" time="0"/>
    <Property name="PROP_BIT_IOTriRelBitGen" value="Cycle 2" time="0"/>
    <Property name="PROP_BIT_JTAGEnableBitGen" value="False" time="0"/>
    <Property name="PROP_BIT_LenBitsBitGen" value="24" time="0"/>
    <Property name="PROP_BIT_MIFFileBitGen" value="" time="0"/>
    <Property name="PROP_BIT_NoHeader" value="False" time="0"/>
    <Property name="PROP_BIT_OutFormatBitGen" value="Bit File (Binary)" time="0"/>
    <Property name="PROP_BIT_OutFormatBitGen_REF" value="" time="0"/>
    <Property name="PROP_BIT_OutFormatPromGen" value="Intel Hex 32-bit" time="0"/>
    <Property name="PROP_BIT_ParityCheckBitGen" value="True" time="0"/>
    <Property name="PROP_BIT_RemZeroFramesBitGen" value="False" time="0"/>
    <Property name="PROP_BIT_RunDRCBitGen" value="True" time="0"/>
    <Property name="PROP_BIT_SearchPthBitGen" value="" time="0"/>
    <Property name="PROP_BIT_StartUpClkBitGen" value="Cclk" time="0"/>
    <Property name="PROP_BIT_SynchIOBitGen" value="True" time="0"/>
    <Property name="PROP_BIT_SysClockConBitGen" value="Reset" time="0"/>
    <Property name="PROP_BIT_SysConBitGen" value="Reset" time="0"/>
    <Property name="PROP_BIT_WaitStTimBitGen" value="5" time="0"/>
    <Property name="PROP_IOTIMING_AllSpeed" value="False" time="0"/>
    <Property name="PROP_LST_AllowDUPMod" value="False" time="0"/>
    <Property name="PROP_LST_CarryChain" value="True" time="0"/>
    <Property name="PROP_LST_CarryChainLength" value="0" time="0"/>
    <Property name="PROP_LST_CmdLineArgs" value="" time="0"/>
    <Property name="PROP_LST_DSPStyle" value="DSP" time="0"/>
    <Property name="PROP_LST_DSPUtil" value="100" time="0"/>
    <Property name="PROP_LST_DecodeUnreachableStates" value="False" time="0"/>
    <Property name="PROP_LST_DisableDistRam" value="False" time="0"/>
    <Property name="PROP_LST_EBRUtil" value="100" time="0"/>
    <Property name="PROP_LST_EdfFrequency" value="200" time="0"/>
    <Property name="PROP_LST_EdfHardtimer" value="Enable" time="0"/>
    <Property name="PROP_LST_EdfInLibPath" value="" time="0"/>
    <Property name="PROP_LST_EdfInRemLoc" value="Off" time="0"/>
    <Property name="PROP_LST_EdfMemPath" value="" time="0"/>
    <Property name="PROP_LST_FIXGATEDCLKS" value="True" time="0"/>
    <Property name="PROP_LST_FSMEncodeStyle" value="Auto" time="0"/>
    <Property name="PROP_LST_ForceGSRInfer" value="No" time="0"/>
    <Property name="PROP_LST_IOInsertion" value="True" time="0"/>
    <Property name="PROP_LST_InterFileDump" value="False" time="0"/>
    <Property name="PROP_LST_LoopLimit" value="1950" time="0"/>
    <Property name="PROP_LST_MaxFanout" value="1000" time="0"/>
    <Property name="PROP_LST_MuxStyle" value="Auto" time="0"/>
    <Property name="PROP_LST_NumCriticalPaths" value="" time="0"/>
    <Property name="PROP_LST_OptimizeGoal" value="Timing" time="0"/>
    <Property name="PROP_LST_PropagatConst" value="True" time="0"/>
    <Property name="PROP_LST_RAMStyle" value="Auto" time="0"/>
    <Property name="PROP_LST_ROMStyle" value="Auto" time="0"/>
    <Property name="PROP_LST_RemoveDupRegs" value="True" time="0"/>
    <Property name="PROP_LST_ResolvedMixedDrivers" value="False" time="0"/>
    <Property name="PROP_LST_ResourceShare" value="True" time="0"/>
    <Property name="PROP_LST_UseIOReg" value="Auto" time="0"/>
    <Property name="PROP_LST_UseLPF" value="True" time="0"/>
    <Property name="PROP_LST_VHDL2008" value="False" time="0"/>
    <Property name="PROP_MAPSTA_AnalysisOption" value="Standard Setup and Hold Analysis" time="0"/>
    <Property name="PROP_MAPSTA_AutoTiming" value="True" time="0"/>
    <Property name="PROP_MAPSTA_CheckUnconstrainedConns" value="False" time="0"/>
    <Property name="PROP_MAPSTA_CheckUnconstrainedPaths" value="False" time="0"/>
    <Property name="PROP_MAPSTA_FullName" value="False" time="0"/>
    <Property name="PROP_MAPSTA_NumUnconstrainedPaths" value="0" time="0"/>
    <Property name="PROP_MAPSTA_ReportStyle" value="Verbose Timing Report" time="0"/>
    <Property name="PROP_MAPSTA_RouteEstAlogtithm" value="0" time="0"/>
    <Property name="PROP_MAPSTA_RptAsynTimLoop" value="False" time="0"/>
    <Property name="PROP_MAPSTA_WordCasePaths" value="1" time="0"/>
    <Property name="PROP_MAP_IgnorePreErr" value="True" time="0"/>
    <Property name="PROP_MAP_MAPIORegister" value="Auto" time="0"/>
    <Property name="PROP_MAP_MAPInferGSR" value="True" time="0"/>
    <Property name="PROP_MAP_MapModArgs" value="" time="0"/>
    <Property name="PROP_MAP_OvermapDevice" value="False" time="0"/>
    <Property name="PROP_MAP_PackLogMapDes" value="" time="0"/>
    <Property name="PROP_MAP_RegRetiming" value="False" time="0"/>
    <Property name="PROP_MAP_SigCrossRef" value="False" time="0"/>
    <Property name="PROP_MAP_SymCrossRef" value="False" time="0"/>
    <Property name="PROP_MAP_TimingDriven" value="False" time="0"/>
    <Property name="PROP_MAP_TimingDrivenNodeRep" value="False" time="0"/>
    <Property name="PROP_MAP_TimingDrivenPack" value="False" time="0"/>
    <Property name="PROP_PARSTA_AnalysisOption" value="Standard Setup and Hold Analysis" time="0"/>
    <Property name="PROP_PARSTA_AutoTiming" value="True" time="0"/>
    <Property name="PROP_PARSTA_CheckUnconstrainedConns" value="False" time="0"/>
    <Property name="PROP_PARSTA_CheckUnconstrainedPaths" value="False" time="0"/>
    <Property name="PROP_PARSTA_FullName" value="False" time="0"/>
    <Property name="PROP_PARSTA_NumUnconstrainedPaths" value="0" time="0"/>
    <Property name="PROP_PARSTA_ReportStyle" value="Verbose Timing Report" time="0"/>
    <Property name="PROP_PARSTA_RptAsynTimLoop" value="False" time="0"/>
    <Property name="PROP_PARSTA_SpeedForHoldAnalysis" value="m" time="0"/>
    <Property name="PROP_PARSTA_SpeedForSetupAnalysis" value="default" time="0"/>
    <Property name="PROP_PARSTA_WordCasePaths" value="10" time="0"/>
    <Property name="PROP_PAR_CrDlyStFileParDes" value="False" time="0"/>
    <Property name="PROP_PAR_DisableTDParDes" value="False" time="0"/>
    <Property name="PROP_PAR_EffortParDes" value="5" time="0"/>
    <Property name="PROP_PAR_MultiSeedSortMode" value="Worst Slack" time="0"/>
    <Property name="PROP_PAR_NewRouteParDes" value="NBR" time="0"/>
    <Property name="PROP_PAR_PARClockSkew" value="Off" time="0"/>
    <Property name="PROP_PAR_PARModArgs" value="" time="0"/>
    <Property name="PROP_PAR_ParMultiNodeList" value="" time="0"/>
    <Property name="PROP_PAR_ParRunPlaceOnly" value="False" time="0"/>
    <Property name="PROP_PAR_PlcIterParDes" value="1" time="0"/>
    <Property name="PROP_PAR_PlcStCostTblParDes" value="1" time="0"/>
    <Property name="PROP_PAR_PrefErrorOut" value="True" time="0"/>
    <Property name="PROP_PAR_RemoveDir" value="True" time="0"/>
    <Property name="PROP_PAR_RouteDlyRedParDes" value="0" time="0"/>
    <Property name="PROP_PAR_RoutePassParDes" value="6" time="0"/>
    <Property name="PROP_PAR_RouteResOptParDes" value="0" time="0"/>
    <Property name="PROP_PAR_RoutingCDP" value="Auto" time="0"/>
    <Property name="PROP_PAR_RoutingCDR" value="1" time="0"/>
    <Property name="PROP_PAR_RunParWithTrce" value="False" time="0"/>
    <Property name="PROP_PAR_RunTimeReduction" value="True" time="0"/>
    <Property name="PROP_PAR_SaveBestRsltParDes" value="1" time="0"/>
    <Property name="PROP_PAR_StopZero" value="False" time="0"/>
    <Property name="PROP_PAR_parHold" value="On" time="0"/>
    <Property name="PROP_PAR_parPathBased" value="Off" time="0"/>
    <Property name="PROP_PRE_CmdLineArgs" value="" time="0"/>
    <Property name="PROP_PRE_EdfArrayBoundsCase" value="False" time="0"/>
    <Property name="PROP_PRE_EdfAutoResOfRam" value="False" time="0"/>
    <Property name="PROP_PRE_EdfClockDomainCross" value="False" time="0"/>
    <Property name="PROP_PRE_EdfDSPAcrossHie" value="False" time="0"/>
    <Property name="PROP_PRE_EdfFullCase" value="False" time="0"/>
    <Property name="PROP_PRE_EdfIgnoreRamRWCol" value="False" time="0"/>
    <Property name="PROP_PRE_EdfMissConstraint" value="False" time="0"/>
    <Property name="PROP_PRE_EdfNetFanout" value="True" time="0"/>
    <Property name="PROP_PRE_EdfParaCase" value="False" time="0"/>
    <Property name="PROP_PRE_EdfReencodeFSM" value="True" time="0"/>
    <Property name="PROP_PRE_EdfResSharing" value="True" time="0"/>
    <Property name="PROP_PRE_EdfTimingViolation" value="True" time="0"/>
    <Property name="PROP_PRE_EdfUseSafeFSM" value="False" time="0"/>
    <Property name="PROP_PRE_EdfVlog2001" value="True" time="0"/>
    <Property name="PROP_PRE_VSynComArea" value="False" time="0"/>
    <Property name="PROP_PRE_VSynCritcal" value="3" time="0"/>
    <Property name="PROP_PRE_VSynFSM" value="Auto" time="0"/>
    <Property name="PROP_PRE_VSynFreq" value="200" time="0"/>
    <Property name="PROP_PRE_VSynGSR" value="False" time="0"/>
    <Property name="PROP_PRE_VSynGatedClk" value="False" time="0"/>
    <Property name="PROP_PRE_VSynIOPad" value="False" time="0"/>
    <Property name="PROP_PRE_VSynOutNetForm" value="None" time="0"/>
    <Property name="PROP_PRE_VSynOutPref" value="True" time="0"/>
    <Property name="PROP_PRE_VSynRepClkFreq" value="True" time="0"/>
    <Property name="PROP_PRE_VSynRetime" value="True" time="0"/>
    <Property name="PROP_PRE_VSynTimSum" value="10" time="0"/>
    <Property name="PROP_PRE_VSynTransform" value="True" time="0"/>
    <Property name="PROP_PRE_VSyninpd" value="0" time="0"/>
    <Property name="PROP_PRE_VSynoutd" value="0" time="0"/>
    <Property name="PROP_SYN_ClockConversion" value="True" time="0"/>
    <Property name="PROP_SYN_CmdLineArgs" value="" time="0"/>
    <Property name="PROP_SYN_DisableRegisterRep" value="False" time="0"/>
    <Property name="PROP_SYN_EdfAllowDUPMod" value="False" time="0"/>
    <Property name="PROP_SYN_EdfArea" value="False" time="0"/>
    <Property name="PROP_SYN_EdfArrangeVHDLFiles" value="True" time="0"/>
    <Property name="PROP_SYN_EdfDefEnumEncode" value="Default" time="0"/>
    <Property name="PROP_SYN_EdfFanout" value="1000" time="0"/>
    <Property name="PROP_SYN_EdfFrequency" value="200" time="0"/>
    <Property name="PROP_SYN_EdfGSR" value="False" time="0"/>
    <Property name="PROP_SYN_EdfInsertIO" value="False" time="0"/>
    <Property name="PROP_SYN_EdfNumCritPath" value="" time="0"/>
    <Property name="PROP_SYN_EdfNumStartEnd" value="" time="0"/>
    <Property name="PROP_SYN_EdfOutNetForm" value="None" time="0"/>
    <Property name="PROP_SYN_EdfPushTirstates" value="True" time="0"/>
    <Property name="PROP_SYN_EdfResSharing" value="True" time="0"/>
    <Property name="PROP_SYN_EdfRunRetiming" value="Pipelining Only" time="0"/>
    <Property name="PROP_SYN_EdfSymFSM" value="True" time="0"/>
    <Property name="PROP_SYN_EdfUnconsClk" value="False" time="0"/>
    <Property name="PROP_SYN_EdfVerilogInput" value="Verilog 2001" time="0"/>
    <Property name="PROP_SYN_ExportSetting" value="No" time="0"/>
    <Property name="PROP_SYN_LibPath" value="" time="0"/>
    <Property name="PROP_SYN_ResolvedMixedDrivers" value="False" time="0"/>
    <Property name="PROP_SYN_UpdateCompilePtTimData" value="False" time="0"/>
    <Property name="PROP_SYN_UseLPF" value="True" time="0"/>
    <Property name="PROP_SYN_VHDL2008" value="False" time="0"/>
    <Property name="PROP_THERMAL_DefaultFreq" value="0" time="0"/>
    <Property name="PROP_TIM_MaxDelSimDes" value="" time="0"/>
    <Property name="PROP_TIM_MinSpeedGrade" value="False" time="0"/>
    <Property name="PROP_TIM_ModPreSimDes" value="" time="0"/>
    <Property name="PROP_TIM_NegStupHldTim" value="True" time="0"/>
    <Property name="PROP_TIM_TimSimGenPUR" value="True" time="0"/>
    <Property name="PROP_TIM_TimSimGenX" value="False" time="0"/>
    <Property name="PROP_TIM_TimSimHierSep" value="" time="0"/>
    <Property name="PROP_TIM_TransportModeOfPathDelay" value="False" time="0"/>
    <Property name="PROP_TIM_TrgtSpeedGrade" value="" time="0"/>
    <Property name="PROP_TIM_WriteVerboseNetlist" value="False" time="0"/>
    <Property name="PROP_TMCHK_EnableCheck" value="True" time="0"/>
</Strategy>
