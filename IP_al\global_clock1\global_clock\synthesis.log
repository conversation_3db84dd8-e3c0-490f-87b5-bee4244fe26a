synthesis:  version Diamond (64-bit) 3.12.1.454

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
Thu Mar 13 16:52:25 2025


Command Line:  D:\Software\lscc\diamond\3.12\ispfpga\bin\nt64\synthesis.exe -f global_clock.synproj -sdc D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.ldc -gui 

Synthesis options:
The -a option is ecp5u.
The -s option is 7.
The -t option is CABGA256.
The -d option is LFE5U-25F.
Using package CABGA256.
Using performance grade 7.
                                                          

##########################################################

### Lattice Family : ECP5U

### Device  : LFE5U-25F

### Package : CABGA256

### Speed   : 7

##########################################################

                                                          

INFO - synthesis: User-Selected Strategy Settings
Optimization goal = Balanced
Top-level module name = global_clock.
Target frequency = 200.000000 MHz.
Maximum fanout = 1000.
Timing path count = 3
BRAM utilization = 100.000000 %
DSP usage = true (default)
DSP utilization = 100 % (default)
fsm_encoding_style = auto
resolve_mixed_drivers = 0
fix_gated_clocks = 1

Mux style = Auto
Use Carry Chain = true
carry_chain_length = 0
Use IO Insertion = FALSE
Use IO Reg = FALSE (implied)
WARNING - synthesis: Cannot pack registers into I/Os because use_io_insertion is FALSE.
WARNING - synthesis: Ignoring user setting of use_io_reg. Value is set to FALSE.
Use IO Reg = FALSE
Resource Sharing = TRUE
Propagate Constants = TRUE
Remove Duplicate Registers = TRUE
force_gsr = auto
ROM style = auto
RAM style = auto
The -comp option is FALSE.
The -syn option is FALSE.
-p D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock (searchpath added)
Verilog design file = D:/Software/lscc/diamond/3.12/cae_library/synthesis/verilog/ecp5u.v
Verilog design file = D:/Software/lscc/diamond/3.12/cae_library/synthesis/verilog/pmi_def.v
Verilog design file = D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.v
NGO file = D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.ngo
WARNING - synthesis: Option -force_gsr will be set to false when option -output_edif/-ngo are set
-sdc option: SDC file input is D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.ldc.
-lpf option: Output file option is ON.
Hardtimer checking is enabled (default). The -dt option is not used.
The -r option is OFF. [ Remove LOC Properties is OFF. ]
Technology check ok...

Analyzing Verilog file D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v. VERI-1482
Compile design.
Compile Design Begin
Analyzing Verilog file d:/software/lscc/diamond/3.12/cae_library/synthesis/verilog/ecp5u.v. VERI-1482
Analyzing Verilog file d:/software/lscc/diamond/3.12/cae_library/synthesis/verilog/pmi_def.v. VERI-1482
Analyzing Verilog file d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v. VERI-1482
Analyzing Verilog file D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v. VERI-1482
Top module name (Verilog): global_clock
INFO - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(8): compiling module global_clock. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(757): compiling module VHI. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(761): compiling module VLO. VERI-1018
INFO - synthesis: D:/Software/lscc/diamond/3.12/ispfpga/userware/NT/SYNTHESIS_HEADERS/ecp5u.v(1696): compiling module EHXPLLL_renamed_due_excessive_length_1. VERI-1018
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/sa5p00/data/sa5plib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/xo2c00/data/xo2clib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/mg5g00/data/mg5glib.ngl'...
Loading NGL library 'D:/Software/lscc/diamond/3.12/ispfpga/or5g00/data/orc5glib.ngl'...
Loading device for application map from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
WARNING - synthesis: There are no design constraints in the file D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.ldc
Top-level module name = global_clock.
WARNING - synthesis: d:/project/ins350_5j_jz/ip_al/global_clock1/global_clock/global_clock.v(23): Removing unused instance scuba_vhi_inst. VDB-5034
WARNING - synthesis: There are no design constraints in the file D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.ldc
This ldc file was generated by Clarity Designer!




######## GSR will not be inferred in an NGO flow, unless force_gsr=yes.
WARNING - synthesis: There are no design constraints in the file global_clock_for_lpf.sdc
Writing LPF file D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.lpf.
Results of NGD DRC are available in global_clock_drc.log.
WARNING - synthesis: DRC checking was skipped because the -ngo option was used.
Writing NGD file D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.ngo.

################### Begin Area Report (global_clock)######################
Number of register bits => 0 of 24879 (0 % )
EHXPLLL => 1
GSR => 1
################### End Area Report ##################

################### Begin Clock Report ######################
Clock Nets
Number of Clocks: 1
  Net : CLKOP, loads : 1
Clock Enable Nets
Number of Clock Enables: 0
Top 0 highest fanout Clock Enables:
Highest fanout non-clock nets
Top 10 highest fanout non-clock nets:
  Net : CLKOS, loads : 0
  Net : CLKOS2, loads : 0
  Net : LOCK, loads : 0
################### End Clock Report ##################

Peak Memory Usage: 84.828  MB

--------------------------------------------------------------
Elapsed CPU time for LSE flow : 0.359  secs
--------------------------------------------------------------
