@E: CS219 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":1:0:1:0|Expecting one of the keywords module, primitive or macromodule
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":1:21:1:84|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":2:88:2:166|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":2:88:3:95|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":4:66:4:67|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":4:107:4:244|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":6:96:6:98|Newline in quoted string
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":7:417:7:419|Unknown macro L
@E: CG349 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":7:532:7:534|Expecting digit after '.' in real
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":7:543:7:544|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":7:567:7:568|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":7:589:7:590|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":7:627:7:628|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":7:697:7:698|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":7:726:7:727|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":7:805:7:806|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":8:11:8:351|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":9:69:9:70|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":9:140:9:141|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":10:52:10:58|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":11:521:11:522|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":11:550:11:664|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":14:42:14:43|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":14:136:14:160|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":14:136:15:297|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":17:71:17:384|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":18:18:18:20|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":19:40:19:41|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":20:99:20:100|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":20:381:20:539|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":22:4:22:12|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":22:4:23:10|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":25:63:25:64|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":25:175:25:176|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":25:177:25:178|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":25:196:25:197|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":27:13:27:14|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":30:12:30:13|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":30:91:30:92|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":34:11:34:12|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":34:43:34:46|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":34:54:34:55|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":35:318:35:319|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG350 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":35:371:35:373|Expecting exponent
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":35:419:35:420|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":37:40:37:44|Unknown macro fch
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":37:74:37:75|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":37:91:37:92|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":37:166:37:665|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":37:166:38:74|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":39:152:39:153|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":39:185:39:210|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":39:185:40:30|Newline in quoted string
@E: CG354 :"D:\Project\INS350_5J_JZ\Src_al\INS350_5J_JZ.v":39:185:40:30|EOF in quoted string
@E: CS219 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":1:0:1:0|Expecting one of the keywords module, primitive or macromodule
@E: CG350 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":2:21:2:23|Expecting exponent
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":2:71:2:79|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":2:71:3:47|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":2:71:4:7|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":2:71:5:4088|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":2:71:6:115|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":7:187:7:188|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":7:347:7:447|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":8:77:8:78|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":9:139:9:140|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":9:312:9:313|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":9:315:9:316|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG350 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":9:345:9:347|Expecting exponent
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":9:356:9:357|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":9:415:9:593|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":9:415:10:331|Newline in quoted string
@E: CG350 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":11:15:11:17|Expecting exponent
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":13:67:13:68|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":13:249:13:250|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":13:318:13:319|expecting identifier immediately following back-quote (`)
@E: CG353 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":13:438:13:440|Expecting digit in radix 10
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":14:70:14:71|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":14:75:14:76|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":14:224:14:226|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":15:383:15:384|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG349 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":15:511:15:513|Expecting digit after '.' in real
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":15:554:15:709|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":16:85:16:130|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":16:85:17:94|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":19:40:19:42|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":19:46:19:47|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":19:143:19:144|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":19:147:19:148|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":19:190:19:203|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":19:190:20:171|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":19:190:21:266|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":23:3:23:4|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG350 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":23:6:23:9|Expecting exponent
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":23:144:23:145|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":23:149:23:150|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":23:328:23:329|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":23:370:23:371|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":23:408:23:409|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":25:36:25:37|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":25:74:25:125|Newline in quoted string
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":28:19:28:21|Unknown macro f
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":28:76:28:77|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":30:6:30:7|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":30:42:30:43|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":30:110:30:111|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG351 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":30:315:30:316|Integer size out of range 1 to 1048576
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":30:315:30:317|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":30:1023:30:1024|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":30:107:30:1132|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":31:34:31:36|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG429 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":31:144:31:145|Found unsized single bit literal in Verilog-2001 mode.
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":31:234:31:235|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":31:248:32:126|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":33:60:33:61|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":33:159:33:160|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":33:536:33:537|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Ctrl_Data.v":33:701:33:702|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS219 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":1:0:1:0|Expecting one of the keywords module, primitive or macromodule
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":1:91:1:93|Unknown macro x
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":1:170:1:218|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":1:170:2:152|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":3:161:3:162|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":3:292:3:293|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":3:405:3:490|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":3:405:4:55|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":5:43:5:44|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":5:84:5:86|Unknown macro u
@E: CG349 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":5:381:5:383|Expecting digit after '.' in real
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":5:669:5:670|expecting identifier immediately following back-quote (`)
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":5:770:5:772|Unknown macro D
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":5:773:5:774|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":5:805:5:806|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":5:819:5:820|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":6:11:6:12|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":6:94:6:95|expecting identifier immediately following back-quote (`)
@E: CG429 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":6:156:6:157|Found unsized single bit literal in Verilog-2001 mode.
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":7:142:7:143|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":7:180:7:181|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG349 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":7:246:7:248|Expecting digit after '.' in real
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":7:309:7:581|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":8:70:8:71|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":9:46:9:238|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":9:46:10:17|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":9:46:11:544|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":12:73:12:1245|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":12:73:13:68|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":12:73:14:34|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":15:61:15:62|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":15:335:15:336|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":15:371:15:372|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":16:4:16:5|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":16:7:16:8|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":16:10:16:11|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":17:9:17:10|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":17:30:17:257|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":19:104:19:105|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":20:57:20:67|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":20:57:21:45|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":20:57:22:220|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":24:144:24:145|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG350 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":24:198:24:200|Expecting exponent
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":24:268:24:474|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":24:268:25:439|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":26:485:26:486|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":26:545:26:546|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Demodulation.v":26:550:26:633|Newline in quoted string
@E: CS219 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":1:0:1:0|Expecting one of the keywords module, primitive or macromodule
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":1:279:1:396|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":1:279:2:20|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":1:279:3:132|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":1:279:4:132|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":1:279:5:551|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":1:279:6:96|Newline in quoted string
@E: CG349 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:179:7:181|Expecting digit after '.' in real
@E: CG429 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:680:7:681|Found unsized single bit literal in Verilog-2001 mode.
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:766:7:768|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:774:7:775|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:818:7:819|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:844:7:845|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:894:7:895|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:908:7:909|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:56:7:1081|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG349 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:83:7:1109|Expecting digit after '.' in real
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:589:7:1614|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:601:7:1626|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:687:7:1712|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG353 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:693:7:1719|Expecting digit in radix 8
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:698:7:699|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:705:7:1730|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":7:757:7:758|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":10:32:10:33|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":12:24:12:25|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":13:50:13:51|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":13:56:13:101|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":13:56:14:29|Newline in quoted string
@E: CG354 :"D:\Project\INS350_5J_JZ\Src_al\Integration.v":13:56:14:29|EOF in quoted string
@E: CS219 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":1:0:1:0|Expecting one of the keywords module, primitive or macromodule
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":1:84:1:87|Newline in quoted string
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":4:23:4:25|Unknown macro q
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":6:1:6:2|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":6:183:6:184|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":6:318:6:319|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":6:327:6:511|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":6:327:7:56|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":6:327:8:505|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":6:327:9:82|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":6:327:10:49|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":11:230:11:231|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":12:38:12:144|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":13:473:13:474|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":13:502:13:503|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":15:14:15:15|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":15:53:15:56|Unknown macro u
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":15:351:15:354|Unknown macro Zw
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":15:385:15:386|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":15:403:15:404|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":15:525:15:591|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":15:525:16:253|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":17:82:17:83|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":19:27:19:28|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":20:57:20:237|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":23:10:23:12|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":26:6:26:7|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":26:10:26:33|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":26:10:27:20|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":28:343:28:344|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":28:534:28:535|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":28:720:28:722|Unknown macro O
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":29:2:29:3|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":29:139:29:270|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":32:87:32:88|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":33:3:33:4|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":33:15:33:206|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":34:705:34:706|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":34:746:34:747|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":35:59:35:95|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":35:59:36:54|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":37:37:37:215|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":38:33:38:34|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":39:59:39:60|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":39:161:39:162|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":39:173:39:174|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":39:181:39:182|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":39:239:39:240|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":42:7:42:49|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":42:7:43:30|Newline in quoted string
@E: CG354 :"D:\Project\INS350_5J_JZ\Src_al\Modulation.v":42:7:43:30|EOF in quoted string
@E: CS219 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":1:0:1:0|Expecting one of the keywords module, primitive or macromodule
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":1:67:1:117|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":2:15:2:50|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":2:15:3:26|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":2:15:4:132|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":2:15:5:168|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":6:301:6:302|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":6:304:6:346|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":6:304:7:60|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":6:304:8:12|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":6:304:9:218|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":6:304:10:85|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":12:31:12:32|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":12:39:12:181|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":12:39:13:584|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":14:56:14:73|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":14:56:15:593|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":17:22:17:23|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":18:303:18:304|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":18:384:18:385|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":18:422:18:423|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":18:438:18:483|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":18:438:19:80|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":18:438:20:325|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":21:68:21:69|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":21:146:21:213|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":21:146:22:35|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":21:146:23:123|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":24:447:24:448|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":25:142:25:143|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":26:444:26:445|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":26:508:26:509|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":26:605:26:607|Unknown macro w
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":26:692:26:693|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":26:710:26:711|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":27:41:27:42|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":27:70:27:205|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":27:70:28:138|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":29:574:29:1046|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":29:574:30:34|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":31:172:31:496|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":31:172:32:321|Newline in quoted string
@E: CG354 :"D:\Project\INS350_5J_JZ\Src_al\Rs422Output.v":31:172:32:321|EOF in quoted string
@E: CS219 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":1:0:1:0|Expecting one of the keywords module, primitive or macromodule
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":1:69:1:70|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":2:50:2:51|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":3:870:3:871|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":3:935:3:4008|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":3:959:3:4032|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":9:66:9:241|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":10:191:10:192|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":10:191:11:199|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":10:191:12:13|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":10:191:13:73|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":14:149:14:150|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":14:225:14:226|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":14:252:14:361|Newline in quoted string
@E: CG353 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":15:111:15:113|Expecting digit in radix 10
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":15:136:16:2|Unknown macro J7
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:16:18:17|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:56:18:57|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG350 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:72:18:74|Expecting exponent
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:145:18:146|expecting identifier immediately following back-quote (`)
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:284:18:287|Unknown macro s1
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:322:18:323|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:753:18:754|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:771:18:772|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:784:18:785|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":18:858:18:859|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":19:60:19:108|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":19:60:20:40|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":19:60:21:227|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":22:515:22:592|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":22:515:23:18|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":24:224:24:225|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":24:281:24:282|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":24:298:24:321|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":24:298:25:123|Newline in quoted string
@E: CG354 :"D:\Project\INS350_5J_JZ\Src_al\SignalGenerator.v":24:298:25:123|EOF in quoted string
@E: CS219 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":1:0:1:0|Expecting one of the keywords module, primitive or macromodule
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":3:5:3:14|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":3:5:4:27|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":5:171:5:172|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":5:323:5:324|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":5:365:5:451|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":5:365:6:97|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":5:365:7:200|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":5:365:8:251|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":5:365:9:83|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":5:365:10:135|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":5:365:11:76|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":13:292:13:869|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":13:292:14:221|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":15:454:15:455|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":15:462:15:463|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":15:469:15:523|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":15:469:16:98|Newline in quoted string
@E: CG353 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":17:134:17:136|Expecting digit in radix 2
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":17:163:17:165|Unknown macro i
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":17:301:17:441|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":18:197:18:285|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":18:197:19:60|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":18:197:20:267|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":18:197:21:251|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":22:271:22:272|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":22:483:22:493|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":22:483:23:67|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":22:483:24:304|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":22:483:25:3|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":22:483:26:66|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":27:27:27:28|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":27:103:27:366|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":28:45:28:46|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":29:8:29:9|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":29:11:29:12|expecting identifier immediately following back-quote (`)
@E: CG353 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":29:118:29:120|Expecting digit in radix 16
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":30:16:30:17|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":31:7:31:9|Unknown macro p
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":34:15:34:16|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":34:132:34:133|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":34:166:34:379|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":35:213:35:214|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":37:456:37:461|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":37:456:38:74|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":37:456:39:53|Newline in quoted string
@E: CG354 :"D:\Project\INS350_5J_JZ\Src_al\SignalProcessing.v":37:456:39:53|EOF in quoted string
@E: CS219 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":1:0:1:0|Expecting one of the keywords module, primitive or macromodule
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":1:18:1:19|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":1:173:1:174|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":2:39:2:40|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":2:44:2:45|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":2:80:2:81|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":2:369:2:370|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":2:596:2:597|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":2:632:2:633|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":2:640:2:641|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":2:671:2:672|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":2:804:2:805|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":3:4:3:5|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":3:68:3:69|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":3:87:3:88|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":3:370:3:371|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":3:693:3:706|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":4:133:4:134|expecting identifier immediately following back-quote (`)
@E: CG350 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":5:41:5:43|Expecting exponent
@E: CG353 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":8:7:8:9|Expecting digit in radix 16
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":8:15:8:76|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":8:15:9:189|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":11:59:11:60|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":12:124:12:125|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":12:201:12:202|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":13:3:13:4|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":13:40:13:256|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":13:40:14:171|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":13:40:15:200|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":13:40:16:73|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":18:109:18:133|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":19:166:19:197|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":20:235:20:236|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":20:240:20:269|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":20:240:21:39|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":22:338:22:339|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":22:344:22:345|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":22:368:22:369|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":22:384:22:385|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":23:30:23:31|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":23:118:23:119|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":23:134:23:135|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG353 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":23:232:23:234|Expecting digit in radix 8
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":23:756:23:757|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":24:7:24:8|expecting identifier immediately following back-quote (`)
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":24:66:24:68|Unknown macro q
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":26:1:26:2|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":28:98:28:99|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":30:64:30:65|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":30:71:30:165|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":30:71:31:6|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":30:71:32:302|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":30:71:33:114|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":30:71:34:316|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":35:168:35:215|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":35:168:36:7|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":35:168:37:171|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":38:223:38:224|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":38:245:38:270|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":38:245:39:19|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":38:245:40:44|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":42:0:42:3|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":42:135:42:136|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":42:310:42:311|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":42:476:42:477|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":42:551:42:552|expecting identifier immediately following back-quote (`)
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":42:583:42:585|Unknown macro y
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":43:14:43:15|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":43:222:43:223|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":43:227:43:228|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":43:279:43:280|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":44:163:44:164|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":44:177:44:178|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":44:179:44:180|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":44:199:44:200|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":45:54:45:55|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":45:90:45:91|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":45:191:45:210|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":46:43:46:69|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":46:43:47:449|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":48:102:48:103|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":49:42:49:179|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":50:132:50:133|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":50:144:50:145|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":50:154:50:155|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":50:251:50:252|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":51:1:51:18|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":51:1:52:58|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":53:210:53:211|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":54:138:54:139|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":54:170:54:171|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":54:375:54:377|Unknown macro u
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":54:381:54:382|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":54:383:54:548|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":54:383:55:115|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":56:626:56:707|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":57:124:57:125|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":57:200:57:201|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":57:221:57:222|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":57:256:57:257|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":57:357:57:358|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":57:389:57:390|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:55:58:56|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:154:58:155|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:389:58:390|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:742:58:743|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:755:58:756|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:819:58:820|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:898:58:899|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:919:58:920|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:19:58:1340|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":58:19:59:4|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":60:550:60:551|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":60:576:60:577|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":60:605:60:606|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":60:624:60:625|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":60:747:60:748|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":60:758:60:766|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":61:363:61:568|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":61:363:62:65|Newline in quoted string
@E: CG353 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":63:195:63:197|Expecting digit in radix 10
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":63:207:63:249|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":64:139:64:335|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":64:139:65:328|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":64:139:66:99|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":64:139:67:20|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":68:250:68:252|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":68:280:68:281|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":68:376:68:377|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":68:457:68:458|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":68:459:68:460|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":68:523:68:524|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":68:672:68:673|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":69:43:69:44|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":69:250:69:251|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":69:321:69:322|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":69:343:69:344|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":69:96:69:97|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":69:250:69:1512|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":70:28:70:380|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":71:42:71:43|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG353 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":72:8:72:10|Expecting digit in radix 10
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":72:41:72:42|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":72:165:72:166|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":72:194:72:195|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":72:230:72:231|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":72:235:72:258|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":73:416:73:417|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":73:450:73:451|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":74:1023:74:1024|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":74:80:74:81|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":74:97:74:98|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":75:15:75:161|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":76:483:76:484|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":76:488:76:548|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":76:488:77:223|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":76:488:78:67|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":79:225:79:226|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":79:232:79:233|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:18:80:21|Unknown macro v1
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:95:80:96|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:501:80:502|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:603:80:604|expecting identifier immediately following back-quote (`)
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:642:80:644|Unknown macro p
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:644:80:646|Unknown macro R
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:777:80:778|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:860:80:861|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:65:80:1090|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG353 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":80:424:80:1450|Expecting digit in radix 8
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":82:49:82:50|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":82:63:82:64|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":83:184:83:205|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":83:184:84:71|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":83:184:85:267|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":83:184:86:26|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":87:62:87:63|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":87:155:87:156|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":87:173:87:175|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":87:428:87:538|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":87:428:88:691|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":87:428:89:167|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":87:428:90:28|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":87:428:91:111|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":92:271:92:272|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":93:4:93:5|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":93:32:93:33|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":93:52:93:53|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":93:63:93:64|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":93:69:93:192|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":95:11:95:12|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":95:52:95:53|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":95:121:95:122|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":95:145:95:149|Unknown macro ei
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":95:156:95:166|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":95:156:96:38|Newline in quoted string
@E: CG223 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":97:493:97:493|Expecting comma or *) delimiter
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":98:51:98:222|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":100:36:100:37|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":100:74:100:127|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":101:252:101:253|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":101:556:101:629|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":103:13:103:153|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":105:191:105:192|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":105:201:105:202|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":105:220:105:730|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":107:92:107:93|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":107:127:107:128|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":108:35:108:37|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":108:106:108:107|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":108:163:108:164|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":108:170:108:171|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":108:215:108:310|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":109:39:109:40|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":111:30:111:337|Newline in quoted string
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":112:73:112:75|Unknown macro o
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":112:161:112:162|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":112:230:112:231|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":112:319:112:320|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":112:333:112:334|expecting identifier immediately following back-quote (`)
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":113:84:113:85|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":113:126:113:127|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":113:131:113:133|Unknown macro t
@E: CG353 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":113:264:113:266|Expecting digit in radix 16
@E: CG350 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":113:322:113:325|Expecting exponent
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":113:329:113:330|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":115:32:115:35|Unknown macro M1
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":115:105:115:106|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":117:2:117:4|Unknown macro q
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":117:25:117:26|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":117:27:117:30|Unknown macro YD
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":117:185:117:186|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":119:135:119:136|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":119:454:119:455|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS231 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":119:475:119:477|Unknown macro U
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":120:21:120:22|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":120:92:120:212|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":121:109:121:216|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":121:109:122:49|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":121:109:123:17|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":121:109:124:74|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":121:109:125:294|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":121:109:126:59|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:444:127:445|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:462:127:463|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:751:127:752|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:827:127:828|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:832:127:833|expecting identifier immediately following back-quote (`)
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:868:127:869|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:928:127:979|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:928:128:161|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:928:129:19|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":127:928:130:13|Newline in quoted string
@E: CS234 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":131:489:131:490|expecting identifier immediately following back-quote (`)
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":131:533:131:537|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":131:533:132:5|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":131:533:133:102|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":134:181:134:352|Newline in quoted string
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":134:181:135:66|Newline in quoted string
@E: CG431 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":136:308:136:309|Expecting radix character (one of b, o, h, or d) or unsized single bit literal (one of '1, '0, 'x, 'z) 
@E: CD489 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":136:326:136:354|Newline in quoted string
@E: CG354 :"D:\Project\INS350_5J_JZ\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":136:326:136:354|EOF in quoted string

