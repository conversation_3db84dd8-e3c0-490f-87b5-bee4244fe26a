# INS350_5J_JZ Pin Assignment File
# LFE5U-25F CABGA256 Package

# Clock Constraints
FREQUENCY NET "clk_in" 25.000000 MHz;
FREQUENCY NET "clk120mhz" 120.000000 MHz;
FREQUENCY NET "clk_AD" 60.000000 MHz;
FREQUENCY NET "clk_AD_t" 60.000000 MHz;

# Pin Assignments
# Input Clock
LOCATE COMP "clk_in" SITE "L16";
IOBUF PORT "clk_in" IO_TYPE=LVCMOS33;

# AD Data Bus (12-bit)
LOCATE COMP "AD_DATA[0]" SITE "T6";
LOCATE COMP "AD_DATA[1]" SITE "R6";
LOCATE COMP "AD_DATA[2]" SITE "R5";
LOCATE COMP "AD_DATA[3]" SITE "T4";
LOCATE COMP "AD_DATA[4]" SITE "R4";
LOCATE COMP "AD_DATA[5]" SITE "T3";
LOCATE COMP "AD_DATA[6]" SITE "R3";
LOCATE COMP "AD_DATA[7]" SITE "T2";
LOCATE COMP "AD_DATA[8]" SITE "R1";
LOCATE COMP "AD_DATA[9]" SITE "P1";
LOCATE COMP "AD_DATA[10]" SITE "N1";
LOCATE COMP "AD_DATA[11]" SITE "M1";
IOBUF PORT "AD_DATA[0]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[1]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[2]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[3]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[4]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[5]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[6]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[7]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[8]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[9]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[10]" IO_TYPE=LVCMOS33;
IOBUF PORT "AD_DATA[11]" IO_TYPE=LVCMOS33;

# DA Data Bus (14-bit)
LOCATE COMP "DA_DATA[0]" SITE "R12";
LOCATE COMP "DA_DATA[1]" SITE "P12";
LOCATE COMP "DA_DATA[2]" SITE "P13";
LOCATE COMP "DA_DATA[3]" SITE "T13";
LOCATE COMP "DA_DATA[4]" SITE "R13";
LOCATE COMP "DA_DATA[5]" SITE "T14";
LOCATE COMP "DA_DATA[6]" SITE "R14";
LOCATE COMP "DA_DATA[7]" SITE "T15";
LOCATE COMP "DA_DATA[8]" SITE "R15";
LOCATE COMP "DA_DATA[9]" SITE "R16";
LOCATE COMP "DA_DATA[10]" SITE "P15";
LOCATE COMP "DA_DATA[11]" SITE "P16";
LOCATE COMP "DA_DATA[12]" SITE "N16";
LOCATE COMP "DA_DATA[13]" SITE "M16";
IOBUF PORT "DA_DATA[0]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[1]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[2]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[3]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[4]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[5]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[6]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[7]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[8]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[9]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[10]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[11]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[12]" IO_TYPE=LVCMOS33;
IOBUF PORT "DA_DATA[13]" IO_TYPE=LVCMOS33;

# Clock Outputs
# Note: N9 is not a user-assignable pin in LFE5U-25F CABGA256
# Using alternative pins for clock outputs
LOCATE COMP "clk_AD_t" SITE "L1";  # Alternative to N9
LOCATE COMP "clk_DA" SITE "M15";
IOBUF PORT "clk_AD_t" IO_TYPE=LVCMOS33;
IOBUF PORT "clk_DA" IO_TYPE=LVCMOS33;

# UART Interface
LOCATE COMP "TXD" SITE "A14";
LOCATE COMP "RXD" SITE "B14";
LOCATE COMP "TxTransmit" SITE "F16";
LOCATE COMP "RxTransmit" SITE "G16";
IOBUF PORT "TXD" IO_TYPE=LVCMOS33;
IOBUF PORT "RXD" IO_TYPE=LVCMOS33;
IOBUF PORT "TxTransmit" IO_TYPE=LVCMOS33;
IOBUF PORT "RxTransmit" IO_TYPE=LVCMOS33;

# DS18B20 Interface
LOCATE COMP "dq" SITE "E16";
IOBUF PORT "dq" IO_TYPE=LVCMOS33;

# PLL Location
LOCATE COMP "CLK120/PLLInst_0" SITE "PLL_BR0";

# Configuration
CONFIG MODE=JTAG;
