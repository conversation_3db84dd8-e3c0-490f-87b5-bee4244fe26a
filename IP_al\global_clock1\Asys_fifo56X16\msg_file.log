SCUBA, Version Diamond (64-bit) 3.12.1.454
Thu Mar 13 14:50:20 2025
  
Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
  
BEGIN SCUBA Module Synthesis
  
    Issued command   : D:\Software\lscc\diamond\3.12\ispfpga\bin\nt64\scuba.exe -w -n Asys_fifo56X16 -lang verilog -synth synplify -bus_exp 7 -bb -arch sa5p00 -type ebfifo -sync_mode -depth 128 -width 56 -no_enable -pe 10 -pe2 12 -pf 128 -pf2 126 -reset_rel SYNC 
    Circuit name     : Asys_fifo56X16
    Module type      : fifoblk
    Module Version   : 5.1
    Ports            : 
    Inputs       : Data[55:0], Clock, WrEn, RdEn, Reset
    Outputs      : Q[55:0], Empty, Full, AlmostEmpty, AlmostFull
    I/O buffer       : not inserted
    EDIF output      : Asys_fifo56X16.edn
    Verilog output   : Asys_fifo56X16.v
    Verilog template : Asys_fifo56X16_tmpl.v
    Verilog testbench: tb_Asys_fifo56X16_tmpl.v
    Verilog purpose  : for synthesis and simulation
    Bus notation     : big endian
    Report output    : Asys_fifo56X16.srp
    Estimated Resource Usage:
            LUT : 111
            EBR : 2
            Reg : 28
  
END   SCUBA Module Synthesis

