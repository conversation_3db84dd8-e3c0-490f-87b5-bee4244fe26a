lappend auto_path "D:/Software/lscc/diamond/3.12/data/script"
package require tbtemplate_generation

set ::bali::Para(MODNAME) global_clock
set ::bali::Para(PROJECT) INS350_5J_JZ
set ::bali::Para(PACKAGE) {"D:/Software/lscc/diamond/3.12/cae_library/vhdl_packages/vdbs"}
set ::bali::Para(PRIMITIVEFILE) {"D:/Software/lscc/diamond/3.12/cae_library/synthesis/verilog/ecp5u.v"}
set ::bali::Para(TFT) {"D:/Software/lscc/diamond/3.12/data/templates/plsitft.tft"}
set ::bali::Para(OUTNAME) INS350_5J_JZ_tf
set ::bali::Para(EXT) .v
set ::bali::Para(FILELIST) {"D:/Project/INS350_5J_JZ/Src_al/INS350_5J_JZ.v=work,Verilog_2001" "D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.v=work,Verilog_2001" }
set ::bali::Para(INCLUDEPATH) {"D:/Project/INS350_5J_JZ/Src_al" "D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock" }
puts "set parameters done"
::bali::GenerateTbTemplate
