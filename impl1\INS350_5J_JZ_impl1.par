PAR: Place And Route Diamond (64-bit) 3.12.1.454.
Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
Tue Jun 24 11:15:19 2025

D:/Software/lscc/diamond/3.12/ispfpga\bin\nt64\par -f INS350_5J_JZ_impl1.p2t
INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.dir INS350_5J_JZ_impl1.prf -gui
-msgset D:/Project/INS350_5J_JZ _V2/promote.xml


Preference file: INS350_5J_JZ_impl1.prf.

Level/       Number       Worst        Timing       Worst        Timing       Run          NCD
Cost [ncd]   Unrouted     Slack        Score        Slack(hold)  Score(hold)  Time         Status
----------   --------     -----        ------       -----------  -----------  ----         ------
5_1   *      0            1.227        0            0.175        0            48           Completed

* : Design saved.

Total (real) run time for 1-seed: 49 secs 

par done!

Note: user must run 'Trace' for timing closure signoff.

Lattice Place and Route Report for Design "INS350_5J_JZ_impl1_map.ncd"
Tue Jun 24 11:15:19 2025

PAR: Place And Route Diamond (64-bit) 3.12.1.454.
Command Line: par -w -l 5 -i 6 -t 1 -c 0 -e 0 -gui -msgset "D:/Project/INS350_5J_JZ _V2/promote.xml" -exp parUseNBR=1:parCDP=auto:parCDR=1:parPathBased=OFF:parASE=1 INS350_5J_JZ_impl1_map.ncd INS350_5J_JZ_impl1.dir/5_1.ncd INS350_5J_JZ_impl1.prf
Preference file: INS350_5J_JZ_impl1.prf.
Placement level-cost: 5-1.
Routing Iterations: 6

Loading design for application par from file INS350_5J_JZ_impl1_map.ncd.
Design name: INS350_5J_JZ
NCD version: 3.3
Vendor:      LATTICE
Device:      LFE5U-25F
Package:     CABGA256
Performance: 7
Loading device for application par from file 'sa5p25.nph' in environment: D:/Software/lscc/diamond/3.12/ispfpga.
Package Status:                     Final          Version 1.42.
Performance Hardware Data Status:   Final          Version 55.1.
License checked out.


Ignore Preference Error(s):  True
Device utilization summary:

   PIO (prelim)      32/197          16% used
                     32/197          16% bonded
   IOLOGIC           28/199          14% used

   SLICE            692/12144         5% used

   GSR                1/1           100% used
   EBR                2/56            3% used
   PLL                1/2            50% used
   MULT18             4/28           14% used
   ALU54              1/14            7% used


Number of Signals: 2137
Number of Connections: 4155

Pin Constraint Summary:
   32 out of 32 pins locked (100% locked).


The following 12 signals are selected to use the primary clock routing resources:
    clk120mhz (driver: CLK120/PLLInst_0, clk/ce/sr load #: 444/0/0)
    clk_AD (driver: CLK120/PLLInst_0, clk/ce/sr load #: 101/0/0)
    wendu.clk_us (driver: wendu/SLICE_224, clk/ce/sr load #: 36/0/0)
    signal_process/demodu/AD_validcnt7 (driver: signal_process/demodu/SLICE_283, clk/ce/sr load #: 0/0/33)
    signal_process/rs422/trans_state[5] (driver: signal_process/rs422/SLICE_479, clk/ce/sr load #: 0/0/31)
    signal_process/rs422/N_80_i (driver: signal_process/rs422/SLICE_626, clk/ce/sr load #: 0/31/0)
    signal_process/demodu/un1_AD_validcntlto7_0_a2 (driver: signal_process/demodu/SLICE_643, clk/ce/sr load #: 0/29/0)
    signal_process/polarity (driver: signal_process/ctrl_signal/SLICE_403, clk/ce/sr load #: 0/28/0)
    signal_process/integ/DA_dout5 (driver: signal_process/integ/SLICE_640, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/median_sum_n_1_sqmuxa (driver: signal_process/demodu/SLICE_621, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/Latch_sum (driver: signal_process/demodu/SLICE_285, clk/ce/sr load #: 0/28/0)
    signal_process/demodu/N_415_i (driver: signal_process/demodu/SLICE_621, clk/ce/sr load #: 0/28/0)


Signal CLK120/locked_out is selected as Global Set/Reset.
Starting Placer Phase 0.
...............
Finished Placer Phase 0.  REAL time: 7 secs 


Starting Placer Phase 1.
......................
Placer score = 330570.
Finished Placer Phase 1.  REAL time: 18 secs 

Starting Placer Phase 2.
.
Placer score =  285515
Finished Placer Phase 2.  REAL time: 19 secs 


------------------ Clock Report ------------------

Global Clock Resources:
  CLK_PIN    : 0 out of 12 (0%)
  GR_PCLK    : 0 out of 12 (0%)
  PLL        : 1 out of 2 (50%)
  DCS        : 0 out of 2 (0%)
  DCC        : 0 out of 60 (0%)
  CLKDIV     : 0 out of 4 (0%)

Quadrant TL Clocks:
  PRIMARY "wendu.clk_us" from Q0 on comp "wendu/SLICE_224" on site "R38C31A", CLK/CE/SR load = 19

  PRIMARY  : 1 out of 16 (6%)

Quadrant TR Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 367
  PRIMARY "clk_AD" from CLKOS on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 12
  PRIMARY "wendu.clk_us" from Q0 on comp "wendu/SLICE_224" on site "R38C31A", CLK/CE/SR load = 17
  PRIMARY "signal_process/rs422/trans_state[5]" from Q0 on comp "signal_process/rs422/SLICE_479" on site "R19C46A", CLK/CE/SR load = 31
  PRIMARY "signal_process/rs422/N_80_i" from F1 on comp "signal_process/rs422/SLICE_626" on site "R30C50D", CLK/CE/SR load = 31
  PRIMARY "signal_process/polarity" from Q0 on comp "signal_process/ctrl_signal/SLICE_403" on site "R23C46C", CLK/CE/SR load = 28
  PRIMARY "signal_process/integ/DA_dout5" from F0 on comp "signal_process/integ/SLICE_640" on site "R24C46B", CLK/CE/SR load = 28
  PRIMARY "signal_process/demodu/N_415_i" from F0 on comp "signal_process/demodu/SLICE_621" on site "R22C48B", CLK/CE/SR load = 27

  PRIMARY  : 8 out of 16 (50%)

Quadrant BL Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 1
  PRIMARY "clk_AD" from CLKOS on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 17

  PRIMARY  : 2 out of 16 (12%)

Quadrant BR Clocks:
  PRIMARY "clk120mhz" from CLKOP on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 76
  PRIMARY "clk_AD" from CLKOS on comp "CLK120/PLLInst_0" on PLL site "PLL_BR0", CLK/CE/SR load = 72
  PRIMARY "signal_process/demodu/AD_validcnt7" from F0 on comp "signal_process/demodu/SLICE_283" on site "R28C44D", CLK/CE/SR load = 33
  PRIMARY "signal_process/demodu/un1_AD_validcntlto7_0_a2" from F0 on comp "signal_process/demodu/SLICE_643" on site "R30C44A", CLK/CE/SR load = 29
  PRIMARY "signal_process/demodu/median_sum_n_1_sqmuxa" from F1 on comp "signal_process/demodu/SLICE_621" on site "R22C48B", CLK/CE/SR load = 28
  PRIMARY "signal_process/demodu/Latch_sum" from Q0 on comp "signal_process/demodu/SLICE_285" on site "R30C44D", CLK/CE/SR load = 28
  PRIMARY "signal_process/demodu/N_415_i" from F0 on comp "signal_process/demodu/SLICE_621" on site "R22C48B", CLK/CE/SR load = 1

  PRIMARY  : 7 out of 16 (43%)

Edge Clocks:

  No edge clock selected.


--------------- End of Clock Report ---------------


+
I/O Usage Summary (final):
   32 out of 197 (16.2%) PIO sites used.
   32 out of 197 (16.2%) bonded PIO sites used.
   Number of PIO comps: 32; differential: 0.
   Number of Vref pins used: 0.

I/O Bank Usage Summary:
+----------+----------------+------------+------------+------------+
| I/O Bank | Usage          | Bank Vccio | Bank Vref1 | Bank Vref2 |
+----------+----------------+------------+------------+------------+
| 0        | 0 / 24 (  0%)  | 3.3V       | -          | -          |
| 1        | 1 / 32 (  3%)  | 3.3V       | -          | -          |
| 2        | 2 / 32 (  6%)  | 3.3V       | -          | -          |
| 3        | 16 / 32 ( 50%) | 3.3V       | -          | -          |
| 6        | 11 / 32 ( 34%) | 3.3V       | -          | -          |
| 7        | 0 / 32 (  0%)  | 3.3V       | -          | -          |
| 8        | 2 / 13 ( 15%)  | 3.3V       | -          | -          |
+----------+----------------+------------+------------+------------+

---------------------------------- DSP Report ----------------------------------

DSP Slice #:           1  2  3  4  5  6  7  8  9 10 11 12 13 14
# of MULT9                                                     
# of MULT18                                          2  1  1   
# of ALU24                                                     
# of ALU54                                           1         
# of PRADD9                                                    
# of PRADD18                                                   

DSP Slice 11         Component_Type       Physical_Type                    Instance_Name                  
 MULT18_R13C51         MULT18X18D             MULT18            signal_process/rs422/un3_p_sum[0:35]      
 MULT18_R13C52         MULT18X18D             MULT18           signal_process/rs422/un3_p_sum[18:53]      
  ALU54_R13C54           ALU54B               ALU54           signal_process/rs422/un3_p_sum_add[0:53]    

DSP Slice 12         Component_Type       Physical_Type                    Instance_Name                  
 MULT18_R13C55         MULT18X18D             MULT18           signal_process/rs422/un3_p_sum[36:71]      

DSP Slice 13         Component_Type       Physical_Type                    Instance_Name                  
 MULT18_R13C60         MULT18X18D             MULT18          signal_process/modu/un1_dout_mult[25:0]     

------------------------------ End of DSP Report -------------------------------
Total placer CPU time: 19 secs 

Dumping design to file INS350_5J_JZ_impl1.dir/5_1.ncd.

0 connections routed; 4155 unrouted.
Starting router resource preassignment
DSP info: No dsp pins have been swapped.

Completed router resource preassignment. Real time: 30 secs 

Start NBR router at 11:15:49 06/24/25

*****************************************************************
Info: NBR allows conflicts(one node used by more than one signal)
      in the earlier iterations. In each iteration, it tries to  
      solve the conflicts while keeping the critical connections 
      routed as short as possible. The routing process is said to
      be completed when no conflicts exist and all connections   
      are routed.                                                
Note: NBR uses a different method to calculate timing slacks. The
      worst slack and total negative slack may not be the same as
      that in TRCE report. You should always run TRCE to verify  
      your design.                                               
*****************************************************************

Start NBR special constraint process at 11:15:51 06/24/25

Start NBR section for initial routing at 11:15:51 06/24/25
Level 1, iteration 1
0(0.00%) conflict; 2542(61.18%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.127ns/0.000ns; real time: 32 secs 
Level 2, iteration 1
5(0.00%) conflicts; 2526(60.79%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 33 secs 
Level 3, iteration 1
24(0.00%) conflicts; 2211(53.21%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 33 secs 
Level 4, iteration 1
127(0.01%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 34 secs 

Info: Initial congestion level at 75% usage is 0
Info: Initial congestion area  at 75% usage is 0 (0.00%)

Start NBR section for normal routing at 11:15:53 06/24/25
Level 1, iteration 1
6(0.00%) conflicts; 137(3.30%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.190ns/0.000ns; real time: 35 secs 
Level 1, iteration 2
5(0.00%) conflicts; 138(3.32%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.099ns/0.000ns; real time: 35 secs 
Level 1, iteration 3
2(0.00%) conflicts; 139(3.35%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 35 secs 
Level 1, iteration 4
3(0.00%) conflicts; 138(3.32%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 35 secs 
Level 2, iteration 1
2(0.00%) conflicts; 139(3.35%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 35 secs 
Level 3, iteration 1
8(0.00%) conflicts; 130(3.13%) untouched conns; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 36 secs 
Level 4, iteration 1
61(0.01%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 36 secs 
Level 4, iteration 2
24(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 36 secs 
Level 4, iteration 3
4(0.00%) conflicts; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 36 secs 
Level 4, iteration 4
1(0.00%) conflict; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 37 secs 
Level 4, iteration 5
0(0.00%) conflict; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 37 secs 

Start NBR section for setup/hold timing optimization with effort level 3 at 11:15:56 06/24/25

Start NBR section for re-routing at 11:15:59 06/24/25
Level 4, iteration 1
0(0.00%) conflict; 0(0.00%) untouched conn; 0 (nbr) score; 
Estimated worst slack/total negative slack<setup>: 1.227ns/0.000ns; real time: 40 secs 

Start NBR section for post-routing at 11:15:59 06/24/25

End NBR router with 0 unrouted connection

NBR Summary
-----------
  Number of unrouted connections : 0 (0.00%)
  Number of connections with timing violations : 0 (0.00%)
  Estimated worst slack<setup> : 1.227ns
  Timing score<setup> : 0
-----------
Notes: The timing info is calculated for SETUP only and all PAR_ADJs are ignored.



Total CPU time 46 secs 
Total REAL time: 48 secs 
Completely routed.
End of route.  4155 routed (100.00%); 0 unrouted.

Hold time timing score: 0, hold timing errors: 0

Timing score: 0 

Dumping design to file INS350_5J_JZ_impl1.dir/5_1.ncd.


All signals are completely routed.


PAR_SUMMARY::Run status = Completed
PAR_SUMMARY::Number of unrouted conns = 0
PAR_SUMMARY::Worst  slack<setup/<ns>> = 1.227
PAR_SUMMARY::Timing score<setup/<ns>> = 0.000
PAR_SUMMARY::Worst  slack<hold /<ns>> = 0.175
PAR_SUMMARY::Timing score<hold /<ns>> = 0.000
PAR_SUMMARY::Number of errors = 0

Total CPU  time to completion: 47 secs 
Total REAL time to completion: 49 secs 

par done!

Note: user must run 'Trace' for timing closure signoff.

Copyright (c) 1991-1994 by NeoCAD Inc. All rights reserved.
Copyright (c) 1995 AT&T Corp.   All rights reserved.
Copyright (c) 1995-2001 Lucent Technologies Inc.  All rights reserved.
Copyright (c) 2001 Agere Systems   All rights reserved.
Copyright (c) 2002-2020 Lattice Semiconductor Corporation,  All rights reserved.
