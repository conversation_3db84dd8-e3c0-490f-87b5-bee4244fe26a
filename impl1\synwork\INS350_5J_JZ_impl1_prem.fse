
fsm_encoding {14930933} onehot

fsm_state_encoding {14930933} INIT1 {000001}

fsm_state_encoding {14930933} WR_CMD {000010}

fsm_state_encoding {14930933} WAIT {000100}

fsm_state_encoding {14930933} INIT2 {001000}

fsm_state_encoding {14930933} RD_CMD {010000}

fsm_state_encoding {14930933} RD_DATA {100000}

fsm_registers {14930933} {cur_state[5]}  {cur_state[4]}  {cur_state[3]}  {cur_state[2]}  {cur_state[1]}  {cur_state[0]} 

fsm_encoding {13890892} onehot

fsm_state_encoding {13890892} tx_idle {00000000001}

fsm_state_encoding {13890892} tx_Header1 {00000000010}

fsm_state_encoding {13890892} tx_Header2 {00000000100}

fsm_state_encoding {13890892} tx_First {00000001000}

fsm_state_encoding {13890892} tx_Second {00000010000}

fsm_state_encoding {13890892} tx_Third {00000100000}

fsm_state_encoding {13890892} tx_Fourth {00001000000}

fsm_state_encoding {13890892} tx_Fifth {00010000000}

fsm_state_encoding {13890892} tx_Sixth {00100000000}

fsm_state_encoding {13890892} tx_Seventh {01000000000}

fsm_state_encoding {13890892} tx_end {10000000000}

fsm_registers {13890892} {tx_state[10]}  {tx_state[9]}  {tx_state[8]}  {tx_state[7]}  {tx_state[6]}  {tx_state[5]}  {tx_state[4]}  {tx_state[3]}  {tx_state[2]}  {tx_state[1]}  {tx_state[0]} 

fsm_encoding {17930931} onehot

fsm_state_encoding {17930931} idle_s {000001}

fsm_state_encoding {17930931} wait_1us_s {000010}

fsm_state_encoding {17930931} dalay_state {000100}

fsm_state_encoding {17930931} check_data_stable_s {001000}

fsm_state_encoding {17930931} transmit_data_s {010000}

fsm_state_encoding {17930931} clear_data_s {100000}

fsm_registers {17930931} {trans_state[5]}  {trans_state[4]}  {trans_state[3]}  {trans_state[2]}  {trans_state[1]}  {trans_state[0]} 
