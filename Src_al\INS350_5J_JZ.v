`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/22/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	IFOG501_2B 
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	IFOG top
// Revision 1.01 - File Created
// Additional Comments: 
//
//////////////////////////////////////////////////////////////////////////////////
module INS350_5J_JZ
(
	input 			clk_in, //external clock 25MHz
	inout 			dq, 	//DS18B20 interface
	input			RXD, //RXD serial in data	
	input			RxTransmit, //external transmit clock 200hz
	output			TXD, //TXD serial out data
	output			TxTransmit, //internal transmit clock
	input	[11:0]	AD_DATA, //AD data
	output	[13:0]	DA_DATA, //DA data
	output			clk_AD_t,  //AD clock
	output			clk_DA   //DA clock
	);


//////////////////////////////////////////////////////////////////////////////////////////////////
parameter 	iWID_AD=12;	
parameter 	iWID_DA=14; 			
parameter	DA_CONSTANT=14900;	//14200-1 //15010-2 //14410-3 //13550-m//904-13850-1//904-14250-3
parameter	iWID_RS422=32;		
parameter 	RX_SYS_FREQ =120000000;  
parameter 	RX_UART_BAUD=115200;    
parameter 	TX_SYS_FREQ =120000000; 
parameter 	TX_UART_BAUD=115200;    
parameter 	iWID_TRANS=13;		  	//Set the number of transfers
parameter	iWID_SIGN=5;		  	//SIGN DATA width
parameter	wCLOSED=1'b1;		  	//1'b1:CLOSED loop; 1'b0: open loop
parameter	iTRANSIT_TIME=182;	  	//set transit-time 	180-3 182-1	181-m 180-2								
parameter	iAD_VALID_START=90;	  	//sample valid start point 96-3 102-1 96-2 96-m
parameter 	acum_cnt=16;		  	//Sampling times 18-3 17-1 17-2 22-m
parameter	iFEEDBACK_SCALE=10;	  	//feedback data begining point
parameter	bPOLAR=0;	  			//Polarity selection
parameter	iOUTPUT_SCALE=1350;	  	//output data begining point
parameter	iDELAYED=120;		  	//delay process counter for transmit signal
parameter	iTRANSMIT_COFF=600000; 	//transmit signal divider coff
parameter	RX_TX=1'b0; 	//transmit signal divider coff 

wire					TxTransmitt;
wire					test_RX;
//signal of DCM and BUFGCE
wire					clk120mhz;
//wire					clk_AD_t;
wire 					locked_out;
wire 					rst_n;
wire 					CLKFX_OUT;
wire					rd_done;
//signal of UART
wire [7:0] 				RX_DATA;
//
wire 					transmit;
wire [iWID_RS422-1:0] 	RS422_DATA;
wire [15:0]  			temp_data;	


global_clock  CLK120
(
	.CLKI(clk_in),
    .RST(1'B0),
    .CLKOP(clk120mhz),
    .CLKOS(clk_AD),
    .CLKOS2(clk_AD_t),
    .LOCK(locked_out)
);


assign rst_n = locked_out;
assign test_RX = (RX_TX)?RxTransmit:TxTransmit;

DS18B20 wendu(
	.clk(clk120mhz),		
	.rst_n(rst_n),  	
	.dq(dq),			
	.rd_done(rd_done),	
	.rd_flag(),	
	.temp_data(temp_data)
	);

//UART communication
UART_Control 
#(
	.iWID_RS422(iWID_RS422), 	
	.RX_SYS_FREQ(RX_SYS_FREQ), 	
	.RX_UART_BAUD(RX_UART_BAUD),
	.TX_SYS_FREQ(TX_SYS_FREQ),  
	.TX_UART_BAUD(TX_UART_BAUD) 
	)
u_uart(
	.clk(clk120mhz),
	.rst_n(rst_n),
	.transmit(transmit),
	.rd_done(rd_done),	//温度数据读完
	.temp_data(temp_data),
	.RS422_DATA(RS422_DATA),
	.rx_data(RX_DATA),
	.TXD(TXD),
	.RXD(RXD)
);
	
//closed-loop signal processing of FOG
SignalProcessing 
#(
	.acum_cnt(acum_cnt),
	.iWID_TRANS(iWID_TRANS),			//Set the number of transfers
	.iWID_AD(iWID_AD),			 		//AD width
	.iWID_DA(iWID_DA),			 		//DA width
	.iWID_RS422(iWID_RS422),		 	//RS422 DATA width
	.iWID_SIGN(iWID_SIGN),		 		//SIGN DATA width
	.wCLOSED(wCLOSED),		 			//CLOSED loop or open loop
	.iTRANSIT_TIME(iTRANSIT_TIME),	 	//set transit-time 									
	.iAD_VALID_START(iAD_VALID_START),	//sample valid start point
	.iFEEDBACK_SCALE(iFEEDBACK_SCALE),	//feedback data begining point
	.iOUTPUT_SCALE(iOUTPUT_SCALE),	 	//output data begining point
	.bPOLAR(bPOLAR),	 				//Polarity selection
	.iDELAYED(iDELAYED),		 		//delay process counter for transmit signal
	.DA_CONSTANT(DA_CONSTANT),		 	//b OF multiplier
	.iTRANSMIT_COFF(iTRANSMIT_COFF)		//transmit signal divider coff
)
signal_process
(
	.rst_n(rst_n),
	.clk(clk120mhz),
	.RxTransmit(test_RX),
	.AD_DATA(AD_DATA),
	.TxTransmit(TxTransmit),
	.clk_AD(clk_AD),
	.clk_DA(clk_DA),
	.DA_DATA(DA_DATA),
	.transmit(transmit),
	.RS422_DATA(RS422_DATA)
);
endmodule
