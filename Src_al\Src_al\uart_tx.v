`timescale 1ns / 1ps
//////////////////////////////////////////////////////////////////////////////////
// Company: tianhailu
// Engineer: Andrew
// 
// Create Date:    	15:33:33 08/03/2022 
// Design Name: 	IFOG501_2B
// Module Name:    	uart_tx
// Project Name: 	IFOG501_2B
// Target Devices: 
// Tool versions: 	TD5.6.2-64bit
// Description: 	串口发送——协议// Revision 1.01 - File Created
// Additional Comments: 
// /*synthesis keep*/
//////////////////////////////////////////////////////////////////////////////////
module uart_tx(		
	input                 clk      ,	//120MHz主时钟
	input                 rst_n    ,	//低电平复位有效	
	input                 clk_bps  ,	//clk_bps_r高电平为接收数据位的中间采样点，同时也作为发送数据的数据改变节点	
	input    [7:0]        data_tx  ,	//接收数据寄存器
	input                 tx_wr   ,		//开始发送数据
	output reg            uart_tx  ,	//RS232发送数据信号
	output reg            tx_done  ,
	output reg            bps_start		//开始接收或者发送数据，波特率时钟启动开始信号
	);
	
//---------------------------------------------------------
reg	[7:0] 	tx_data;	//待发送数据的寄存器
reg 		bps_start_r;
reg 		tx_en;	//发送数据使能信号，高有效
reg	[3:0] 	num;

always @ (posedge clk or negedge rst_n) begin
	if(rst_n==1'b0) begin
		bps_start_r <= 1'b0;
		tx_en <= 1'b0;
		tx_data <= 8'd0;
		tx_done <= 1'b0;
	end
	else if(tx_wr) begin	 //接收数据完毕
		bps_start_r <= 1'b1;
		tx_data <= data_tx;	 //把接收到的数据存入发送数据寄存器
		tx_en <= 1'b1;		    //进入发送数据状态中
		tx_done <= 1'b0;
	end
	else if(num==4'd12) begin	 //数据发送完成，复位
		bps_start_r <= 1'b0;
		tx_en <= 1'b0;
		tx_done <= 1'b1;
	end
	else begin
	    tx_done <= 1'b0; 
		tx_en 	<= tx_en; 
		tx_data <= tx_data;
		bps_start_r <= bps_start_r;
	end
end

//bps_start_r:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	bps_start <= bps_start_r;
end

//---------------------------------------------------------
reg 	even_bit;	//偶校验位
reg 	odd_bit;	//奇校验位
reg 	uart_tx_r;	//发送bit

always @ (posedge clk or negedge rst_n) begin
	if(rst_n==1'b0) begin
		num 	 <= 4'd0;
		uart_tx_r<= 1'b1;
        even_bit <= 1'b0;
        odd_bit  <= 1'b0;
	end
	else if(tx_en) begin
			if(clk_bps)	begin
				num <= num+1'b1;
				even_bit <= ^tx_data;   //偶校验需求下计算出来的校验位
				odd_bit  <= !(^tx_data);//奇校验需求下计算出来的校验位
				case (num)
					4'd0 : uart_tx_r <= 1'b0; 	    //发送起始位
					4'd1 : uart_tx_r <= tx_data[0];	//发送bit0
					4'd2 : uart_tx_r <= tx_data[1];	//发送bit1
					4'd3 : uart_tx_r <= tx_data[2];	//发送bit2
					4'd4 : uart_tx_r <= tx_data[3];	//发送bit3
					4'd5 : uart_tx_r <= tx_data[4];	//发送bit4
					4'd6 : uart_tx_r <= tx_data[5];	//发送bit5
					4'd7 : uart_tx_r <= tx_data[6];	//发送bit6
					4'd8 : uart_tx_r <= tx_data[7];	//发送bit7
					4'd9 : uart_tx_r <= even_bit;	//发送偶校验位			
					4'd10: uart_tx_r<= 1'b1;	    //发送结束位
					default: uart_tx_r <= 1'b1;
				endcase
			end
			else if(num==4'd12)begin //光纤陀螺单帧共11bit
				num <= 4'd0;	//复位
				even_bit <= 1'b0;
				odd_bit  <= 1'b0;			
			end
	end
end

//uart_tx_r:Delay one beat to make it easy to latch the data
always@(posedge clk)begin
	uart_tx <= uart_tx_r;
end

endmodule


