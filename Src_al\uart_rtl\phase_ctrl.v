module phase_ctrl(
input clk,
input rstn,
input en,
input [3:0] ctrl_code,
output psclk,
output psdown,
output psstep,
output [2:0]psclksel
)   ;
	reg [2:0]en_reg;  
	reg [3:0] code_reg;
	assign en_rise= en_reg[1] & ~en_reg[2];    
	always @(posedge clk or negedge rstn ) begin
		if (rstn==1'b0) begin
			en_reg<='b0;
			code_reg<='b0;
		end
		else begin
			en_reg <={en_reg[1:0],en};
			code_reg<=(en_rise==1'b1)? ctrl_code:code_reg;
		end
	end //end always
	 
	assign psdown=code_reg[3];
	assign psclksel=code_reg[2:0];
	
	reg [7:0] cnt;
	reg flag;
	always @(posedge clk or negedge rstn) begin
		if (rstn==1'b0) begin
			cnt<='b0;
			flag<=1'b0;
		end
		else begin
			flag<=(en_rise==1'b1)? 1'b1:
							(cnt=='d9)? 1'b0:flag;
			cnt<=(flag==1'b1)?cnt+1'b1:'b0;
		end
	end//end always   
	assign psclk=cnt[0];  
	//assign psstep=(cnt[7:1]=='d1 )? 1'b1:1'b0; //1  clk cycle
	assign psstep=(cnt[7:2]=='d1 )? 1'b1:1'b0; //4  clk cycle
endmodule