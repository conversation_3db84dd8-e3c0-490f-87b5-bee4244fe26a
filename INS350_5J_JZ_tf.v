

// TOOL:     vlog2tf
// DATE:     Thu Mar 13 17:01:45 2025
 
// TITLE:    Lattice Semiconductor Corporation
// MODULE:   global_clock
// DESIGN:   global_clock
// FILENAME: INS350_5J_JZ_tf.v
// PROJECT:  INS350_5J_JZ
// VERSION:  2.0
// This file is auto generated by Diamond


`timescale 1 ns / 1 ps

// Define Module for Test Fixture
module global_clock_tf();

// Inputs
    reg CLKI;
    reg RST;


// Outputs
    wire CLKOP;
    wire CLKOS;
    wire CLKOS2;
    wire LOCK;


// Bidirs


// Instantiate the UUT
// Please check and add your parameters manually
    global_clock UUT (
        .CLKI(CLKI), 
        .RST(RST), 
        .CLKOP(CLKOP), 
        .CLKOS(CLKOS), 
        .CLKOS2(CLKOS2), 
        .LOCK(LOCK)
        );


// Initialize Inputs
// You can add your stimulus here
    initial begin
            CLKI = 0;
            RST = 0;
    end

endmodule // global_clock_tf