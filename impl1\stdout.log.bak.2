Running in Lattice mode

                               Synplify Pro (R) 

               Version R-2021.03L-SP1-1 for win64 - Dec 22, 2021 

                    Copyright (c) 1988 - 2021 Synopsys, Inc.
   This software and the associated documentation are proprietary to Synopsys,
 Inc. This software may only be used in accordance with the terms and conditions
 of a written license agreement with Synopsys, Inc. All other use, reproduction,
            or distribution of this software is strictly prohibited.

Starting:    D:\Software\lscc\diamond\3.12\synpbase\bin64\mbin\synbatch.exe
Install:     D:\Software\lscc\diamond\3.12\synpbase
Hostname:    TLH-022
Date:        Fri Jun  6 13:41:09 2025
Version:     R-2021.03L-SP1-1

Arguments:   -product synplify_pro -batch INS350_5J_JZ_impl1_synplify.tcl
ProductType: synplify_pro




log file: "D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1.srr"
Running: impl1 in foreground

Running proj_1|impl1

Running Flow: compile (Compile) on proj_1|impl1
# Fri Jun  6 13:41:09 2025

Running Flow: compile_flow (Compile Process) on proj_1|impl1
# Fri Jun  6 13:41:09 2025

Running: compiler (Compile Input) on proj_1|impl1
# Fri Jun  6 13:41:09 2025
Copied D:\Project\INS350_5J_JZ _copy\impl1\synwork\INS350_5J_JZ_impl1_comp.srs to D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1.srs

compiler completed
# Fri Jun  6 13:41:16 2025

Return Code: 0
Run Time:00h:00m:07s

Running: multi_srs_gen (Multi-srs Generator) on proj_1|impl1
# Fri Jun  6 13:41:16 2025

multi_srs_gen completed
# Fri Jun  6 13:41:17 2025

Return Code: 0
Run Time:00h:00m:01s
Copied D:\Project\INS350_5J_JZ _copy\impl1\synwork\INS350_5J_JZ_impl1_mult.srs to D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1.srs
Copied D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1.srr to D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1.srf
Complete: Compile Process on proj_1|impl1

Running: premap (Premap) on proj_1|impl1
# Fri Jun  6 13:41:17 2025

premap completed with warnings
# Fri Jun  6 13:41:19 2025

Return Code: 1
Run Time:00h:00m:02s
Complete: Compile on proj_1|impl1

Running Flow: map (Map) on proj_1|impl1
# Fri Jun  6 13:41:19 2025
License granted for 4 parallel jobs

Running: fpga_mapper (Map & Optimize) on proj_1|impl1
# Fri Jun  6 13:41:19 2025
Copied D:\Project\INS350_5J_JZ _copy\impl1\synwork\INS350_5J_JZ_impl1_m.srm to D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1.srm

fpga_mapper completed with warnings
# Fri Jun  6 13:41:28 2025

Return Code: 1
Run Time:00h:00m:09s
Complete: Map on proj_1|impl1
Copied D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1.srr to D:\Project\INS350_5J_JZ _copy\impl1\INS350_5J_JZ_impl1.srf
Complete: Logic Synthesis on proj_1|impl1
TCL script complete: "INS350_5J_JZ_impl1_synplify.tcl"
exit status=0
exit status=0
Save changes for project:
D:\Project\INS350_5J_JZ _copy\impl1\proj_1.prj
batch mode default:no
