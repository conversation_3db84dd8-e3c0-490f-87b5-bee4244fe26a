(edif global_clock
  (edifVersion 2 0 0)
  (edifLevel 0)
  (keywordMap (keywordLevel 0))
  (status
    (written
      (timestamp 2025 3 13 16 52 24)
      (program "SCUBA" (version "Diamond (64-bit) 3.12.1.454"))))
      (comment "D:\Software\lscc\diamond\3.12\ispfpga\bin\nt64\scuba.exe -w -n global_clock -lang verilog -synth lse -bus_exp 7 -bb -arch sa5p00 -type pll -fin 20.00 -fclkop 120.00 -fclkop_tol 0.0 -fclkos 60.00 -fclkos_tol 0.0 -phases 0 -fclkos2 60.00 -fclkos2_tol 0.0 -phases2 0 -phase_cntl STATIC -rst -lock -sticky -fb_mode 1 -fdc D:/Project/INS350_5J_JZ/IP_al/global_clock1/global_clock/global_clock.fdc ")
  (library ORCLIB
    (edifLevel 0)
    (technology
      (numberDefinition))
    (cell EHXPLLL
      (cellType GENERIC)
      (view view1
        (viewType NETLIST)
        (interface
          (port CLKI
            (direction INPUT))
          (port CLKFB
            (direction INPUT))
          (port PHASESEL1
            (direction INPUT))
          (port PHASESEL0
            (direction INPUT))
          (port PHASEDIR
            (direction INPUT))
          (port PHASESTEP
            (direction INPUT))
          (port PHASELOADREG
            (direction INPUT))
          (port STDBY
            (direction INPUT))
          (port PLLWAKESYNC
            (direction INPUT))
          (port RST
            (direction INPUT))
          (port ENCLKOP
            (direction INPUT))
          (port ENCLKOS
            (direction INPUT))
          (port ENCLKOS2
            (direction INPUT))
          (port ENCLKOS3
            (direction INPUT))
          (port CLKOP
            (direction OUTPUT))
          (port CLKOS
            (direction OUTPUT))
          (port CLKOS2
            (direction OUTPUT))
          (port CLKOS3
            (direction OUTPUT))
          (port LOCK
            (direction OUTPUT))
          (port INTLOCK
            (direction OUTPUT))
          (port REFCLK
            (direction OUTPUT))
          (port CLKINTFB
            (direction OUTPUT)))))
    (cell VHI
      (cellType GENERIC)
      (view view1
        (viewType NETLIST)
        (interface
          (port Z
            (direction OUTPUT)))))
    (cell VLO
      (cellType GENERIC)
      (view view1
        (viewType NETLIST)
        (interface
          (port Z
            (direction OUTPUT)))))
    (cell global_clock
      (cellType GENERIC)
      (view view1
        (viewType NETLIST)
        (interface
          (port CLKI
            (direction INPUT))
          (port RST
            (direction INPUT))
          (port CLKOP
            (direction OUTPUT))
          (port CLKOS
            (direction OUTPUT))
          (port CLKOS2
            (direction OUTPUT))
          (port LOCK
            (direction OUTPUT)))
        (property NGD_DRC_MASK (integer 1))
        (contents
          (instance scuba_vhi_inst
            (viewRef view1 
              (cellRef VHI)))
          (instance scuba_vlo_inst
            (viewRef view1 
              (cellRef VLO)))
          (instance PLLInst_0
            (viewRef view1 
              (cellRef EHXPLLL))
            (property PLLRST_ENA
              (string "ENABLED"))
            (property INTFB_WAKE
              (string "DISABLED"))
            (property STDBY_ENABLE
              (string "DISABLED"))
            (property DPHASE_SOURCE
              (string "DISABLED"))
            (property CLKOS3_FPHASE
              (string "0"))
            (property CLKOS3_CPHASE
              (string "0"))
            (property CLKOS2_FPHASE
              (string "0"))
            (property CLKOS2_CPHASE
              (string "9"))
            (property CLKOS_FPHASE
              (string "0"))
            (property CLKOS_CPHASE
              (string "9"))
            (property CLKOP_FPHASE
              (string "0"))
            (property CLKOP_CPHASE
              (string "4"))
            (property PLL_LOCK_MODE
              (string "2"))
            (property CLKOS_TRIM_DELAY
              (string "0"))
            (property CLKOS_TRIM_POL
              (string "FALLING"))
            (property CLKOP_TRIM_DELAY
              (string "0"))
            (property CLKOP_TRIM_POL
              (string "FALLING"))
            (property OUTDIVIDER_MUXD
              (string "DIVD"))
            (property CLKOS3_ENABLE
              (string "DISABLED"))
            (property FREQUENCY_PIN_CLKOS2
              (string "60.000000"))
            (property OUTDIVIDER_MUXC
              (string "DIVC"))
            (property CLKOS2_ENABLE
              (string "ENABLED"))
            (property FREQUENCY_PIN_CLKOS
              (string "60.000000"))
            (property OUTDIVIDER_MUXB
              (string "DIVB"))
            (property CLKOS_ENABLE
              (string "ENABLED"))
            (property FREQUENCY_PIN_CLKOP
              (string "120.000000"))
            (property OUTDIVIDER_MUXA
              (string "DIVA"))
            (property CLKOP_ENABLE
              (string "ENABLED"))
            (property FREQUENCY_PIN_CLKI
              (string "20.000000"))
            (property ICP_CURRENT
              (string "5"))
            (property LPF_RESISTOR
              (string "16"))
            (property CLKOS3_DIV
              (string "1"))
            (property CLKOS2_DIV
              (string "10"))
            (property CLKOS_DIV
              (string "10"))
            (property CLKOP_DIV
              (string "5"))
            (property CLKFB_DIV
              (string "6"))
            (property CLKI_DIV
              (string "1"))
            (property FEEDBK_PATH
              (string "CLKOP")))
          (net REFCLK
            (joined
              (portRef REFCLK (instanceRef PLLInst_0))))
          (net scuba_vhi
            (joined
              (portRef Z (instanceRef scuba_vhi_inst))))
          (net scuba_vlo
            (joined
              (portRef Z (instanceRef scuba_vlo_inst))
              (portRef ENCLKOS3 (instanceRef PLLInst_0))
              (portRef ENCLKOS2 (instanceRef PLLInst_0))
              (portRef ENCLKOS (instanceRef PLLInst_0))
              (portRef ENCLKOP (instanceRef PLLInst_0))
              (portRef PLLWAKESYNC (instanceRef PLLInst_0))
              (portRef STDBY (instanceRef PLLInst_0))
              (portRef PHASELOADREG (instanceRef PLLInst_0))
              (portRef PHASESTEP (instanceRef PLLInst_0))
              (portRef PHASEDIR (instanceRef PLLInst_0))
              (portRef PHASESEL1 (instanceRef PLLInst_0))
              (portRef PHASESEL0 (instanceRef PLLInst_0))))
          (net LOCK
            (joined
              (portRef LOCK)
              (portRef LOCK (instanceRef PLLInst_0))))
          (net CLKOS2
            (joined
              (portRef CLKOS2)
              (portRef CLKOS2 (instanceRef PLLInst_0))))
          (net CLKOS
            (joined
              (portRef CLKOS)
              (portRef CLKOS (instanceRef PLLInst_0))))
          (net CLKOP
            (joined
              (portRef CLKOP)
              (portRef CLKFB (instanceRef PLLInst_0))
              (portRef CLKOP (instanceRef PLLInst_0))))
          (net RST
            (joined
              (portRef RST)
              (portRef RST (instanceRef PLLInst_0))))
          (net CLKI
            (joined
              (portRef CLKI)
              (portRef CLKI (instanceRef PLLInst_0))))))))
  (design global_clock
    (cellRef global_clock
      (libraryRef ORCLIB)))
)
