Selecting top level module INS350_5J_JZ
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":757:7:757:9|Synthesizing module VHI in library work.
Running optimization stage 1 on VHI .......
Finished optimization stage 1 on VHI (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 110MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":761:7:761:9|Synthesizing module VLO in library work.
Running optimization stage 1 on VLO .......
Finished optimization stage 1 on VLO (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 110MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":1696:7:1696:13|Synthesizing module EHXPLLL in library work.
Running optimization stage 1 on EHXPLLL .......
Finished optimization stage 1 on EHXPLLL (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 110MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\global_clock\global_clock.v":8:7:8:18|Synthesizing module global_clock in library work.
Running optimization stage 1 on global_clock .......
@W: CL168 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\global_clock\global_clock.v":23:8:23:21|Removing instance scuba_vhi_inst because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
Finished optimization stage 1 on global_clock (CPU Time 0h:00m:00s, Memory Used current: 110MB peak: 110MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":17:7:17:13|Synthesizing module DS18B20 in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":85:12:85:17|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":189:20:189:27|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":248:20:248:27|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":291:15:291:18|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":307:21:307:29|Removing redundant assignment.
Running optimization stage 1 on DS18B20 .......
@A: CL282 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":157:0:157:5|Feedback mux created for signal rd_flag. It is possible a set/reset assignment for this is signal missing. To improve timing and area, specify a set/reset value.
@W: CL190 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":157:0:157:5|Optimizing register bit dq_out to a constant 0. To keep the instance, apply constraint syn_preserve=1 on the instance.
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":157:0:157:5|Register bit rd_flag is always 1.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":157:0:157:5|Pruning unused register dq_out. Make sure that there are no unused intermediate registers.
Finished optimization stage 1 on DS18B20 (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\speed_select_Tx.v":17:7:17:21|Synthesizing module speed_select_Tx in library work.

	SYS_FREQ=32'b00000111001001110000111000000000
	UART_BAUD=32'b00000000000000011100001000000000
	BPS_PARA=32'b00000000000000000000010000010001
	BPS_PARA_2=32'b00000000000000000000001000001000
   Generated name = speed_select_Tx_120000000s_115200s_1041s_520s
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\speed_select_Tx.v":34:16:34:24|Object uart_ctrl is declared but not assigned. Either assign a value or remove the declaration.
Running optimization stage 1 on speed_select_Tx_120000000s_115200s_1041s_520s .......
Finished optimization stage 1 on speed_select_Tx_120000000s_115200s_1041s_520s (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":16:7:16:13|Synthesizing module uart_tx in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":53:12:53:16|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":54:13:54:19|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":55:17:55:27|Removing redundant assignment.
Running optimization stage 1 on uart_tx .......
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\uart_tx.v":69:0:69:5|Pruning unused register odd_bit. Make sure that there are no unused intermediate registers.
Finished optimization stage 1 on uart_tx (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":15:7:15:15|Synthesizing module Ctrl_Data in library work.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":64:53:64:63|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":69:57:69:69|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":85:14:85:21|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":117:24:117:33|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":129:22:129:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":141:22:141:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":153:22:153:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":165:22:165:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":177:22:177:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":189:22:189:31|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":201:22:201:31|Removing redundant assignment.
Running optimization stage 1 on Ctrl_Data .......
Finished optimization stage 1 on Ctrl_Data (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\UART_Control.v":16:7:16:18|Synthesizing module UART_Control in library work.

	iWID_RS422=32'b00000000000000000000000000100000
	RX_SYS_FREQ=32'b00000111001001110000111000000000
	RX_UART_BAUD=32'b00000000000000011100001000000000
	TX_SYS_FREQ=32'b00000111001001110000111000000000
	TX_UART_BAUD=32'b00000000000000011100001000000000
   Generated name = UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1
@W: CG360 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\UART_Control.v":31:16:31:22|Removing wire rx_data, as there is no assignment to it.
Running optimization stage 1 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 .......
@W: CL318 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\UART_Control.v":31:16:31:22|*Output rx_data has undriven bits; assigning undriven bits to 'Z'.  Simulation mismatch possible. Assign all bits of the output.
Finished optimization stage 1 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalGenerator.v":42:7:42:21|Synthesizing module SignalGenerator in library work.

	iWID_TRANS=32'b00000000000000000000000000001101
	iTRANSIT_TIME=32'b00000000000000000000000010110110
	iAD_VALID_START=32'b00000000000000000000000001011010
	iDA=32'b00000000000000000000000000000011
	iAD_VALID_END=32'b00000000000000000000000010100110
	iDEMODU=32'b00000000000000000000000010101001
	iINTEGRATE=32'b00000000000000000000000010101101
	iMODU=32'b00000000000000000000000010110000
   Generated name = SignalGenerator_13s_182s_90s_3s_166s_169s_173s_176s
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalGenerator.v":80:15:80:25|Removing redundant assignment.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalGenerator.v":56:16:56:27|Object output_drive is declared but not assigned. Either assign a value or remove the declaration.
Running optimization stage 1 on SignalGenerator_13s_182s_90s_3s_166s_169s_173s_176s .......
Finished optimization stage 1 on SignalGenerator_13s_182s_90s_3s_166s_169s_173s_176s (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":25:7:25:10|Synthesizing module AND2 in library work.
Running optimization stage 1 on AND2 .......
Finished optimization stage 1 on AND2 (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":367:7:367:9|Synthesizing module INV in library work.
Running optimization stage 1 on INV .......
Finished optimization stage 1 on INV (CPU Time 0h:00m:00s, Memory Used current: 112MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":810:7:810:10|Synthesizing module XOR2 in library work.
Running optimization stage 1 on XOR2 .......
Finished optimization stage 1 on XOR2 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":710:7:710:14|Synthesizing module ROM16X1A in library work.
Running optimization stage 1 on ROM16X1A .......
Finished optimization stage 1 on ROM16X1A (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":959:7:959:14|Synthesizing module PDPW16KD in library work.
Running optimization stage 1 on PDPW16KD .......
Finished optimization stage 1 on PDPW16KD (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":119:7:119:13|Synthesizing module FD1P3DX in library work.
Running optimization stage 1 on FD1P3DX .......
Finished optimization stage 1 on FD1P3DX (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":160:7:160:13|Synthesizing module FD1S3BX in library work.
Running optimization stage 1 on FD1S3BX .......
Finished optimization stage 1 on FD1S3BX (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":168:7:168:13|Synthesizing module FD1S3DX in library work.
Running optimization stage 1 on FD1S3DX .......
Finished optimization stage 1 on FD1S3DX (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Software\lscc\diamond\3.12\synpbase\lib\lucent\ecp5u.v":76:7:76:11|Synthesizing module CCU2C in library work.
Running optimization stage 1 on CCU2C .......
Finished optimization stage 1 on CCU2C (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":8:7:8:20|Synthesizing module Asys_fifo56X16 in library work.
Running optimization stage 1 on Asys_fifo56X16 .......
@W: CL168 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":177:8:177:12|Removing instance INV_1 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
@W: CL168 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":171:8:171:12|Removing instance INV_4 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
@W: CL168 :"D:\Project\INS350_5J_JZ _copy\IP_al\global_clock1\Asys_fifo56X16\Asys_fifo56X16.v":169:9:169:15|Removing instance AND2_t0 because it does not drive other instances. To preserve this instance, use the syn_noprune synthesis directive.
Finished optimization stage 1 on Asys_fifo56X16 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 113MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":42:7:42:18|Synthesizing module Demodulation in library work.

	acum_cnt=32'b00000000000000000000000000010000
	iWID_IN=32'b00000000000000000000000000001100
	iWID_OUT=32'b00000000000000000000000000111000
   Generated name = Demodulation_16s_12s_56s
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":101:17:101:27|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":118:16:118:25|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":130:46:130:61|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":159:59:159:72|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":171:13:171:20|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":175:12:175:19|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":176:16:176:27|Removing redundant assignment.
Running optimization stage 1 on Demodulation_16s_12s_56s .......
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":158:0:158:5|Pruning unused register sample_sum_DY2[55:0]. Make sure that there are no unused intermediate registers.
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Demodulation.v":139:0:139:5|Pruning unused register Read_enDY[1:0]. Make sure that there are no unused intermediate registers.
Finished optimization stage 1 on Demodulation_16s_12s_56s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Integration.v":42:7:42:17|Synthesizing module Integration in library work.

	iWID_IN=32'b00000000000000000000000000111000
	iWID_OUT=32'b00000000000000000000000000111000
   Generated name = Integration_56s_56s
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Integration.v":71:11:71:17|Removing redundant assignment.
Running optimization stage 1 on Integration_56s_56s .......
Finished optimization stage 1 on Integration_56s_56s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":42:7:42:16|Synthesizing module Modulation in library work.

	iWID_TRANS=32'b00000000000000000000000000001101
	iWID_IN=32'b00000000000000000000000000111000
	iWID_OUT=32'b00000000000000000000000000001110
	DA_CONSTANT=32'b00000000000000000011101000110100
	iFEEDBACK_SCALE=32'b00000000000000000000000000001010
	wCLOSED=1'b1
	pi_1_2=14'b00111111111111
	c_step=14'b00110110101100
   Generated name = Modulation_13s_56s_14s_14900s_10s_1_4095_3500
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":103:10:103:15|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":117:9:117:13|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":131:11:131:17|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":138:13:138:21|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":150:11:150:17|Removing redundant assignment.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":64:19:64:22|Object step is declared but not assigned. Either assign a value or remove the declaration.
@W: CG133 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":67:20:67:28|Object stair_dy1 is declared but not assigned. Either assign a value or remove the declaration.
Running optimization stage 1 on Modulation_13s_56s_14s_14900s_10s_1_4095_3500 .......
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":127:0:127:5|Pruning unused register c_stair[13:0]. Make sure that there are no unused intermediate registers.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":142:0:142:5|Pruning unused bits 13 to 0 of DADY_dout[27:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":134:0:134:5|Pruning unused bits 13 to 0 of dout_mult[27:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
Finished optimization stage 1 on Modulation_13s_56s_14s_14900s_10s_1_4095_3500 (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":43:7:43:17|Synthesizing module Rs422Output in library work.

	bPOLAR=32'b00000000000000000000000000000000
	iWID_IN=32'b00000000000000000000000000111000
	iWID_OUT=32'b00000000000000000000000000100000
	iDELAYED=32'b00000000000000000000000001111000
	iOUTPUT_SCALE=32'b00000000000000000000010101000110
	idle_s=4'b0000
	wait_1us_s=4'b0001
	dalay_state=4'b0010
	check_data_stable_s=4'b0011
	transmit_data_s=4'b0100
	clear_data_s=4'b0101
   Generated name = Rs422Output_Z2
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":180:14:180:21|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":190:9:190:11|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":195:38:195:43|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":204:11:204:15|Removing redundant assignment.
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":221:13:221:19|Removing redundant assignment.
Running optimization stage 1 on Rs422Output_Z2 .......
@W: CL169 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":194:0:194:5|Pruning unused register sum_dy[55:0]. Make sure that there are no unused intermediate registers.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":208:0:208:5|Pruning unused bits 79 to 56 of p_sum_dy[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":208:0:208:5|Pruning unused bits 23 to 0 of p_sum_dy[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":198:0:198:5|Pruning unused bits 79 to 56 of p_sum[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL271 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":198:0:198:5|Pruning unused bits 23 to 0 of p_sum[79:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
Finished optimization stage 1 on Rs422Output_Z2 (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SquareWaveGenerator.v":16:7:16:25|Synthesizing module SquareWaveGenerator in library work.

	iTRANSMIT_COFF=32'b00000000000010010010011111000000
   Generated name = SquareWaveGenerator_600000s
@N: CG179 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SquareWaveGenerator.v":46:11:46:17|Removing redundant assignment.
Running optimization stage 1 on SquareWaveGenerator_600000s .......
Finished optimization stage 1 on SquareWaveGenerator_600000s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalProcessing.v":41:7:41:22|Synthesizing module SignalProcessing in library work.

	acum_cnt=32'b00000000000000000000000000010000
	iWID_TRANS=32'b00000000000000000000000000001101
	iWID_AD=32'b00000000000000000000000000001100
	iWID_DA=32'b00000000000000000000000000001110
	iWID_RS422=32'b00000000000000000000000000100000
	iWID_SIGN=32'b00000000000000000000000000000101
	wCLOSED=1'b1
	iTRANSIT_TIME=32'b00000000000000000000000010110110
	iAD_VALID_START=32'b00000000000000000000000001011010
	iFEEDBACK_SCALE=32'b00000000000000000000000000001010
	bPOLAR=32'b00000000000000000000000000000000
	iOUTPUT_SCALE=32'b00000000000000000000010101000110
	iDELAYED=32'b00000000000000000000000001111000
	DA_CONSTANT=32'b00000000000000000011101000110100
	iTRANSMIT_COFF=32'b00000000000010010010011111000000
	iWID_PROC=32'b00000000000000000000000000111000
   Generated name = SignalProcessing_Z3
Running optimization stage 1 on SignalProcessing_Z3 .......
Finished optimization stage 1 on SignalProcessing_Z3 (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
@N: CG364 :"D:\Project\INS350_5J_JZ _copy\Src_al\INS350_5J_JZ.v":17:7:17:18|Synthesizing module INS350_5J_JZ in library work.
@W: CG360 :"D:\Project\INS350_5J_JZ _copy\Src_al\INS350_5J_JZ.v":54:9:54:19|Removing wire TxTransmitt, as there is no assignment to it.
@W: CG360 :"D:\Project\INS350_5J_JZ _copy\Src_al\INS350_5J_JZ.v":61:10:61:18|Removing wire CLKFX_OUT, as there is no assignment to it.
Running optimization stage 1 on INS350_5J_JZ .......
Finished optimization stage 1 on INS350_5J_JZ (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
Running optimization stage 2 on INS350_5J_JZ .......
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\INS350_5J_JZ.v":22:9:22:18|Input RxTransmit is unused.
Finished optimization stage 2 on INS350_5J_JZ (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 119MB)
Running optimization stage 2 on SignalProcessing_Z3 .......
Finished optimization stage 2 on SignalProcessing_Z3 (CPU Time 0h:00m:00s, Memory Used current: 113MB peak: 119MB)
Running optimization stage 2 on SquareWaveGenerator_600000s .......
Finished optimization stage 2 on SquareWaveGenerator_600000s (CPU Time 0h:00m:00s, Memory Used current: 114MB peak: 119MB)
Running optimization stage 2 on Rs422Output_Z2 .......
@N: CL201 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":93:0:93:5|Trying to extract state machine for register trans_state.
Extracted state machine for register trans_state
State machine has 6 reachable states with original encodings of:
   0000
   0001
   0010
   0011
   0100
   0101
@W: CL247 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Rs422Output.v":57:22:57:24|Input port bit 55 of din[55:0] is unused

Finished optimization stage 2 on Rs422Output_Z2 (CPU Time 0h:00m:00s, Memory Used current: 115MB peak: 119MB)
Running optimization stage 2 on Modulation_13s_56s_14s_14900s_10s_1_4095_3500 .......
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":96:0:96:5|Register bit square[12] is always 0.
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":96:0:96:5|Register bit square[13] is always 0.
@W: CL279 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":96:0:96:5|Pruning register bits 13 to 12 of square[13:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":107:0:107:5|Register bit square_dy[13] is always 0.
@N: CL189 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":107:0:107:5|Register bit square_dy[12] is always 0.
@W: CL279 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":107:0:107:5|Pruning register bits 13 to 12 of square_dy[13:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL279 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":96:0:96:5|Pruning register bits 11 to 1 of square[11:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL279 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":107:0:107:5|Pruning register bits 11 to 1 of square_dy[11:0]. If this is not the intended behavior, drive the inputs with valid values, or inputs from the top level.
@W: CL246 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":56:28:56:30|Input port bits 55 to 24 of din[55:0] are unused. Assign logic for all port bits or change the input port size.
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Modulation.v":52:12:52:16|Input rst_n is unused.
Finished optimization stage 2 on Modulation_13s_56s_14s_14900s_10s_1_4095_3500 (CPU Time 0h:00m:00s, Memory Used current: 115MB peak: 119MB)
Running optimization stage 2 on Integration_56s_56s .......
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Integration.v":48:14:48:18|Input rst_n is unused.
Finished optimization stage 2 on Integration_56s_56s (CPU Time 0h:00m:00s, Memory Used current: 116MB peak: 119MB)
Running optimization stage 2 on Demodulation_16s_12s_56s .......
Finished optimization stage 2 on Demodulation_16s_12s_56s (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on Asys_fifo56X16 .......
Finished optimization stage 2 on Asys_fifo56X16 (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on CCU2C .......
Finished optimization stage 2 on CCU2C (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on FD1S3DX .......
Finished optimization stage 2 on FD1S3DX (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on FD1S3BX .......
Finished optimization stage 2 on FD1S3BX (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on FD1P3DX .......
Finished optimization stage 2 on FD1P3DX (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on PDPW16KD .......
Finished optimization stage 2 on PDPW16KD (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on ROM16X1A .......
Finished optimization stage 2 on ROM16X1A (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on XOR2 .......
Finished optimization stage 2 on XOR2 (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on INV .......
Finished optimization stage 2 on INV (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on AND2 .......
Finished optimization stage 2 on AND2 (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on SignalGenerator_13s_182s_90s_3s_166s_169s_173s_176s .......
@A: CL153 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\SignalGenerator.v":56:16:56:27|*Unassigned bits of output_drive are referenced and tied to 0 -- simulation mismatch possible.
Finished optimization stage 2 on SignalGenerator_13s_182s_90s_3s_166s_169s_173s_176s (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 .......
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\UART_Control.v":33:11:33:13|Input RXD is unused.
Finished optimization stage 2 on UART_Control_32s_120000000s_115200s_120000000s_115200s_Z1 (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on Ctrl_Data .......
@N: CL201 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":89:0:89:5|Trying to extract state machine for register tx_state.
Extracted state machine for register tx_state
State machine has 11 reachable states with original encodings of:
   0000
   0001
   0010
   0011
   0100
   0101
   0110
   0111
   1000
   1001
   1010
@N: CL159 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\Ctrl_Data.v":22:15:22:21|Input rd_done is unused.
Finished optimization stage 2 on Ctrl_Data (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on uart_tx .......
Finished optimization stage 2 on uart_tx (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on speed_select_Tx_120000000s_115200s_1041s_520s .......
Finished optimization stage 2 on speed_select_Tx_120000000s_115200s_1041s_520s (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on DS18B20 .......
@N: CL201 :"D:\Project\INS350_5J_JZ _copy\Src_al\Src_al\DS18B20.v":93:0:93:5|Trying to extract state machine for register cur_state.
Extracted state machine for register cur_state
State machine has 6 reachable states with original encodings of:
   000001
   000010
   000100
   001000
   010000
   100000
Finished optimization stage 2 on DS18B20 (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on global_clock .......
Finished optimization stage 2 on global_clock (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on EHXPLLL .......
Finished optimization stage 2 on EHXPLLL (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on VLO .......
Finished optimization stage 2 on VLO (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)
Running optimization stage 2 on VHI .......
Finished optimization stage 2 on VHI (CPU Time 0h:00m:00s, Memory Used current: 117MB peak: 119MB)

For a summary of runtime and memory usage per design unit, please see file:
==========================================================
@L: D:\Project\INS350_5J_JZ _copy\impl1\synwork\layer0.rt.csv

